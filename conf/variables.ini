# 占位符使用时用${}包起来。e.g ${hologres.endpoint}
# hologres master集群
hologres.endpoint=hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com
hologres.port=80
hologres.username=LTAI5t5ta8eK5UciPg6fM45e
hologres.password=******************************

# 自建ES集群
es.workbench.hosts=http://************:9200;http://*************:9200
es.workbench.username=workbench_app
es.workbench.password=RI_WLDQktvrViCzGsopAF

# 在线数据引擎ES集群
es.dataengine.hosts=http://************:9200
es.dataengine.username=dataengine_app
es.dataengine.password=bsKEgHOoikPzmtRJSMV_L

# 沟通记录库集群
es.fwyywechatmessagedb.hosts=http://*************:9200;http://*************:9200
es.fwyywechatmessagedb.username=fwyywechatmessagedb_app
es.fwyywechatmessagedb.password=UJlKzbZdMBsqFikeIgxoO

# kafka相关
kafka.consumer.groupid=zlink.fwyy-dataengine
# 注意binlog存储位置
kafka.bootstrap-servers.binlog.tx=*************:9092
kafka.bootstrap-servers.binlog.ali=alikafka-pre-cn-tl32lwudm00k-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lwudm00k-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lwudm00k-3-vpc.alikafka.aliyuncs.com:9092