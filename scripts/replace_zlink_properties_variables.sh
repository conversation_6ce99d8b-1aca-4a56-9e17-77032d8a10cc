#!/bin/bash
readonly ROOT_DIR=$(dirname $(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd))
readonly VARIABLES_CONF=$ROOT_DIR/conf/variables.ini

if [ ! -f $VARIABLES_CONF ];then
    exit
fi

command=""
# https://stackoverflow.com/questions/12916352/shell-script-read-missing-last-line
while read line || [[ -n $line ]]
do
    # 跳过注释行，#开头
    if [[ $line =~ ^#.* ]];then
        continue
    fi

    k=$(echo $line | cut -d'=' -f 1)
    v=$(echo $line | cut -d'=' -f 2)

    if [ -z "$command" ];then
        command="s#\${${k}}#${v}#g"
    else
        command=${command}"; s#\${${k}}#${v}#g"
    fi
done < $VARIABLES_CONF

if [ ! -z "$command" ];then
    echo "替换变量"
    # https://stackoverflow.com/questions/394230/how-to-detect-the-os-from-a-bash-script
    if [[ "$OSTYPE" == "darwin"* ]]; then
        find $ROOT_DIR/output/conf -type f -name "*.properties" | xargs sed -i "" "$command"
    else
        find $ROOT_DIR/output/conf -type f -name "*.properties" | xargs sed -i "$command"
    fi
    if [ $? -eq 0 ];then
        echo "替换完成"
    fi
fi
