INSERT INTO zyb_zbk_bzr_ads.ads_zbk_tbl_assignclass_coursebind_studentcount (
   "all_cnt",
   "assistant_uid",
   "continue_cnt",
   "continue_error_cnt",
   "course_grade",
   "course_id",
   "course_is_inner",
   "course_subject",
   "course_type",
   "delay_transfer_new_cnt",
   "delay_transfer_old_cnt",
   "deleted",
   "new_cnt",
   "no_transfer_new_cnt",
   "no_transfer_old_cnt",
   "nomal_transfer_new_cnt",
   "normal_transfer_old_cnt",
   "old_cnt",
   "person_uid",
   "pull_new_duty",
   "season",
   "season_num",
   "season_year",
   "series_new_cnt",
   "single_new_cnt",
   "zbk_update_time"
) SELECT
   "all_cnt",
   "assistant_uid",
   "continue_cnt",
   "continue_error_cnt",
   COALESCE("course_grade", 0) AS "course_grade",
   "course_id",
   COALESCE("course_is_inner", 0) AS "course_is_inner",
   COALESCE("course_subject", 0) AS "course_subject",
   COALESCE("course_type", 0) AS "course_type",
   "delay_transfer_new_cnt",
   "delay_transfer_old_cnt",
   COALESCE("deleted", 0) AS "deleted",
   "new_cnt",
   "no_transfer_new_cnt",
   "no_transfer_old_cnt",
   "nomal_transfer_new_cnt",
   "normal_transfer_old_cnt",
   "old_cnt",
   COALESCE("person_uid", 0) AS "person_uid",
   "pull_new_duty",
   COALESCE("season", 0) AS "season",
   COALESCE("season_num", 0) AS "season_num",
   COALESCE("season_year", 0) AS "season_year",
   "series_new_cnt",
   "single_new_cnt",
   "zbk_update_time"
FROM zyb_zbk_bzr_ads_base.ads_zbk_tbl_assignclass_coursebind_studentcount
