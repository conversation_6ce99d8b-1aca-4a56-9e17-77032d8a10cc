EXPLAIN INSERT INTO zyb_zbk_bzr_ads.ads_zbk_idl_trade_order_assistant_v1 (
   "is_transfer_student_typeB",
   "assistant_uid",
   "change_from",
   "change_status",
   "change_time",
   "change_to",
   "course_id",
   "create_time",
   "current_sale_trans_pv",
   "current_trans_pv",
   "enroll_deer_programming_last_time",
   "enroll_deer_writing_last_time",
   "is_bound_discount",
   "is_bound_discount_retain",
   "is_delete",
   "is_l1r",
   "is_l2_once",
   "is_l2r",
   "is_l2r_bound_space_season",
   "is_l2r_space_season",
   "is_new",
   "is_refund_pre",
   "is_refund_start",
   "is_retain",
   "is_trans",
   "is_trans_once",
   "is_trans_once_time",
   "is_transfer",
   "is_transfer_student",
   "iss_deer_programming",
   "iss_deer_writing",
   "last_refund_l2_time",
   "learn_season",
   "new_user_type",
   "next_sale_trans_pv",
   "next_trans_pv",
   "other_trans_pv",
   "pre_retain_detail",
   "pre_status",
   "pre_status_autume_spring",
   "pre_time",
   "province_name",
   "refund_pre_time",
   "refund_time",
   "reserve_infos",
   "retain_detail",
   "sku_id",
   "space_season_bound_detail",
   "space_season_retain_detail",
   "status",
   "student_uid",
   "sub_trade_id",
   "trade_id",
   "trans_detail",
   "transfer_available_subject",
   "transfer_bound_subtrade_id_count",
   "transfer_subtrade_id_count",
   "update_time",
   "year",
   "year_season"
) SELECT
   "is_transfer_student_typeB",
   "assistant_uid",
   "change_from",
   "change_status",
   "change_time",
   "change_to",
   "course_id",
   "create_time",
   "current_sale_trans_pv",
   "current_trans_pv",
   "enroll_deer_programming_last_time",
   "enroll_deer_writing_last_time",
   "is_bound_discount",
   "is_bound_discount_retain",
   "is_delete",
   "is_l1r",
   "is_l2_once",
   "is_l2r",
   "is_l2r_bound_space_season",
   "is_l2r_space_season",
   "is_new",
   "is_refund_pre",
   "is_refund_start",
   "is_retain",
   "is_trans",
   "is_trans_once",
   "is_trans_once_time",
   "is_transfer",
   "is_transfer_student",
   "iss_deer_programming",
   "iss_deer_writing",
   "last_refund_l2_time",
   "learn_season",
   "new_user_type",
   "next_sale_trans_pv",
   "next_trans_pv",
   "other_trans_pv",
   "pre_retain_detail",
   "pre_status",
   "pre_status_autume_spring",
   "pre_time",
   "province_name",
   "refund_pre_time",
   "refund_time",
   "reserve_infos",
   "retain_detail",
   "sku_id",
   "space_season_bound_detail",
   "space_season_retain_detail",
   "status",
   "student_uid",
   "sub_trade_id",
   "trade_id",
   "trans_detail",
   "transfer_available_subject",
   "transfer_bound_subtrade_id_count",
   "transfer_subtrade_id_count",
   "update_time",
   "year",
   "year_season"
FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_assistant_v1
