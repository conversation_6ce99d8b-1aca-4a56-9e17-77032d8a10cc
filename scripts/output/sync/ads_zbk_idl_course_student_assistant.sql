INSERT INTO zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1 (
   "is_transfer_student_typeB",
   "after_test_participate_num",
   "after_test_right_num",
   "assistant_uid",
   "attend_count",
   "attend_count_5m",
   "attend_count_quarter",
   "attend_type_label",
   "course_id",
   "course_name",
   "course_type",
   "department",
   "effective_wechat_num",
   "exam32_total_submit_num",
   "finish_num",
   "has_auto_enterprise_wechat",
   "has_backinterview",
   "has_backinterview_tag",
   "has_bangbang_tag",
   "has_interview",
   "has_manual_enterprise_wechat",
   "has_manual_wechat",
   "has_retain_tag",
   "has_tag",
   "has_wechat",
   "homework_submit_count",
   "interview_type",
   "is_after_class_phone_back_interview",
   "is_after_class_phone_back_interview_1min",
   "is_backinterview_by_phone",
   "is_interview_by_phone",
   "is_interview_call_3min",
   "is_interview_finish",
   "is_phone_access",
   "is_phone_cover",
   "is_refund",
   "is_test_complete",
   "is_transfer_student",
   "is_transfer_student_delayed",
   "is_wechat_bind",
   "last_backinterview_time",
   "m_grade",
   "m_subject",
   "new_user_type",
   "phone_backinterview_count",
   "playback_cnt_attend_after_unlock_7d",
   "playback_cnt_finish_after_unlock_7d",
   "pre_continue",
   "pre_finish_num",
   "refund_time",
   "service_survey_detail",
   "start_time",
   "stop_time",
   "student_uid",
   "test_complete_time",
   "trade_id",
   "trade_time",
   "view_count_5m",
   "wechat_cnt",
   "wechat_reply_cnt",
   "wechat_time",
   "zbk_update_time"
) SELECT
   "is_transfer_student_typeB",
   "after_test_participate_num",
   "after_test_right_num",
   COALESCE("assistant_uid", 0) AS "assistant_uid",
   "attend_count",
   "attend_count_5m",
   "attend_count_quarter",
   "attend_type_label",
   "course_id",
   "course_name",
   "course_type",
   "department",
   "effective_wechat_num",
   "exam32_total_submit_num",
   "finish_num",
   "has_auto_enterprise_wechat",
   "has_backinterview",
   "has_backinterview_tag",
   "has_bangbang_tag",
   "has_interview",
   "has_manual_enterprise_wechat",
   "has_manual_wechat",
   "has_retain_tag",
   "has_tag",
   "has_wechat",
   "homework_submit_count",
   "interview_type",
   "is_after_class_phone_back_interview",
   "is_after_class_phone_back_interview_1min",
   "is_backinterview_by_phone",
   "is_interview_by_phone",
   "is_interview_call_3min",
   "is_interview_finish",
   "is_phone_access",
   "is_phone_cover",
   "is_refund",
   "is_test_complete",
   "is_transfer_student",
   "is_transfer_student_delayed",
   "is_wechat_bind",
   "last_backinterview_time",
   "m_grade",
   "m_subject",
   "new_user_type",
   "phone_backinterview_count",
   "playback_cnt_attend_after_unlock_7d",
   "playback_cnt_finish_after_unlock_7d",
   "pre_continue",
   "pre_finish_num",
   "refund_time",
   "service_survey_detail",
   "start_time",
   "stop_time",
   "student_uid",
   "test_complete_time",
   "trade_id",
   "trade_time",
   "view_count_5m",
   "wechat_cnt",
   "wechat_reply_cnt",
   "wechat_time",
   "zbk_update_time"
FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_course_student_assistant
