INSERT INTO zyb_zbk_bzr_ads.ads_zbk_tbldatasopcal (
   "homework_SorA_num",
   "after_unlock_7d_attend_playback_num",
   "after_unlock_7d_finish_playback_num",
   "after_unlock_attend_playback_num",
   "after_unlock_finish_playback_num",
   "after_unlock_need_attend_num",
   "after_unlock_playback_num",
   "ai_attend_num",
   "ai_attend_total",
   "ai_class_finish_num",
   "all_time_homework_submit_num",
   "all_view_num",
   "assistant_uid",
   "assistantcourse_attend_finish_num",
   "assistantcourse_attend_num",
   "assistantcourse_need_attend_num",
   "attend_num",
   "attend_num_5m",
   "attend_num_quarter",
   "attend_one_third_num",
   "attend_total",
   "attend_two_third_num",
   "challenge_attend_right_num",
   "challenge_attend_total_num",
   "challenge_finish_num",
   "challenge_total_num",
   "chat_num",
   "class_finish_num",
   "corrections_homework_num48",
   "corrections_homework_num72",
   "course_id",
   "exam33_similar_question_amend_num",
   "exam33_similar_question_submit_num",
   "exam33_submit_and_unamend_num",
   "exam33_wrong_similar_expound_video_view_num",
   "exam7_submit_and_unamend_num",
   "exam7_wrong_expound_video_view_num",
   "homework_amend_num",
   "homework_amend_num_in_14d",
   "homework_num",
   "homework_recorrection_num",
   "homework_submit_3_times_or_amend_num",
   "homework_tid_first_correct_cnt",
   "homework_tid_first_right_cnt",
   "interactive_participate_num",
   "interactive_right_num",
   "interactive_total_num",
   "is_follow",
   "is_follow_a",
   "lesson_finish_num",
   "lesson_id",
   "lesson_test_finish_num",
   "lesson_test_participate_num",
   "lesson_test_right_num",
   "lesson_test_total_num",
   "lesson_total_num",
   "niudao_finish_num",
   "niudao_total_num",
   "playback_all_num",
   "playback_participate_num",
   "playback_right_num",
   "postclass_attend_num",
   "postclass_finish_attend_num",
   "pre_attend_is5_num",
   "pre_attend_num",
   "preclass_attend_num",
   "preclass_finish_attend_num",
   "preview_bef_fin_num",
   "preview_finish_num",
   "reg_num",
   "save_time",
   "stage_test_finish_num",
   "transfer_status",
   "user_type",
   "view_finish_num",
   "view_finish_num_in_14d",
   "view_num",
   "view_num_5m",
   "view_num_quarter",
   "zbk_update_time"
) SELECT
   "homework_SorA_num",
   "after_unlock_7d_attend_playback_num",
   "after_unlock_7d_finish_playback_num",
   "after_unlock_attend_playback_num",
   "after_unlock_finish_playback_num",
   "after_unlock_need_attend_num",
   "after_unlock_playback_num",
   "ai_attend_num",
   "ai_attend_total",
   "ai_class_finish_num",
   "all_time_homework_submit_num",
   "all_view_num",
   "assistant_uid",
   "assistantcourse_attend_finish_num",
   "assistantcourse_attend_num",
   "assistantcourse_need_attend_num",
   "attend_num",
   "attend_num_5m",
   "attend_num_quarter",
   "attend_one_third_num",
   "attend_total",
   "attend_two_third_num",
   "challenge_attend_right_num",
   "challenge_attend_total_num",
   "challenge_finish_num",
   "challenge_total_num",
   "chat_num",
   "class_finish_num",
   "corrections_homework_num48",
   "corrections_homework_num72",
   COALESCE("course_id", 0) AS "course_id",
   "exam33_similar_question_amend_num",
   "exam33_similar_question_submit_num",
   "exam33_submit_and_unamend_num",
   "exam33_wrong_similar_expound_video_view_num",
   "exam7_submit_and_unamend_num",
   "exam7_wrong_expound_video_view_num",
   "homework_amend_num",
   "homework_amend_num_in_14d",
   "homework_num",
   "homework_recorrection_num",
   "homework_submit_3_times_or_amend_num",
   "homework_tid_first_correct_cnt",
   "homework_tid_first_right_cnt",
   "interactive_participate_num",
   "interactive_right_num",
   "interactive_total_num",
   "is_follow",
   "is_follow_a",
   "lesson_finish_num",
   "lesson_id",
   "lesson_test_finish_num",
   "lesson_test_participate_num",
   "lesson_test_right_num",
   "lesson_test_total_num",
   "lesson_total_num",
   "niudao_finish_num",
   "niudao_total_num",
   "playback_all_num",
   "playback_participate_num",
   "playback_right_num",
   "postclass_attend_num",
   "postclass_finish_attend_num",
   "pre_attend_is5_num",
   "pre_attend_num",
   "preclass_attend_num",
   "preclass_finish_attend_num",
   "preview_bef_fin_num",
   "preview_finish_num",
   "reg_num",
   "save_time",
   "stage_test_finish_num",
   "transfer_status",
   "user_type",
   "view_finish_num",
   "view_finish_num_in_14d",
   "view_num",
   "view_num_5m",
   "view_num_quarter",
   "zbk_update_time"
FROM zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopcal
