EXPLAIN INSERT INTO zyb_zbk_bzr_ads.ads_zbk_adl_referral_activity_junior_assistant_index (
   "assistant_uid",
   "config_id",
   "dt",
   "invitee_uid_bind_pv",
   "invitee_uid_uv",
   "inviter_uid_uv",
   "poster_uv",
   "season",
   "student_uid_uv",
   "year",
   "zbk_update_time"
) SELECT
   "assistant_uid",
   "config_id",
   "dt",
   "invitee_uid_bind_pv",
   "invitee_uid_uv",
   "inviter_uid_uv",
   "poster_uv",
   "season",
   "student_uid_uv",
   "year",
   "zbk_update_time"
FROM zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_junior_assistant_index
