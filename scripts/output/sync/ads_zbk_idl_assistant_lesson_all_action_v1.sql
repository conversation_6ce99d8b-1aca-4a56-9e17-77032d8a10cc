INSERT INTO zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_all_action_v1 (
   "all_index",
   "assistant_first_access_d_value",
   "assistant_first_access_time",
   "assistant_last_leave_time",
   "assistant_postclass_live_duration",
   "assistant_preclass_live_duration",
   "assistant_uid",
   "brand_id",
   "course_assistant_bind_detail",
   "course_id",
   "course_name",
   "course_type",
   "courseware_upload_time",
   "deleted",
   "grade_period",
   "is_assistant_postclass_delay",
   "is_assistant_preclass_late",
   "is_assistant_preclass_redgift",
   "is_courseware_upload_timeout",
   "is_inner",
   "is_main",
   "learn_season",
   "lesson_assistant_status",
   "lesson_deleted",
   "lesson_id",
   "lesson_name",
   "lesson_start_time",
   "lesson_stop_time",
   "main_grade",
   "main_index",
   "main_subject",
   "restart_id",
   "upload_status",
   "year",
   "zbk_update_time"
) SELECT
   "all_index",
   "assistant_first_access_d_value",
   "assistant_first_access_time",
   "assistant_last_leave_time",
   "assistant_postclass_live_duration",
   "assistant_preclass_live_duration",
   "assistant_uid",
   "brand_id",
   "course_assistant_bind_detail",
   "course_id",
   "course_name",
   "course_type",
   "courseware_upload_time",
   "deleted",
   "grade_period",
   "is_assistant_postclass_delay",
   COALESCE("is_assistant_preclass_late", 0) AS "is_assistant_preclass_late",
   "is_assistant_preclass_redgift",
   "is_courseware_upload_timeout",
   "is_inner",
   "is_main",
   "learn_season",
   "lesson_assistant_status",
   "lesson_deleted",
   "lesson_id",
   "lesson_name",
   COALESCE("lesson_start_time", 0) AS "lesson_start_time",
   "lesson_stop_time",
   "main_grade",
   "main_index",
   "main_subject",
   "restart_id",
   "upload_status",
   "year",
   "zbk_update_time"
FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_all_action_v1
