BEGIN;
CREATE TABLE zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1 (
    "assistant_uid" INT8 not null default 0,
    "attend_count" INT8,
    "attend_count_5m" INT8,
    "attend_count_quarter" INT8,
    "attend_type_label" TEXT,
    "call_list" TEXT,
    "course_id" INT8,
    "course_name" TEXT,
    "course_type" INT8,
    "department" INT8,
    "effective_wechat_num" INT8,
    "exam32_total_submit_num" INT8,
    "finish_num" INT8,
    "grade_period" INT8,
    "grades" TEXT,
    "has_auto_enterprise_wechat" INT8,
    "has_backinterview" INT8,
    "has_backinterview_tag" INT8,
    "has_bangbang_tag" INT8,
    "has_interview" INT8,
    "has_manual_enterprise_wechat" INT8,
    "has_manual_wechat" INT8,
    "has_retain_tag" INT8,
    "has_tag" INT8,
    "has_wechat" INT8,
    "homework_submit_count" INT8,
    "interview_type" INT8,
    "is_after_class_phone_back_interview" INT8,
    "is_after_class_phone_back_interview1" INT8,
    "is_after_class_phone_back_interview2" INT8,
    "is_after_class_phone_back_interview3" INT8,
    "is_after_class_phone_back_interview4" INT8,
    "is_backinterview_by_phone" INT8,
    "is_bangbang_wechat_bind" INT8,
    "is_device_debug" INT8,
    "is_full_attendance" INT8,
    "is_has_test" INT8,
    "is_huanxiong_wechat_bind" INT8,
    "is_interview_by_phone" INT8,
    "is_interview_by_wechat" INT8,
    "is_interview_call_3min" INT8,
    "is_interview_finish" INT8,
    "is_multiple_service" INT8,
    "is_phone_access" INT8,
    "is_phone_cover" INT8,
    "is_refund" INT8,
    "is_report" INT8,
    "is_report_read" INT8,
    "is_report_view" INT8,
    "is_studejt_info_complete" INT8,
    "is_test_complete" INT8,
    "is_test_expound" INT8,
    "is_trans" INT8,
    "is_transfer_student" INT8,
    "is_transfer_student_delayed" INT8,
    "is_transfer_student_typeB" INT8,
    "is_transfer_target" INT8,
    "is_wechat_bind" INT8,
    "last_backinterview_time" INT8,
    "m_grade" INT8,
    "m_subject" INT8,
    "new_user_type" TEXT,
    "phone_backinterview_count" INT8,
    "is_after_class_phone_back_interview_1min" INT4,
    "playback_cnt_attend_after_unlock_7d" INT8,
    "playback_cnt_finish_after_unlock_7d" INT8,
    "pre_continue" INT8,
    "pre_finish_num" INT8,
    "refund_time" INT8,
    "season_idx" INT8,
    "service_survey_detail" TEXT,
    "stage_test_submit_count" INT8,
    "start_time" INT8,
    "stop_time" INT8,
    "student_uid" INT8,
    "subjects" TEXT,
    "task_done_num" INT8,
    "test_complete_time" INT8,
    "trade_id" INT8,
    "trade_time" INT8,
    "transfer_available_subject" TEXT,
    "transfer_subtrade_id_num" INT8,
    "view_count_5m" INT8,
    "wechat_cnt" INT8,
    "wechat_reply_cnt" INT8,
    "wechat_time" INT8,
    "after_test_right_num" INT4,
    "after_test_participate_num" INT4,
    "zbk_update_time" INT4,

    PRIMARY KEY (course_id,student_uid)
);

COMMENT ON TABLE zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1 IS '班主任cu表';

-- 列存表
CALL SET_TABLE_PROPERTY('zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1', 'orientation', 'column');
CALL SET_TABLE_PROPERTY('zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1', 'table_group', 'zyb_zbk_bzr_tg_32');
CALL SET_TABLE_PROPERTY('zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1', 'distribution_key', 'course_id');
CALL SET_TABLE_PROPERTY('zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1', 'clustering_key', 'course_id,student_uid');
CALL SET_TABLE_PROPERTY('zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1', 'segment_key', 'student_uid,assistant_uid,course_id');
CALL SET_TABLE_PROPERTY('zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1', 'dictionary_encoding_columns', 'new_user_type');
CALL SET_TABLE_PROPERTY('zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1', 'bitmap_columns', 'student_uid,assistant_uid,is_refund,new_user_type');
-- 视图
CREATE VIEW zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1_view AS SELECT * FROM zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1;
COMMIT;
