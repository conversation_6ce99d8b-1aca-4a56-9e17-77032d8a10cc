INSERT INTO zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_student_action_v1 (
   "all_lesson_index",
   "assistant_uid",
   "attend",
   "attend_detail",
   "attend_duration",
   "attend_long",
   "attend_quarter",
   "boost_lesson_index",
   "chat_num",
   "course_id",
   "course_name",
   "course_type",
   "exam_answer",
   "hx_alltest_finishnum",
   "hx_pretest_finishnum",
   "inout_count",
   "interaction_answer_detail",
   "is_assistantcourse_attend",
   "is_assistantcourse_finish",
   "is_attend_finish",
   "is_lbp_attend",
   "is_lbp_attend_finish",
   "is_need_attend",
   "is_need_attend_after_unlock",
   "is_playback_finish_after_unlock",
   "is_playback_finish_after_unlock_7d",
   "is_playback_long_after_unlock",
   "is_playback_long_after_unlock_7d",
   "is_playback_quarter",
   "is_postclass_attend",
   "is_postclass_finish_attend",
   "is_preclass_attend",
   "is_preclass_finish_attend",
   "is_view_finish_in_14d",
   "is_view_finished",
   "last_playback_time",
   "lbp_attend_duration",
   "learn_season",
   "lesson_deleted",
   "lesson_id",
   "lesson_name",
   "lesson_start_time",
   "lesson_status",
   "lesson_stop_time",
   "lesson_type",
   "lesson_unlock_time",
   "main_department",
   "main_grade",
   "main_lesson_index",
   "main_subject",
   "new_user_type",
   "photo_assistant_uid",
   "playback_participate_num",
   "playback_right_num",
   "playback_time",
   "playback_time_after_unlock",
   "playback_time_after_unlock_7d",
   "playback_time_in_14d",
   "playback_time_in_7d",
   "postclass_attend_duration",
   "pre_attend",
   "preclass_attend_duration",
   "restart_id",
   "student_attend_label",
   "student_interaction_label",
   "student_uid",
   "sub_trade_id",
   "teacher_id",
   "trade_change_status",
   "trade_change_time",
   "trade_create_time",
   "trade_refund_time",
   "trade_status",
   "year",
   "zbk_update_time",
   "lbp_last_playtime",
   "lbp_play_detail",
   "lbp_online_duration"
) SELECT
   "all_lesson_index",
   COALESCE("assistant_uid", 0) AS "assistant_uid",
   "attend",
   "attend_detail",
   "attend_duration",
   "attend_long",
   "attend_quarter",
   "boost_lesson_index",
   "chat_num",
   COALESCE("course_id", 0) AS "course_id",
   "course_name",
   "course_type",
   "exam_answer",
   "hx_alltest_finishnum",
   "hx_pretest_finishnum",
   "inout_count",
   "interaction_answer_detail",
   "is_assistantcourse_attend",
   "is_assistantcourse_finish",
   "is_attend_finish",
   "is_lbp_attend",
   "is_lbp_attend_finish",
   "is_need_attend",
   "is_need_attend_after_unlock",
   "is_playback_finish_after_unlock",
   "is_playback_finish_after_unlock_7d",
   "is_playback_long_after_unlock",
   "is_playback_long_after_unlock_7d",
   "is_playback_quarter",
   "is_postclass_attend",
   "is_postclass_finish_attend",
   "is_preclass_attend",
   "is_preclass_finish_attend",
   "is_view_finish_in_14d",
   "is_view_finished",
   "last_playback_time",
   "lbp_attend_duration",
   "learn_season",
   "lesson_deleted",
   "lesson_id",
   "lesson_name",
   "lesson_start_time",
   "lesson_status",
   "lesson_stop_time",
   "lesson_type",
   "lesson_unlock_time",
   "main_department",
   "main_grade",
   "main_lesson_index",
   "main_subject",
   "new_user_type",
   "photo_assistant_uid",
   "playback_participate_num",
   "playback_right_num",
   "playback_time",
   "playback_time_after_unlock",
   "playback_time_after_unlock_7d",
   "playback_time_in_14d",
   "playback_time_in_7d",
   "postclass_attend_duration",
   "pre_attend",
   "preclass_attend_duration",
   "restart_id",
   "student_attend_label",
   "student_interaction_label",
   "student_uid",
   "sub_trade_id",
   "teacher_id",
   "trade_change_status",
   "trade_change_time",
   "trade_create_time",
   "trade_refund_time",
   "trade_status",
   "year",
   "zbk_update_time",
   "lbp_last_playtime",
   "lbp_play_detail",
   "lbp_online_duration"
FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_student_action_v1
WHERE "course_id" > 0
