# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_exam_relation_v1, \
             dest_zyb_zbk_bzr_ads_ads_zbk_bdl_exam_relation_v1, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_exam_relation_v1.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_exam_relation_v1.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_exam_relation_v1 ( \
        `bind_id` BIGINT, \
        `bind_status` BIGINT, \
        `bind_type` BIGINT, \
        `exam_id` BIGINT, \
        `exam_tag` BIGINT, \
        `exam_type` BIGINT, \
        `is_artificial_correct` BIGINT, \
        `question_detail_new` STRING, \
        `relation_type` BIGINT, \
        `total_num` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_bdl_exam_relation_v1', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_bdl_exam_relation_v1.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_bdl_exam_relation_v1 ( \
        `bind_id` BIGINT, \
        `bind_status` BIGINT, \
        `bind_type` BIGINT, \
        `exam_id` BIGINT, \
        `exam_tag` BIGINT, \
        `exam_type` BIGINT, \
        `is_artificial_correct` BIGINT, \
        `question_detail_new` STRING, \
        `relation_type` BIGINT, \
        `total_num` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_bdl_exam_relation_v1', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_bdl_exam_relation_v1 \
    SELECT \
        `bind_id`, \
        `bind_status`, \
        `bind_type`, \
        `exam_id`, \
        `exam_tag`, \
        `exam_type`, \
        `is_artificial_correct`, \
        `question_detail_new`, \
        `relation_type`, \
        `total_num`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_exam_relation_v1
