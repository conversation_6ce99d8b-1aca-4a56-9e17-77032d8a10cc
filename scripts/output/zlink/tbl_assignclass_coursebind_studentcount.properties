# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_tbl_assignclass_coursebind_studentcount, \
             dest_zyb_zbk_bzr_ads_ads_zbk_tbl_assignclass_coursebind_studentcount, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_tbl_assignclass_coursebind_studentcount.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_tbl_assignclass_coursebind_studentcount.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_tbl_assignclass_coursebind_studentcount ( \
        `all_cnt` BIGINT, \
        `assistant_uid` BIGINT, \
        `continue_cnt` BIGINT, \
        `continue_error_cnt` BIGINT, \
        `course_grade` BIGINT, \
        `course_id` BIGINT, \
        `course_is_inner` BIGINT, \
        `course_subject` BIGINT, \
        `course_type` BIGINT, \
        `delay_transfer_new_cnt` BIGINT, \
        `delay_transfer_old_cnt` BIGINT, \
        `deleted` BIGINT, \
        `new_cnt` BIGINT, \
        `no_transfer_new_cnt` BIGINT, \
        `no_transfer_old_cnt` BIGINT, \
        `nomal_transfer_new_cnt` BIGINT, \
        `normal_transfer_old_cnt` BIGINT, \
        `old_cnt` BIGINT, \
        `person_uid` BIGINT, \
        `pull_new_duty` BIGINT, \
        `season` BIGINT, \
        `season_num` BIGINT, \
        `season_year` BIGINT, \
        `series_new_cnt` BIGINT, \
        `single_new_cnt` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_tbl_assignclass_coursebind_studentcount', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_tbl_assignclass_coursebind_studentcount.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_tbl_assignclass_coursebind_studentcount ( \
        `all_cnt` BIGINT, \
        `assistant_uid` BIGINT, \
        `continue_cnt` BIGINT, \
        `continue_error_cnt` BIGINT, \
        `course_grade` BIGINT, \
        `course_id` BIGINT, \
        `course_is_inner` BIGINT, \
        `course_subject` BIGINT, \
        `course_type` BIGINT, \
        `delay_transfer_new_cnt` BIGINT, \
        `delay_transfer_old_cnt` BIGINT, \
        `deleted` BIGINT, \
        `new_cnt` BIGINT, \
        `no_transfer_new_cnt` BIGINT, \
        `no_transfer_old_cnt` BIGINT, \
        `nomal_transfer_new_cnt` BIGINT, \
        `normal_transfer_old_cnt` BIGINT, \
        `old_cnt` BIGINT, \
        `person_uid` BIGINT, \
        `pull_new_duty` BIGINT, \
        `season` BIGINT, \
        `season_num` BIGINT, \
        `season_year` BIGINT, \
        `series_new_cnt` BIGINT, \
        `single_new_cnt` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_tbl_assignclass_coursebind_studentcount', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_tbl_assignclass_coursebind_studentcount \
    SELECT \
        `all_cnt`, \
        `assistant_uid`, \
        `continue_cnt`, \
        `continue_error_cnt`, \
        `course_grade`, \
        `course_id`, \
        `course_is_inner`, \
        `course_subject`, \
        `course_type`, \
        `delay_transfer_new_cnt`, \
        `delay_transfer_old_cnt`, \
        `deleted`, \
        `new_cnt`, \
        `no_transfer_new_cnt`, \
        `no_transfer_old_cnt`, \
        `nomal_transfer_new_cnt`, \
        `normal_transfer_old_cnt`, \
        `old_cnt`, \
        `person_uid`, \
        `pull_new_duty`, \
        `season`, \
        `season_num`, \
        `season_year`, \
        `series_new_cnt`, \
        `single_new_cnt`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_tbl_assignclass_coursebind_studentcount
