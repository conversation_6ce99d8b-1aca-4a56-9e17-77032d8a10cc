# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_action, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_action, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_action.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_action.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_action ( \
        `course_id` BIGINT, \
        `exam27` STRING, \
        `exam35` STRING, \
        `exam36` STRING, \
        `exam37` STRING, \
        `exam38` STRING, \
        `exam39` STRING, \
        `exam40` STRING, \
        `exam41` STRING, \
        `exam42` STRING, \
        `exam43` STRING, \
        `exam44` STRING, \
        `exam45` STRING, \
        `exam46` STRING, \
        `is_need_participate` BIGINT, \
        `is_stage_finish` BIGINT, \
        `stage_id` BIGINT, \
        `stage_release_time` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_action', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_action.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_action ( \
        `course_id` BIGINT, \
        `exam27` STRING, \
        `exam35` STRING, \
        `exam36` STRING, \
        `exam37` STRING, \
        `exam38` STRING, \
        `exam39` STRING, \
        `exam40` STRING, \
        `exam41` STRING, \
        `exam42` STRING, \
        `exam43` STRING, \
        `exam44` STRING, \
        `exam45` STRING, \
        `exam46` STRING, \
        `is_need_participate` BIGINT, \
        `is_stage_finish` BIGINT, \
        `stage_id` BIGINT, \
        `stage_release_time` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_course_stage_student_action', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_action \
    SELECT \
        `course_id`, \
        `exam27`, \
        `exam35`, \
        `exam36`, \
        `exam37`, \
        `exam38`, \
        `exam39`, \
        `exam40`, \
        `exam41`, \
        `exam42`, \
        `exam43`, \
        `exam44`, \
        `exam45`, \
        `exam46`, \
        `is_need_participate`, \
        `is_stage_finish`, \
        `stage_id`, \
        `stage_release_time`, \
        `student_uid`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_action
