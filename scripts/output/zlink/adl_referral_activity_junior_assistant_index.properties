# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_junior_assistant_index, \
             dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_junior_assistant_index, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_junior_assistant_index.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_junior_assistant_index.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_junior_assistant_index ( \
        `assistant_uid` BIGINT, \
        `config_id` BIGINT, \
        `dt` BIGINT, \
        `invitee_uid_bind_pv` BIGINT, \
        `invitee_uid_uv` BIGINT, \
        `inviter_uid_uv` BIGINT, \
        `poster_uv` BIGINT, \
        `season` BIGINT, \
        `student_uid_uv` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_junior_assistant_index', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_junior_assistant_index.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_junior_assistant_index ( \
        `assistant_uid` BIGINT, \
        `config_id` BIGINT, \
        `dt` BIGINT, \
        `invitee_uid_bind_pv` BIGINT, \
        `invitee_uid_uv` BIGINT, \
        `inviter_uid_uv` BIGINT, \
        `poster_uv` BIGINT, \
        `season` BIGINT, \
        `student_uid_uv` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_adl_referral_activity_junior_assistant_index', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_junior_assistant_index \
    SELECT \
        `assistant_uid`, \
        `config_id`, \
        `dt`, \
        `invitee_uid_bind_pv`, \
        `invitee_uid_uv`, \
        `inviter_uid_uv`, \
        `poster_uv`, \
        `season`, \
        `student_uid_uv`, \
        `year`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_junior_assistant_index
