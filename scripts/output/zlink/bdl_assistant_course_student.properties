# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_assistant_course_student, \
             dest_zyb_zbk_bzr_ads_ads_zbk_bdl_assistant_course_student, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_assistant_course_student.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_assistant_course_student.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_assistant_course_student ( \
        `assistant_uid` BIGINT, \
        `class_id` BIGINT, \
        `course_id` BIGINT, \
        `reg_time` BIGINT, \
        `status` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_bdl_assistant_course_student', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_bdl_assistant_course_student.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_bdl_assistant_course_student ( \
        `assistant_uid` BIGINT, \
        `class_id` BIGINT, \
        `course_id` BIGINT, \
        `reg_time` BIGINT, \
        `status` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_bdl_assistant_course_student', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_bdl_assistant_course_student \
    SELECT \
        `assistant_uid`, \
        `class_id`, \
        `course_id`, \
        `reg_time`, \
        `status`, \
        `student_uid`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_assistant_course_student
