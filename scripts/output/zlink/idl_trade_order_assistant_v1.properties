# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_assistant_v1, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_assistant_v1, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_assistant_v1.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_assistant_v1.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_assistant_v1 ( \
        `is_transfer_student_typeB` BIGINT, \
        `assistant_uid` BIGINT, \
        `change_from` BIGINT, \
        `change_status` BIGINT, \
        `change_time` BIGINT, \
        `change_to` BIGINT, \
        `course_id` BIGINT, \
        `create_time` BIGINT, \
        `current_sale_trans_pv` BIGINT, \
        `current_trans_pv` BIGINT, \
        `enroll_deer_programming_last_time` BIGINT, \
        `enroll_deer_writing_last_time` BIGINT, \
        `is_bound_discount` BIGINT, \
        `is_bound_discount_retain` BIGINT, \
        `is_delete` BIGINT, \
        `is_l1r` BIGINT, \
        `is_l2_once` BIGINT, \
        `is_l2r` BIGINT, \
        `is_l2r_bound_space_season` BIGINT, \
        `is_l2r_space_season` BIGINT, \
        `is_new` BIGINT, \
        `is_refund_pre` BIGINT, \
        `is_refund_start` BIGINT, \
        `is_retain` BIGINT, \
        `is_trans` BIGINT, \
        `is_trans_once` BIGINT, \
        `is_trans_once_time` BIGINT, \
        `is_transfer` BIGINT, \
        `is_transfer_student` BIGINT, \
        `iss_deer_programming` BIGINT, \
        `iss_deer_writing` BIGINT, \
        `last_refund_l2_time` BIGINT, \
        `learn_season` BIGINT, \
        `new_user_type` STRING, \
        `next_sale_trans_pv` BIGINT, \
        `next_trans_pv` BIGINT, \
        `other_trans_pv` BIGINT, \
        `pre_retain_detail` STRING, \
        `pre_status` BIGINT, \
        `pre_status_autume_spring` BIGINT, \
        `pre_time` BIGINT, \
        `province_name` STRING, \
        `refund_pre_time` BIGINT, \
        `refund_time` BIGINT, \
        `reserve_infos` STRING, \
        `retain_detail` STRING, \
        `sku_id` BIGINT, \
        `space_season_bound_detail` STRING, \
        `space_season_retain_detail` STRING, \
        `status` BIGINT, \
        `student_uid` BIGINT, \
        `sub_trade_id` BIGINT, \
        `trade_id` BIGINT, \
        `trans_detail` STRING, \
        `transfer_available_subject` STRING, \
        `transfer_bound_subtrade_id_count` BIGINT, \
        `transfer_subtrade_id_count` BIGINT, \
        `update_time` BIGINT, \
        `year` BIGINT, \
        `year_season` STRING \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_assistant_v1', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_assistant_v1.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_assistant_v1 ( \
        `is_transfer_student_typeB` BIGINT, \
        `assistant_uid` BIGINT, \
        `change_from` BIGINT, \
        `change_status` BIGINT, \
        `change_time` BIGINT, \
        `change_to` BIGINT, \
        `course_id` BIGINT, \
        `create_time` BIGINT, \
        `current_sale_trans_pv` BIGINT, \
        `current_trans_pv` BIGINT, \
        `enroll_deer_programming_last_time` BIGINT, \
        `enroll_deer_writing_last_time` BIGINT, \
        `is_bound_discount` BIGINT, \
        `is_bound_discount_retain` BIGINT, \
        `is_delete` BIGINT, \
        `is_l1r` BIGINT, \
        `is_l2_once` BIGINT, \
        `is_l2r` BIGINT, \
        `is_l2r_bound_space_season` BIGINT, \
        `is_l2r_space_season` BIGINT, \
        `is_new` BIGINT, \
        `is_refund_pre` BIGINT, \
        `is_refund_start` BIGINT, \
        `is_retain` BIGINT, \
        `is_trans` BIGINT, \
        `is_trans_once` BIGINT, \
        `is_trans_once_time` BIGINT, \
        `is_transfer` BIGINT, \
        `is_transfer_student` BIGINT, \
        `iss_deer_programming` BIGINT, \
        `iss_deer_writing` BIGINT, \
        `last_refund_l2_time` BIGINT, \
        `learn_season` BIGINT, \
        `new_user_type` STRING, \
        `next_sale_trans_pv` BIGINT, \
        `next_trans_pv` BIGINT, \
        `other_trans_pv` BIGINT, \
        `pre_retain_detail` STRING, \
        `pre_status` BIGINT, \
        `pre_status_autume_spring` BIGINT, \
        `pre_time` BIGINT, \
        `province_name` STRING, \
        `refund_pre_time` BIGINT, \
        `refund_time` BIGINT, \
        `reserve_infos` STRING, \
        `retain_detail` STRING, \
        `sku_id` BIGINT, \
        `space_season_bound_detail` STRING, \
        `space_season_retain_detail` STRING, \
        `status` BIGINT, \
        `student_uid` BIGINT, \
        `sub_trade_id` BIGINT, \
        `trade_id` BIGINT, \
        `trans_detail` STRING, \
        `transfer_available_subject` STRING, \
        `transfer_bound_subtrade_id_count` BIGINT, \
        `transfer_subtrade_id_count` BIGINT, \
        `update_time` BIGINT, \
        `year` BIGINT, \
        `year_season` STRING \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_trade_order_assistant_v1', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_assistant_v1 \
    SELECT \
        `is_transfer_student_typeB`, \
        `assistant_uid`, \
        `change_from`, \
        `change_status`, \
        `change_time`, \
        `change_to`, \
        `course_id`, \
        `create_time`, \
        `current_sale_trans_pv`, \
        `current_trans_pv`, \
        `enroll_deer_programming_last_time`, \
        `enroll_deer_writing_last_time`, \
        `is_bound_discount`, \
        `is_bound_discount_retain`, \
        `is_delete`, \
        `is_l1r`, \
        `is_l2_once`, \
        `is_l2r`, \
        `is_l2r_bound_space_season`, \
        `is_l2r_space_season`, \
        `is_new`, \
        `is_refund_pre`, \
        `is_refund_start`, \
        `is_retain`, \
        `is_trans`, \
        `is_trans_once`, \
        `is_trans_once_time`, \
        `is_transfer`, \
        `is_transfer_student`, \
        `iss_deer_programming`, \
        `iss_deer_writing`, \
        `last_refund_l2_time`, \
        `learn_season`, \
        `new_user_type`, \
        `next_sale_trans_pv`, \
        `next_trans_pv`, \
        `other_trans_pv`, \
        `pre_retain_detail`, \
        `pre_status`, \
        `pre_status_autume_spring`, \
        `pre_time`, \
        `province_name`, \
        `refund_pre_time`, \
        `refund_time`, \
        `reserve_infos`, \
        `retain_detail`, \
        `sku_id`, \
        `space_season_bound_detail`, \
        `space_season_retain_detail`, \
        `status`, \
        `student_uid`, \
        `sub_trade_id`, \
        `trade_id`, \
        `trans_detail`, \
        `transfer_available_subject`, \
        `transfer_bound_subtrade_id_count`, \
        `transfer_subtrade_id_count`, \
        `update_time`, \
        `year`, \
        `year_season`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_assistant_v1
