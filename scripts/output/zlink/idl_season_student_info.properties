# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_season_student_info, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_season_student_info, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_season_student_info.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_season_student_info.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_season_student_info ( \
        `grade_id` BIGINT, \
        `pre_subject` STRING, \
        `pre_subject_num` STRING, \
        `season_id` BIGINT, \
        `student_uid` BIGINT, \
        `subject` STRING, \
        `subject_num` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_season_student_info', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_season_student_info.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_season_student_info ( \
        `grade_id` BIGINT, \
        `pre_subject` STRING, \
        `pre_subject_num` STRING, \
        `season_id` BIGINT, \
        `student_uid` BIGINT, \
        `subject` STRING, \
        `subject_num` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_season_student_info', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_season_student_info \
    SELECT \
        `grade_id`, \
        `pre_subject`, \
        `pre_subject_num`, \
        `season_id`, \
        `student_uid`, \
        `subject`, \
        `subject_num`, \
        `year`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_season_student_info
