# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_lu, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_lu, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_lu.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_lu.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_lu ( \
        `attend_duration` BIGINT, \
        `course_id` BIGINT, \
        `is_asscourse_attend` BIGINT, \
        `is_asscourse_finish` BIGINT, \
        `lesson_id` BIGINT, \
        `playback_time` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_assistantcourse_lu', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_lu.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_lu ( \
        `attend_duration` BIGINT, \
        `course_id` BIGINT, \
        `is_asscourse_attend` BIGINT, \
        `is_asscourse_finish` BIGINT, \
        `lesson_id` BIGINT, \
        `playback_time` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_assistantcourse_lu', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_lu \
    SELECT \
        `attend_duration`, \
        `course_id`, \
        `is_asscourse_attend`, \
        `is_asscourse_finish`, \
        `lesson_id`, \
        `playback_time`, \
        `student_uid`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_lu
