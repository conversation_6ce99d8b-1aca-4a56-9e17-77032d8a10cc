# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_all_action_v1, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_all_action_v1, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_all_action_v1.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_all_action_v1.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_all_action_v1 ( \
        `all_index` BIGINT, \
        `assistant_first_access_d_value` BIGINT, \
        `assistant_first_access_time` BIGINT, \
        `assistant_last_leave_time` BIGINT, \
        `assistant_postclass_live_duration` BIGINT, \
        `assistant_preclass_live_duration` BIGINT, \
        `assistant_uid` B<PERSON>IN<PERSON>, \
        `brand_id` BIGINT, \
        `course_assistant_bind_detail` STRING, \
        `course_id` BIGINT, \
        `course_name` STRING, \
        `course_type` BIGINT, \
        `courseware_upload_time` BIGINT, \
        `deleted` BIGINT, \
        `grade_period` BIGINT, \
        `is_assistant_postclass_delay` BIGINT, \
        `is_assistant_preclass_late` BIGINT, \
        `is_assistant_preclass_redgift` BIGINT, \
        `is_courseware_upload_timeout` BIGINT, \
        `is_inner` BIGINT, \
        `is_main` BIGINT, \
        `learn_season` BIGINT, \
        `lesson_assistant_status` BIGINT, \
        `lesson_deleted` BIGINT, \
        `lesson_id` BIGINT, \
        `lesson_name` STRING, \
        `lesson_start_time` BIGINT, \
        `lesson_stop_time` BIGINT, \
        `main_grade` BIGINT, \
        `main_index` BIGINT, \
        `main_subject` BIGINT, \
        `restart_id` BIGINT, \
        `upload_status` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_all_action_v1', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_all_action_v1.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_all_action_v1 ( \
        `all_index` BIGINT, \
        `assistant_first_access_d_value` BIGINT, \
        `assistant_first_access_time` BIGINT, \
        `assistant_last_leave_time` BIGINT, \
        `assistant_postclass_live_duration` BIGINT, \
        `assistant_preclass_live_duration` BIGINT, \
        `assistant_uid` BIGINT, \
        `brand_id` BIGINT, \
        `course_assistant_bind_detail` STRING, \
        `course_id` BIGINT, \
        `course_name` STRING, \
        `course_type` BIGINT, \
        `courseware_upload_time` BIGINT, \
        `deleted` BIGINT, \
        `grade_period` BIGINT, \
        `is_assistant_postclass_delay` BIGINT, \
        `is_assistant_preclass_late` BIGINT, \
        `is_assistant_preclass_redgift` BIGINT, \
        `is_courseware_upload_timeout` BIGINT, \
        `is_inner` BIGINT, \
        `is_main` BIGINT, \
        `learn_season` BIGINT, \
        `lesson_assistant_status` BIGINT, \
        `lesson_deleted` BIGINT, \
        `lesson_id` BIGINT, \
        `lesson_name` STRING, \
        `lesson_start_time` BIGINT, \
        `lesson_stop_time` BIGINT, \
        `main_grade` BIGINT, \
        `main_index` BIGINT, \
        `main_subject` BIGINT, \
        `restart_id` BIGINT, \
        `upload_status` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_all_action_v1', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_all_action_v1 \
    SELECT \
        `all_index`, \
        `assistant_first_access_d_value`, \
        `assistant_first_access_time`, \
        `assistant_last_leave_time`, \
        `assistant_postclass_live_duration`, \
        `assistant_preclass_live_duration`, \
        `assistant_uid`, \
        `brand_id`, \
        `course_assistant_bind_detail`, \
        `course_id`, \
        `course_name`, \
        `course_type`, \
        `courseware_upload_time`, \
        `deleted`, \
        `grade_period`, \
        `is_assistant_postclass_delay`, \
        `is_assistant_preclass_late`, \
        `is_assistant_preclass_redgift`, \
        `is_courseware_upload_timeout`, \
        `is_inner`, \
        `is_main`, \
        `learn_season`, \
        `lesson_assistant_status`, \
        `lesson_deleted`, \
        `lesson_id`, \
        `lesson_name`, \
        `lesson_start_time`, \
        `lesson_stop_time`, \
        `main_grade`, \
        `main_index`, \
        `main_subject`, \
        `restart_id`, \
        `upload_status`, \
        `year`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_all_action_v1
