# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice, \
             dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice ( \
        `course_id` BIGINT, \
        `is_l2_once` BIGINT, \
        `is_l2r` BIGINT, \
        `last_l2r_course` BIGINT, \
        `last_l2r_sub_trade_id` BIGINT, \
        `last_l2r_time` BIGINT, \
        `last_refund_l2_time` BIGINT, \
        `learn_season` BIGINT, \
        `retain_detail` STRING, \
        `status` BIGINT, \
        `student_uid` BIGINT, \
        `sub_trade_id` BIGINT, \
        `trade_time` BIGINT, \
        `update_time` BIGINT, \
        `year` BIGINT, \
        `year_season` STRING \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_tblcontinuenotice', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice ( \
        `course_id` BIGINT, \
        `is_l2_once` BIGINT, \
        `is_l2r` BIGINT, \
        `last_l2r_course` BIGINT, \
        `last_l2r_sub_trade_id` BIGINT, \
        `last_l2r_time` BIGINT, \
        `last_refund_l2_time` BIGINT, \
        `learn_season` BIGINT, \
        `retain_detail` STRING, \
        `status` BIGINT, \
        `student_uid` BIGINT, \
        `sub_trade_id` BIGINT, \
        `trade_time` BIGINT, \
        `update_time` BIGINT, \
        `year` BIGINT, \
        `year_season` STRING \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_tblcontinuenotice', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice \
    SELECT \
        `course_id`, \
        `is_l2_once`, \
        `is_l2r`, \
        `last_l2r_course`, \
        `last_l2r_sub_trade_id`, \
        `last_l2r_time`, \
        `last_refund_l2_time`, \
        `learn_season`, \
        `retain_detail`, \
        `status`, \
        `student_uid`, \
        `sub_trade_id`, \
        `trade_time`, \
        `update_time`, \
        `year`, \
        `year_season`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice
