# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_cu, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_cu, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_cu.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_cu.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_cu ( \
        `asscourse_attend_count` BIGINT, \
        `asscourse_finish_count` BIGINT, \
        `course_id` BIGINT, \
        `is_refund` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_assistantcourse_cu', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_cu.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_cu ( \
        `asscourse_attend_count` BIGINT, \
        `asscourse_finish_count` BIGINT, \
        `course_id` BIGINT, \
        `is_refund` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_assistantcourse_cu', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_cu \
    SELECT \
        `asscourse_attend_count`, \
        `asscourse_finish_count`, \
        `course_id`, \
        `is_refund`, \
        `student_uid`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_cu
