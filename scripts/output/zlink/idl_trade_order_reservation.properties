# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_reservation, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_reservation, \
             sink_ads_base_2_ads


#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_reservation.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_reservation.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_reservation ( \
        `course_id` BIGINT, \
        `create_time` BIGINT, \
        `pre_id` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_reservation', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_reservation.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_reservation ( \
        `course_id` BIGINT, \
        `create_time` BIGINT, \
        `pre_id` BIGINT, \
        `student_uid` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_trade_order_reservation', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_reservation \
    SELECT \
        `course_id`, \
        `create_time`, \
        `pre_id`, \
        `student_uid`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_reservation
