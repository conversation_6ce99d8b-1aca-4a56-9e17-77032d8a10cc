# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_tid_answer_info, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_tid_answer_info, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_tid_answer_info.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_tid_answer_info.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_tid_answer_info ( \
        `answer_id` STRING, \
        `course_id` BIGINT, \
        `exam_id` BIGINT, \
        `exam_type` BIGINT, \
        `is_right` BIGINT, \
        `is_submit` BIGINT, \
        `stage_id` BIGINT, \
        `student_uid` BIGINT, \
        `submit_time` BIGINT, \
        `tid` BIGINT, \
        `tid_idx` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_tid_answer_info', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_tid_answer_info.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_tid_answer_info ( \
        `answer_id` STRING, \
        `course_id` BIGINT, \
        `exam_id` BIGINT, \
        `exam_type` BIGINT, \
        `is_right` BIGINT, \
        `is_submit` BIGINT, \
        `stage_id` BIGINT, \
        `student_uid` BIGINT, \
        `submit_time` BIGINT, \
        `tid` BIGINT, \
        `tid_idx` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_course_stage_student_tid_answer_info', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_tid_answer_info \
    SELECT \
        `answer_id`, \
        `course_id`, \
        `exam_id`, \
        `exam_type`, \
        `is_right`, \
        `is_submit`, \
        `stage_id`, \
        `student_uid`, \
        `submit_time`, \
        `tid`, \
        `tid_idx`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_tid_answer_info
