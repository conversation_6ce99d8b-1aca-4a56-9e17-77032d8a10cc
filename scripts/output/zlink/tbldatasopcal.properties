# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_tbldatasopcal, \
             dest_zyb_zbk_bzr_ads_ads_zbk_tbldatasopcal, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_tbldatasopcal.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_tbldatasopcal.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_tbldatasopcal ( \
        `homework_SorA_num` BIGINT, \
        `after_unlock_7d_attend_playback_num` BIGINT, \
        `after_unlock_7d_finish_playback_num` BIGINT, \
        `after_unlock_attend_playback_num` BIGINT, \
        `after_unlock_finish_playback_num` BIGINT, \
        `after_unlock_need_attend_num` BIGINT, \
        `after_unlock_playback_num` BIGINT, \
        `ai_attend_num` BIGINT, \
        `ai_attend_total` BIGINT, \
        `ai_class_finish_num` BIGINT, \
        `all_time_homework_submit_num` BIGINT, \
        `all_view_num` BIGINT, \
        `assistant_uid` BIGINT, \
        `assistantcourse_attend_finish_num` BIGINT, \
        `assistantcourse_attend_num` BIGINT, \
        `assistantcourse_need_attend_num` BIGINT, \
        `attend_num` BIGINT, \
        `attend_num_5m` BIGINT, \
        `attend_num_quarter` BIGINT, \
        `attend_one_third_num` BIGINT, \
        `attend_total` BIGINT, \
        `attend_two_third_num` BIGINT, \
        `challenge_attend_right_num` BIGINT, \
        `challenge_attend_total_num` BIGINT, \
        `challenge_finish_num` BIGINT, \
        `challenge_total_num` BIGINT, \
        `chat_num` BIGINT, \
        `class_finish_num` BIGINT, \
        `corrections_homework_num48` BIGINT, \
        `corrections_homework_num72` BIGINT, \
        `course_id` BIGINT, \
        `exam33_similar_question_amend_num` BIGINT, \
        `exam33_similar_question_submit_num` BIGINT, \
        `exam33_submit_and_unamend_num` BIGINT, \
        `exam33_wrong_similar_expound_video_view_num` BIGINT, \
        `exam7_submit_and_unamend_num` BIGINT, \
        `exam7_wrong_expound_video_view_num` BIGINT, \
        `homework_amend_num` BIGINT, \
        `homework_amend_num_in_14d` BIGINT, \
        `homework_num` BIGINT, \
        `homework_recorrection_num` BIGINT, \
        `homework_submit_3_times_or_amend_num` BIGINT, \
        `homework_tid_first_correct_cnt` BIGINT, \
        `homework_tid_first_right_cnt` BIGINT, \
        `interactive_participate_num` BIGINT, \
        `interactive_right_num` BIGINT, \
        `interactive_total_num` BIGINT, \
        `is_follow` BIGINT, \
        `is_follow_a` BIGINT, \
        `lesson_finish_num` BIGINT, \
        `lesson_id` BIGINT, \
        `lesson_test_finish_num` BIGINT, \
        `lesson_test_participate_num` BIGINT, \
        `lesson_test_right_num` BIGINT, \
        `lesson_test_total_num` BIGINT, \
        `lesson_total_num` BIGINT, \
        `niudao_finish_num` BIGINT, \
        `niudao_total_num` BIGINT, \
        `playback_all_num` BIGINT, \
        `playback_participate_num` BIGINT, \
        `playback_right_num` BIGINT, \
        `postclass_attend_num` BIGINT, \
        `postclass_finish_attend_num` BIGINT, \
        `pre_attend_is5_num` BIGINT, \
        `pre_attend_num` BIGINT, \
        `preclass_attend_num` BIGINT, \
        `preclass_finish_attend_num` BIGINT, \
        `preview_bef_fin_num` BIGINT, \
        `preview_finish_num` BIGINT, \
        `reg_num` BIGINT, \
        `save_time` BIGINT, \
        `stage_test_finish_num` BIGINT, \
        `transfer_status` BIGINT, \
        `update_time` BIGINT, \
        `user_type` STRING, \
        `view_finish_num` BIGINT, \
        `view_finish_num_in_14d` BIGINT, \
        `view_num` BIGINT, \
        `view_num_5m` BIGINT, \
        `view_num_quarter` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopcal', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_tbldatasopcal.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_tbldatasopcal ( \
        `homework_SorA_num` BIGINT, \
        `after_unlock_7d_attend_playback_num` BIGINT, \
        `after_unlock_7d_finish_playback_num` BIGINT, \
        `after_unlock_attend_playback_num` BIGINT, \
        `after_unlock_finish_playback_num` BIGINT, \
        `after_unlock_need_attend_num` BIGINT, \
        `after_unlock_playback_num` BIGINT, \
        `ai_attend_num` BIGINT, \
        `ai_attend_total` BIGINT, \
        `ai_class_finish_num` BIGINT, \
        `all_time_homework_submit_num` BIGINT, \
        `all_view_num` BIGINT, \
        `assistant_uid` BIGINT, \
        `assistantcourse_attend_finish_num` BIGINT, \
        `assistantcourse_attend_num` BIGINT, \
        `assistantcourse_need_attend_num` BIGINT, \
        `attend_num` BIGINT, \
        `attend_num_5m` BIGINT, \
        `attend_num_quarter` BIGINT, \
        `attend_one_third_num` BIGINT, \
        `attend_total` BIGINT, \
        `attend_two_third_num` BIGINT, \
        `challenge_attend_right_num` BIGINT, \
        `challenge_attend_total_num` BIGINT, \
        `challenge_finish_num` BIGINT, \
        `challenge_total_num` BIGINT, \
        `chat_num` BIGINT, \
        `class_finish_num` BIGINT, \
        `corrections_homework_num48` BIGINT, \
        `corrections_homework_num72` BIGINT, \
        `course_id` BIGINT, \
        `exam33_similar_question_amend_num` BIGINT, \
        `exam33_similar_question_submit_num` BIGINT, \
        `exam33_submit_and_unamend_num` BIGINT, \
        `exam33_wrong_similar_expound_video_view_num` BIGINT, \
        `exam7_submit_and_unamend_num` BIGINT, \
        `exam7_wrong_expound_video_view_num` BIGINT, \
        `homework_amend_num` BIGINT, \
        `homework_amend_num_in_14d` BIGINT, \
        `homework_num` BIGINT, \
        `homework_recorrection_num` BIGINT, \
        `homework_submit_3_times_or_amend_num` BIGINT, \
        `homework_tid_first_correct_cnt` BIGINT, \
        `homework_tid_first_right_cnt` BIGINT, \
        `interactive_participate_num` BIGINT, \
        `interactive_right_num` BIGINT, \
        `interactive_total_num` BIGINT, \
        `is_follow` BIGINT, \
        `is_follow_a` BIGINT, \
        `lesson_finish_num` BIGINT, \
        `lesson_id` BIGINT, \
        `lesson_test_finish_num` BIGINT, \
        `lesson_test_participate_num` BIGINT, \
        `lesson_test_right_num` BIGINT, \
        `lesson_test_total_num` BIGINT, \
        `lesson_total_num` BIGINT, \
        `niudao_finish_num` BIGINT, \
        `niudao_total_num` BIGINT, \
        `playback_all_num` BIGINT, \
        `playback_participate_num` BIGINT, \
        `playback_right_num` BIGINT, \
        `postclass_attend_num` BIGINT, \
        `postclass_finish_attend_num` BIGINT, \
        `pre_attend_is5_num` BIGINT, \
        `pre_attend_num` BIGINT, \
        `preclass_attend_num` BIGINT, \
        `preclass_finish_attend_num` BIGINT, \
        `preview_bef_fin_num` BIGINT, \
        `preview_finish_num` BIGINT, \
        `reg_num` BIGINT, \
        `save_time` BIGINT, \
        `stage_test_finish_num` BIGINT, \
        `transfer_status` BIGINT, \
        `update_time` BIGINT, \
        `user_type` STRING, \
        `view_finish_num` BIGINT, \
        `view_finish_num_in_14d` BIGINT, \
        `view_num` BIGINT, \
        `view_num_5m` BIGINT, \
        `view_num_quarter` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_tbldatasopcal', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_tbldatasopcal \
    SELECT \
        `homework_SorA_num`, \
        `after_unlock_7d_attend_playback_num`, \
        `after_unlock_7d_finish_playback_num`, \
        `after_unlock_attend_playback_num`, \
        `after_unlock_finish_playback_num`, \
        `after_unlock_need_attend_num`, \
        `after_unlock_playback_num`, \
        `ai_attend_num`, \
        `ai_attend_total`, \
        `ai_class_finish_num`, \
        `all_time_homework_submit_num`, \
        `all_view_num`, \
        `assistant_uid`, \
        `assistantcourse_attend_finish_num`, \
        `assistantcourse_attend_num`, \
        `assistantcourse_need_attend_num`, \
        `attend_num`, \
        `attend_num_5m`, \
        `attend_num_quarter`, \
        `attend_one_third_num`, \
        `attend_total`, \
        `attend_two_third_num`, \
        `challenge_attend_right_num`, \
        `challenge_attend_total_num`, \
        `challenge_finish_num`, \
        `challenge_total_num`, \
        `chat_num`, \
        `class_finish_num`, \
        `corrections_homework_num48`, \
        `corrections_homework_num72`, \
        `course_id`, \
        `exam33_similar_question_amend_num`, \
        `exam33_similar_question_submit_num`, \
        `exam33_submit_and_unamend_num`, \
        `exam33_wrong_similar_expound_video_view_num`, \
        `exam7_submit_and_unamend_num`, \
        `exam7_wrong_expound_video_view_num`, \
        `homework_amend_num`, \
        `homework_amend_num_in_14d`, \
        `homework_num`, \
        `homework_recorrection_num`, \
        `homework_submit_3_times_or_amend_num`, \
        `homework_tid_first_correct_cnt`, \
        `homework_tid_first_right_cnt`, \
        `interactive_participate_num`, \
        `interactive_right_num`, \
        `interactive_total_num`, \
        `is_follow`, \
        `is_follow_a`, \
        `lesson_finish_num`, \
        `lesson_id`, \
        `lesson_test_finish_num`, \
        `lesson_test_participate_num`, \
        `lesson_test_right_num`, \
        `lesson_test_total_num`, \
        `lesson_total_num`, \
        `niudao_finish_num`, \
        `niudao_total_num`, \
        `playback_all_num`, \
        `playback_participate_num`, \
        `playback_right_num`, \
        `postclass_attend_num`, \
        `postclass_finish_attend_num`, \
        `pre_attend_is5_num`, \
        `pre_attend_num`, \
        `preclass_attend_num`, \
        `preclass_finish_attend_num`, \
        `preview_bef_fin_num`, \
        `preview_finish_num`, \
        `reg_num`, \
        `save_time`, \
        `stage_test_finish_num`, \
        `transfer_status`, \
        `update_time`, \
        `user_type`, \
        `view_finish_num`, \
        `view_finish_num_in_14d`, \
        `view_num`, \
        `view_num_5m`, \
        `view_num_quarter`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_tbldatasopcal
