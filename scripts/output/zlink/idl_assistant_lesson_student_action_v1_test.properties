# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_student_action_v1_test, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_student_action_v1_test, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_student_action_v1_test.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_student_action_v1_test.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_student_action_v1_test ( \
        `all_lesson_index` BIGINT, \
        `assistant_uid` BIGINT, \
        `attend` BIGINT, \
        `attend_detail` STRING, \
        `attend_duration` BIGINT, \
        `attend_long` BIGINT, \
        `attend_quarter` BIGINT, \
        `boost_lesson_index` BIGINT, \
        `chat_num` BIGINT, \
        `course_id` BIGINT, \
        `course_name` STRING, \
        `course_type` BIGINT, \
        `exam_answer` STRING, \
        `hx_alltest_finishnum` BIGINT, \
        `hx_pretest_finishnum` BIGINT, \
        `inout_count` BIGINT, \
        `interaction_answer_detail` STRING, \
        `is_assistantcourse_attend` BIGINT, \
        `is_assistantcourse_finish` BIGINT, \
        `is_attend_finish` BIGINT, \
        `is_lbp_attend` BIGINT, \
        `is_lbp_attend_finish` BIGINT, \
        `is_need_attend` BIGINT, \
        `is_need_attend_after_unlock` BIGINT, \
        `is_playback_finish_after_unlock` BIGINT, \
        `is_playback_finish_after_unlock_7d` BIGINT, \
        `is_playback_long_after_unlock` BIGINT, \
        `is_playback_long_after_unlock_7d` BIGINT, \
        `is_playback_quarter` BIGINT, \
        `is_postclass_attend` BIGINT, \
        `is_postclass_finish_attend` BIGINT, \
        `is_preclass_attend` BIGINT, \
        `is_preclass_finish_attend` BIGINT, \
        `is_view_finish_in_14d` BIGINT, \
        `is_view_finished` BIGINT, \
        `last_playback_time` BIGINT, \
        `lbp_attend_duration` BIGINT, \
        `learn_season` BIGINT, \
        `lesson_deleted` BIGINT, \
        `lesson_id` BIGINT, \
        `lesson_name` STRING, \
        `lesson_start_time` BIGINT, \
        `lesson_status` BIGINT, \
        `lesson_stop_time` BIGINT, \
        `lesson_type` BIGINT, \
        `lesson_unlock_time` BIGINT, \
        `main_department` BIGINT, \
        `main_grade` BIGINT, \
        `main_lesson_index` BIGINT, \
        `main_subject` BIGINT, \
        `new_user_type` STRING, \
        `photo_assistant_uid` BIGINT, \
        `playback_participate_num` BIGINT, \
        `playback_right_num` BIGINT, \
        `playback_time` BIGINT, \
        `playback_time_after_unlock` BIGINT, \
        `playback_time_after_unlock_7d` BIGINT, \
        `playback_time_in_14d` BIGINT, \
        `playback_time_in_7d` BIGINT, \
        `postclass_attend_duration` BIGINT, \
        `pre_attend` BIGINT, \
        `preclass_attend_duration` BIGINT, \
        `restart_id` BIGINT, \
        `student_attend_label` STRING, \
        `student_interaction_label` STRING, \
        `student_uid` BIGINT, \
        `sub_trade_id` BIGINT, \
        `teacher_id` BIGINT, \
        `trade_change_status` BIGINT, \
        `trade_change_time` BIGINT, \
        `trade_create_time` BIGINT, \
        `trade_refund_time` BIGINT, \
        `trade_status` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_student_action_v1_test', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_student_action_v1_test.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_student_action_v1_test ( \
        `all_lesson_index` BIGINT, \
        `assistant_uid` BIGINT, \
        `attend` BIGINT, \
        `attend_detail` STRING, \
        `attend_duration` BIGINT, \
        `attend_long` BIGINT, \
        `attend_quarter` BIGINT, \
        `boost_lesson_index` BIGINT, \
        `chat_num` BIGINT, \
        `course_id` BIGINT, \
        `course_name` STRING, \
        `course_type` BIGINT, \
        `exam_answer` STRING, \
        `hx_alltest_finishnum` BIGINT, \
        `hx_pretest_finishnum` BIGINT, \
        `inout_count` BIGINT, \
        `interaction_answer_detail` STRING, \
        `is_assistantcourse_attend` BIGINT, \
        `is_assistantcourse_finish` BIGINT, \
        `is_attend_finish` BIGINT, \
        `is_lbp_attend` BIGINT, \
        `is_lbp_attend_finish` BIGINT, \
        `is_need_attend` BIGINT, \
        `is_need_attend_after_unlock` BIGINT, \
        `is_playback_finish_after_unlock` BIGINT, \
        `is_playback_finish_after_unlock_7d` BIGINT, \
        `is_playback_long_after_unlock` BIGINT, \
        `is_playback_long_after_unlock_7d` BIGINT, \
        `is_playback_quarter` BIGINT, \
        `is_postclass_attend` BIGINT, \
        `is_postclass_finish_attend` BIGINT, \
        `is_preclass_attend` BIGINT, \
        `is_preclass_finish_attend` BIGINT, \
        `is_view_finish_in_14d` BIGINT, \
        `is_view_finished` BIGINT, \
        `last_playback_time` BIGINT, \
        `lbp_attend_duration` BIGINT, \
        `learn_season` BIGINT, \
        `lesson_deleted` BIGINT, \
        `lesson_id` BIGINT, \
        `lesson_name` STRING, \
        `lesson_start_time` BIGINT, \
        `lesson_status` BIGINT, \
        `lesson_stop_time` BIGINT, \
        `lesson_type` BIGINT, \
        `lesson_unlock_time` BIGINT, \
        `main_department` BIGINT, \
        `main_grade` BIGINT, \
        `main_lesson_index` BIGINT, \
        `main_subject` BIGINT, \
        `new_user_type` STRING, \
        `photo_assistant_uid` BIGINT, \
        `playback_participate_num` BIGINT, \
        `playback_right_num` BIGINT, \
        `playback_time` BIGINT, \
        `playback_time_after_unlock` BIGINT, \
        `playback_time_after_unlock_7d` BIGINT, \
        `playback_time_in_14d` BIGINT, \
        `playback_time_in_7d` BIGINT, \
        `postclass_attend_duration` BIGINT, \
        `pre_attend` BIGINT, \
        `preclass_attend_duration` BIGINT, \
        `restart_id` BIGINT, \
        `student_attend_label` STRING, \
        `student_interaction_label` STRING, \
        `student_uid` BIGINT, \
        `sub_trade_id` BIGINT, \
        `teacher_id` BIGINT, \
        `trade_change_status` BIGINT, \
        `trade_change_time` BIGINT, \
        `trade_create_time` BIGINT, \
        `trade_refund_time` BIGINT, \
        `trade_status` BIGINT, \
        `year` BIGINT, \
        `zbk_update_time` BIGINT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_student_action_v1_test', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_student_action_v1_test \
    SELECT \
        `all_lesson_index`, \
        `assistant_uid`, \
        `attend`, \
        `attend_detail`, \
        `attend_duration`, \
        `attend_long`, \
        `attend_quarter`, \
        `boost_lesson_index`, \
        `chat_num`, \
        `course_id`, \
        `course_name`, \
        `course_type`, \
        `exam_answer`, \
        `hx_alltest_finishnum`, \
        `hx_pretest_finishnum`, \
        `inout_count`, \
        `interaction_answer_detail`, \
        `is_assistantcourse_attend`, \
        `is_assistantcourse_finish`, \
        `is_attend_finish`, \
        `is_lbp_attend`, \
        `is_lbp_attend_finish`, \
        `is_need_attend`, \
        `is_need_attend_after_unlock`, \
        `is_playback_finish_after_unlock`, \
        `is_playback_finish_after_unlock_7d`, \
        `is_playback_long_after_unlock`, \
        `is_playback_long_after_unlock_7d`, \
        `is_playback_quarter`, \
        `is_postclass_attend`, \
        `is_postclass_finish_attend`, \
        `is_preclass_attend`, \
        `is_preclass_finish_attend`, \
        `is_view_finish_in_14d`, \
        `is_view_finished`, \
        `last_playback_time`, \
        `lbp_attend_duration`, \
        `learn_season`, \
        `lesson_deleted`, \
        `lesson_id`, \
        `lesson_name`, \
        `lesson_start_time`, \
        `lesson_status`, \
        `lesson_stop_time`, \
        `lesson_type`, \
        `lesson_unlock_time`, \
        `main_department`, \
        `main_grade`, \
        `main_lesson_index`, \
        `main_subject`, \
        `new_user_type`, \
        `photo_assistant_uid`, \
        `playback_participate_num`, \
        `playback_right_num`, \
        `playback_time`, \
        `playback_time_after_unlock`, \
        `playback_time_after_unlock_7d`, \
        `playback_time_in_14d`, \
        `playback_time_in_7d`, \
        `postclass_attend_duration`, \
        `pre_attend`, \
        `preclass_attend_duration`, \
        `restart_id`, \
        `student_attend_label`, \
        `student_interaction_label`, \
        `student_uid`, \
        `sub_trade_id`, \
        `teacher_id`, \
        `trade_change_status`, \
        `trade_change_time`, \
        `trade_create_time`, \
        `trade_refund_time`, \
        `trade_status`, \
        `year`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_student_action_v1_test
