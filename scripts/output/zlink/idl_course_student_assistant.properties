# 声明用到的所有表
flame.tables=source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_student_assistant, \
             dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_student_assistant, \
             sink_ads_base_2_ads

#hologres源表
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_student_assistant.group=source
flame.table.source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_student_assistant.sql=CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_student_assistant ( \
        `is_transfer_student_typeB` BIGINT, \
        `after_test_participate_num` INT, \
        `after_test_right_num` INT, \
        `assistant_uid` BIGIN<PERSON>, \
        `attend_count` BIGINT, \
        `attend_count_5m` BIGINT, \
        `attend_count_quarter` BIGINT, \
        `attend_type_label` STRING, \
        `course_id` BIGINT, \
        `course_name` STRING, \
        `course_type` BIGINT, \
        `department` BIGINT, \
        `effective_wechat_num` BIGINT, \
        `exam32_total_submit_num` BIGINT, \
        `finish_num` BIGINT, \
        `has_auto_enterprise_wechat` BIGINT, \
        `has_backinterview` BIGINT, \
        `has_backinterview_tag` BIGINT, \
        `has_bangbang_tag` BIGINT, \
        `has_interview` BIGINT, \
        `has_manual_enterprise_wechat` BIGINT, \
        `has_manual_wechat` BIGINT, \
        `has_retain_tag` BIGINT, \
        `has_tag` BIGINT, \
        `has_wechat` BIGINT, \
        `homework_submit_count` BIGINT, \
        `interview_type` BIGINT, \
        `is_after_class_phone_back_interview` BIGINT, \
        `is_after_class_phone_back_interview_1min` INT, \
        `is_backinterview_by_phone` BIGINT, \
        `is_interview_by_phone` BIGINT, \
        `is_interview_call_3min` BIGINT, \
        `is_interview_finish` BIGINT, \
        `is_phone_access` BIGINT, \
        `is_phone_cover` BIGINT, \
        `is_refund` BIGINT, \
        `is_test_complete` BIGINT, \
        `is_transfer_student` BIGINT, \
        `is_transfer_student_delayed` BIGINT, \
        `is_wechat_bind` BIGINT, \
        `last_backinterview_time` BIGINT, \
        `lbp_attend_count` BIGINT, \
        `lbp_finish_count` BIGINT, \
        `m_grade` BIGINT, \
        `m_subject` BIGINT, \
        `new_user_type` STRING, \
        `phone_backinterview_count` BIGINT, \
        `playback_cnt_attend_after_unlock_7d` BIGINT, \
        `playback_cnt_finish_after_unlock_7d` BIGINT, \
        `pre_continue` BIGINT, \
        `pre_finish_num` BIGINT, \
        `refund_time` BIGINT, \
        `service_survey_detail` STRING, \
        `start_time` BIGINT, \
        `stop_time` BIGINT, \
        `student_uid` BIGINT, \
        `test_complete_time` BIGINT, \
        `trade_id` BIGINT, \
        `trade_time` BIGINT, \
        `view_count_5m` BIGINT, \
        `wechat_cnt` BIGINT, \
        `wechat_reply_cnt` BIGINT, \
        `wechat_time` BIGINT, \
        `zbk_update_time` INT \
    ) WITH ( \
       'connector'='hologres', \
       'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
       'username'='LTAI5t5ta8eK5UciPg6fM45e', \
       'password'='******************************', \
       'dbname'='zyb_zbk_bzr', \
       'tablename'='zyb_zbk_bzr_ads_base.ads_zbk_idl_course_student_assistant', \
       'cdcMode'='true', \
       'binlogMaxRetryTimes'='10', \
       'binlogRetryIntervalMs'='500', \
       'binlogBatchReadSize'='100', \
       'binlog'='true' \
    )

flame.table.dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_student_assistant.sql=CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_student_assistant ( \
        `is_transfer_student_typeB` BIGINT, \
        `after_test_participate_num` INT, \
        `after_test_right_num` INT, \
        `assistant_uid` BIGINT, \
        `attend_count` BIGINT, \
        `attend_count_5m` BIGINT, \
        `attend_count_quarter` BIGINT, \
        `attend_type_label` STRING, \
        `course_id` BIGINT, \
        `course_name` STRING, \
        `course_type` BIGINT, \
        `department` BIGINT, \
        `effective_wechat_num` BIGINT, \
        `exam32_total_submit_num` BIGINT, \
        `finish_num` BIGINT, \
        `has_auto_enterprise_wechat` BIGINT, \
        `has_backinterview` BIGINT, \
        `has_backinterview_tag` BIGINT, \
        `has_bangbang_tag` BIGINT, \
        `has_interview` BIGINT, \
        `has_manual_enterprise_wechat` BIGINT, \
        `has_manual_wechat` BIGINT, \
        `has_retain_tag` BIGINT, \
        `has_tag` BIGINT, \
        `has_wechat` BIGINT, \
        `homework_submit_count` BIGINT, \
        `interview_type` BIGINT, \
        `is_after_class_phone_back_interview` BIGINT, \
        `is_after_class_phone_back_interview_1min` INT, \
        `is_backinterview_by_phone` BIGINT, \
        `is_interview_by_phone` BIGINT, \
        `is_interview_call_3min` BIGINT, \
        `is_interview_finish` BIGINT, \
        `is_phone_access` BIGINT, \
        `is_phone_cover` BIGINT, \
        `is_refund` BIGINT, \
        `is_test_complete` BIGINT, \
        `is_transfer_student` BIGINT, \
        `is_transfer_student_delayed` BIGINT, \
        `is_wechat_bind` BIGINT, \
        `last_backinterview_time` BIGINT, \
        `lbp_attend_count` BIGINT, \
        `lbp_finish_count` BIGINT, \
        `m_grade` BIGINT, \
        `m_subject` BIGINT, \
        `new_user_type` STRING, \
        `phone_backinterview_count` BIGINT, \
        `playback_cnt_attend_after_unlock_7d` BIGINT, \
        `playback_cnt_finish_after_unlock_7d` BIGINT, \
        `pre_continue` BIGINT, \
        `pre_finish_num` BIGINT, \
        `refund_time` BIGINT, \
        `service_survey_detail` STRING, \
        `start_time` BIGINT, \
        `stop_time` BIGINT, \
        `student_uid` BIGINT, \
        `test_complete_time` BIGINT, \
        `trade_id` BIGINT, \
        `trade_time` BIGINT, \
        `view_count_5m` BIGINT, \
        `wechat_cnt` BIGINT, \
        `wechat_reply_cnt` BIGINT, \
        `wechat_time` BIGINT, \
        `zbk_update_time` INT \
    ) WITH ( \
        'connector'='hologres', \
        'endpoint'='hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80', \
        'username'='LTAI5t5ta8eK5UciPg6fM45e', \
        'password'='******************************', \
        'dbname'='zyb_zbk_bzr', \
        'tablename'='zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant', \
        'mutatetype'='insertOrUpdate', \
        'ignoreDelete'='false', \
        'reserveUpdateBefore'='true' \
    )

flame.table.sink_ads_base_2_ads.sql=INSERT INTO dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_student_assistant \
    SELECT \
        `is_transfer_student_typeB`, \
        `after_test_participate_num`, \
        `after_test_right_num`, \
        `assistant_uid`, \
        `attend_count`, \
        `attend_count_5m`, \
        `attend_count_quarter`, \
        `attend_type_label`, \
        `course_id`, \
        `course_name`, \
        `course_type`, \
        `department`, \
        `effective_wechat_num`, \
        `exam32_total_submit_num`, \
        `finish_num`, \
        `has_auto_enterprise_wechat`, \
        `has_backinterview`, \
        `has_backinterview_tag`, \
        `has_bangbang_tag`, \
        `has_interview`, \
        `has_manual_enterprise_wechat`, \
        `has_manual_wechat`, \
        `has_retain_tag`, \
        `has_tag`, \
        `has_wechat`, \
        `homework_submit_count`, \
        `interview_type`, \
        `is_after_class_phone_back_interview`, \
        `is_after_class_phone_back_interview_1min`, \
        `is_backinterview_by_phone`, \
        `is_interview_by_phone`, \
        `is_interview_call_3min`, \
        `is_interview_finish`, \
        `is_phone_access`, \
        `is_phone_cover`, \
        `is_refund`, \
        `is_test_complete`, \
        `is_transfer_student`, \
        `is_transfer_student_delayed`, \
        `is_wechat_bind`, \
        `last_backinterview_time`, \
        `lbp_attend_count`, \
        `lbp_finish_count`, \
        `m_grade`, \
        `m_subject`, \
        `new_user_type`, \
        `phone_backinterview_count`, \
        `playback_cnt_attend_after_unlock_7d`, \
        `playback_cnt_finish_after_unlock_7d`, \
        `pre_continue`, \
        `pre_finish_num`, \
        `refund_time`, \
        `service_survey_detail`, \
        `start_time`, \
        `stop_time`, \
        `student_uid`, \
        `test_complete_time`, \
        `trade_id`, \
        `trade_time`, \
        `view_count_5m`, \
        `wechat_cnt`, \
        `wechat_reply_cnt`, \
        `wechat_time`, \
        `zbk_update_time`, \
        UNIX_TIMESTAMP() AS zbk_update_time \
    FROM source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_student_assistant
