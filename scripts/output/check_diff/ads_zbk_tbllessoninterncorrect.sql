SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_tbllessoninterncorrect

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_tbllessoninterncorrect

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_tbllessoninterncorrect base
JOIN zyb_zbk_bzr_ads.ads_zbk_tbllessoninterncorrect target
    ON COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."cpu_id", 0) = COALESCE(target."cpu_id", 0)
    AND COALESCE(base."daily_homework_amend_submit_num", 0) = COALESCE(target."daily_homework_amend_submit_num", 0)
    AND COALESCE(base."daily_homework_correct_in_24h_num", 0) = COALESCE(target."daily_homework_correct_in_24h_num", 0)
    AND COALESCE(base."daily_homework_corrected_num", 0) = COALESCE(target."daily_homework_corrected_num", 0)
    AND COALESCE(base."daily_homework_finish_correct_in_24h_num", 0) = COALESCE(target."daily_homework_finish_correct_in_24h_num", 0)
    AND COALESCE(base."daily_homework_finish_correct_in_48h_num", 0) = COALESCE(target."daily_homework_finish_correct_in_48h_num", 0)
    AND COALESCE(base."daily_homework_finish_correct_in_72h_num", 0) = COALESCE(target."daily_homework_finish_correct_in_72h_num", 0)
    AND COALESCE(base."daily_homework_finish_correct_num", 0) = COALESCE(target."daily_homework_finish_correct_num", 0)
    AND COALESCE(base."daily_homework_not_finish_correct_in_18h36h_num", 0) = COALESCE(target."daily_homework_not_finish_correct_in_18h36h_num", 0)
    AND COALESCE(base."daily_homework_not_finish_correct_in_24h_num", 0) = COALESCE(target."daily_homework_not_finish_correct_in_24h_num", 0)
    AND COALESCE(base."daily_homework_not_finish_correct_in_48h_num", 0) = COALESCE(target."daily_homework_not_finish_correct_in_48h_num", 0)
    AND COALESCE(base."daily_homework_not_finish_correct_in_72h_num", 0) = COALESCE(target."daily_homework_not_finish_correct_in_72h_num", 0)
    AND COALESCE(base."daily_homework_review_failed_num", 0) = COALESCE(target."daily_homework_review_failed_num", 0)
    AND COALESCE(base."daily_homework_review_passed_num", 0) = COALESCE(target."daily_homework_review_passed_num", 0)
    AND COALESCE(base."daily_homework_submit_num", 0) = COALESCE(target."daily_homework_submit_num", 0)
    AND COALESCE(base."daily_homework_waiting_for_review_num", 0) = COALESCE(target."daily_homework_waiting_for_review_num", 0)
    AND COALESCE(base."daily_intern_correct_homework_num", 0) = COALESCE(target."daily_intern_correct_homework_num", 0)
    AND COALESCE(base."daily_intern_correct_overtime_question_num", 0) = COALESCE(target."daily_intern_correct_overtime_question_num", 0)
    AND COALESCE(base."daily_intern_correct_question_num", 0) = COALESCE(target."daily_intern_correct_question_num", 0)
    AND COALESCE(base."daily_intern_review_homework_in24h_num", 0) = COALESCE(target."daily_intern_review_homework_in24h_num", 0)
    AND COALESCE(base."daily_intern_review_homework_num", 0) = COALESCE(target."daily_intern_review_homework_num", 0)
    AND COALESCE(base."daily_intern_review_pass_question_num", 0) = COALESCE(target."daily_intern_review_pass_question_num", 0)
    AND COALESCE(base."daily_intern_review_passed_question_num", 0) = COALESCE(target."daily_intern_review_passed_question_num", 0)
    AND COALESCE(base."daily_intern_review_question_num", 0) = COALESCE(target."daily_intern_review_question_num", 0)
    AND COALESCE(base."daily_intern_reviewed_question_num", 0) = COALESCE(target."daily_intern_reviewed_question_num", 0)
    AND COALESCE(base."daily_intern_waiting_for_review_question_num", 0) = COALESCE(target."daily_intern_waiting_for_review_question_num", 0)
    AND COALESCE(base."daily_question_amend_submit_num", 0) = COALESCE(target."daily_question_amend_submit_num", 0)
    AND COALESCE(base."daily_question_correct_overtime_num", 0) = COALESCE(target."daily_question_correct_overtime_num", 0)
    AND COALESCE(base."daily_question_corrected_num", 0) = COALESCE(target."daily_question_corrected_num", 0)
    AND COALESCE(base."daily_question_review_failed_num", 0) = COALESCE(target."daily_question_review_failed_num", 0)
    AND COALESCE(base."daily_question_review_passed_num", 0) = COALESCE(target."daily_question_review_passed_num", 0)
    AND COALESCE(base."daily_question_submit_num", 0) = COALESCE(target."daily_question_submit_num", 0)
    AND COALESCE(base."exam_type", 0) = COALESCE(target."exam_type", 0)
    AND COALESCE(base."intern_correct_uid", 0) = COALESCE(target."intern_correct_uid", 0)
    AND COALESCE(base."lesson_id", 0) = COALESCE(target."lesson_id", 0)
    AND COALESCE(base."outline_id", 0) = COALESCE(target."outline_id", 0)
    AND COALESCE(base."save_time", 0) = COALESCE(target."save_time", 0)