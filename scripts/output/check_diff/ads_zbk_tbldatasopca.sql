-- set statement_timeout = '120min';
-- set hg_experimental_query_hash_join_max_in_memory_rows to 3000000;

-- SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopca

-- UNION ALL

-- SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_tbldatasopca

-- UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopca base
JOIN zyb_zbk_bzr_ads.ads_zbk_tbldatasopca target
    ON COALESCE(base."access_num", 0) = COALESCE(target."access_num", 0)
    AND COALESCE(base."add_backinterview_duration", 0) = COALESCE(target."add_backinterview_duration", 0)
    AND COALESCE(base."add_backinterview_num", 0) = COALESCE(target."add_backinterview_num", 0)
    AND COALESCE(base."add_interview_duration", 0) = COALESCE(target."add_interview_duration", 0)
    AND COALESCE(base."add_interview_num", 0) = COALESCE(target."add_interview_num", 0)
    AND COALESCE(base."after_class_phone_back_interview_1min_num", 0) = COALESCE(target."after_class_phone_back_interview_1min_num", 0)
    AND COALESCE(base."after_class_phone_back_interview_num", 0) = COALESCE(target."after_class_phone_back_interview_num", 0)
    AND COALESCE(base."after_classrefund_num", 0) = COALESCE(target."after_classrefund_num", 0)
    AND COALESCE(base."all_backinterview_num", 0) = COALESCE(target."all_backinterview_num", 0)
    AND COALESCE(base."all_backinterview_standard_num", 0) = COALESCE(target."all_backinterview_standard_num", 0)
    AND COALESCE(base."all_duration", 0) = COALESCE(target."all_duration", 0)
    AND COALESCE(base."all_enterprise_wechat_num", 0) = COALESCE(target."all_enterprise_wechat_num", 0)
    AND COALESCE(base."all_num", 0) = COALESCE(target."all_num", 0)
    AND COALESCE(base."all_wechat_add_num", 0) = COALESCE(target."all_wechat_add_num", 0)
    AND COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."automaticl_enterprise_wechat_num", 0) = COALESCE(target."automaticl_enterprise_wechat_num", 0)
    AND COALESCE(base."back_interview_num", 0) = COALESCE(target."back_interview_num", 0)
    AND COALESCE(base."before2_reg_num", 0) = COALESCE(target."before2_reg_num", 0)
    AND COALESCE(base."before_reg_num", 0) = COALESCE(target."before_reg_num", 0)
    AND COALESCE(base."bound_conversion_reg_num", 0) = COALESCE(target."bound_conversion_reg_num", 0)
    AND COALESCE(base."bound_reserved_reg_num", 0) = COALESCE(target."bound_reserved_reg_num", 0)
    AND COALESCE(base."bound_retain_num", 0) = COALESCE(target."bound_retain_num", 0)
    AND COALESCE(base."change_num", 0) = COALESCE(target."change_num", 0)
    AND COALESCE(base."complete_test_num", 0) = COALESCE(target."complete_test_num", 0)
    AND COALESCE(base."continue_access_num", 0) = COALESCE(target."continue_access_num", 0)
    AND COALESCE(base."continue_label_num", 0) = COALESCE(target."continue_label_num", 0)
    AND COALESCE(base."continue_level_num_1", 0) = COALESCE(target."continue_level_num_1", 0)
    AND COALESCE(base."continue_level_num_2", 0) = COALESCE(target."continue_level_num_2", 0)
    AND COALESCE(base."continue_oncelevel_num", 0) = COALESCE(target."continue_oncelevel_num", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."daily_change", 0) = COALESCE(target."daily_change", 0)
    AND COALESCE(base."daily_not_bound_l2r_num", 0) = COALESCE(target."daily_not_bound_l2r_num", 0)
    AND COALESCE(base."day_3min_back_interview_num", 0) = COALESCE(target."day_3min_back_interview_num", 0)
    AND COALESCE(base."day_3min_continue_num", 0) = COALESCE(target."day_3min_continue_num", 0)
    AND COALESCE(base."day_3min_interview_num", 0) = COALESCE(target."day_3min_interview_num", 0)
    AND COALESCE(base."day_3min_num", 0) = COALESCE(target."day_3min_num", 0)
    AND COALESCE(base."day_3min_wechat_num", 0) = COALESCE(target."day_3min_wechat_num", 0)
    AND COALESCE(base."day_all_backinterview_duration", 0) = COALESCE(target."day_all_backinterview_duration", 0)
    AND COALESCE(base."day_all_backinterview_num", 0) = COALESCE(target."day_all_backinterview_num", 0)
    AND COALESCE(base."day_back_interview_num", 0) = COALESCE(target."day_back_interview_num", 0)
    AND COALESCE(base."day_back_interview_touch_num", 0) = COALESCE(target."day_back_interview_touch_num", 0)
    AND COALESCE(base."day_bound_discount_retain_num", 0) = COALESCE(target."day_bound_discount_retain_num", 0)
    AND COALESCE(base."day_continue_touch_num", 0) = COALESCE(target."day_continue_touch_num", 0)
    AND COALESCE(base."day_interview_num", 0) = COALESCE(target."day_interview_num", 0)
    AND COALESCE(base."day_interview_touch_num", 0) = COALESCE(target."day_interview_touch_num", 0)
    AND COALESCE(base."day_phone_interview_num", 0) = COALESCE(target."day_phone_interview_num", 0)
    AND COALESCE(base."day_touch_num", 0) = COALESCE(target."day_touch_num", 0)
    AND COALESCE(base."day_wechat_access_num", 0) = COALESCE(target."day_wechat_access_num", 0)
    AND COALESCE(base."day_wechat_duration", 0) = COALESCE(target."day_wechat_duration", 0)
    AND COALESCE(base."day_wechat_num", 0) = COALESCE(target."day_wechat_num", 0)
    AND COALESCE(base."day_wechat_touch_num", 0) = COALESCE(target."day_wechat_touch_num", 0)
    AND COALESCE(base."deer_programming_reg_num", 0) = COALESCE(target."deer_programming_reg_num", 0)
    AND COALESCE(base."deer_programming_target_num", 0) = COALESCE(target."deer_programming_target_num", 0)
    AND COALESCE(base."delete_num", 0) = COALESCE(target."delete_num", 0)
    AND COALESCE(base."homevisit_num", 0) = COALESCE(target."homevisit_num", 0)
    AND COALESCE(base."interview_access_num", 0) = COALESCE(target."interview_access_num", 0)
    AND COALESCE(base."interview_must_label_num", 0) = COALESCE(target."interview_must_label_num", 0)
    AND COALESCE(base."interview_num", 0) = COALESCE(target."interview_num", 0)
    AND COALESCE(base."long_season_l2r_num", 0) = COALESCE(target."long_season_l2r_num", 0)
    AND COALESCE(base."manual_enterprise_wechat_num", 0) = COALESCE(target."manual_enterprise_wechat_num", 0)
    AND COALESCE(base."manual_wechat_add_num", 0) = COALESCE(target."manual_wechat_add_num", 0)
    AND COALESCE(base."need_retain_num", 0) = COALESCE(target."need_retain_num", 0)
    AND COALESCE(base."need_retain_user_join_reg_num", 0) = COALESCE(target."need_retain_user_join_reg_num", 0)
    AND COALESCE(base."need_retain_user_l2r_num", 0) = COALESCE(target."need_retain_user_l2r_num", 0)
    AND COALESCE(base."not_bound_discound_l2r_num", 0) = COALESCE(target."not_bound_discound_l2r_num", 0)
    AND COALESCE(base."not_bound_discound_retain_num", 0) = COALESCE(target."not_bound_discound_retain_num", 0)
    AND COALESCE(base."not_bound_discount_num", 0) = COALESCE(target."not_bound_discount_num", 0)
    AND COALESCE(base."not_bound_l2r_discound_retain_num", 0) = COALESCE(target."not_bound_l2r_discound_retain_num", 0)
    AND COALESCE(base."not_continue_num", 0) = COALESCE(target."not_continue_num", 0)
    AND COALESCE(base."not_join_reg_num", 0) = COALESCE(target."not_join_reg_num", 0)
    AND COALESCE(base."not_retain_phone_access_num", 0) = COALESCE(target."not_retain_phone_access_num", 0)
    AND COALESCE(base."not_retain_phone_cover_num", 0) = COALESCE(target."not_retain_phone_cover_num", 0)
    AND COALESCE(base."once_l2r_num", 0) = COALESCE(target."once_l2r_num", 0)
    AND COALESCE(base."phone_back_interview_num", 0) = COALESCE(target."phone_back_interview_num", 0)
    AND COALESCE(base."phone_backinterview_standard_num", 0) = COALESCE(target."phone_backinterview_standard_num", 0)
    AND COALESCE(base."phone_cover_access_num", 0) = COALESCE(target."phone_cover_access_num", 0)
    AND COALESCE(base."phone_cover_num", 0) = COALESCE(target."phone_cover_num", 0)
    AND COALESCE(base."phone_interview_num", 0) = COALESCE(target."phone_interview_num", 0)
    AND COALESCE(base."price_guaranteed_continue_level_num_2", 0) = COALESCE(target."price_guaranteed_continue_level_num_2", 0)
    AND COALESCE(base."price_guaranteed_daily_continue_level_num_2", 0) = COALESCE(target."price_guaranteed_daily_continue_level_num_2", 0)
    AND COALESCE(base."price_guaranteed_join_reg_num", 0) = COALESCE(target."price_guaranteed_join_reg_num", 0)
    AND COALESCE(base."price_guaranteed_num", 0) = COALESCE(target."price_guaranteed_num", 0)
    AND COALESCE(base."questionnaire_num", 0) = COALESCE(target."questionnaire_num", 0)
    AND COALESCE(base."refund_num", 0) = COALESCE(target."refund_num", 0)
    AND COALESCE(base."reg_num_continue", 0) = COALESCE(target."reg_num_continue", 0)
    AND COALESCE(base."reg_num_other", 0) = COALESCE(target."reg_num_other", 0)
    AND COALESCE(base."reg_num_refund", 0) = COALESCE(target."reg_num_refund", 0)
    AND COALESCE(base."retain_num", 0) = COALESCE(target."retain_num", 0)
    AND COALESCE(base."retain_subtrade_id_num", 0) = COALESCE(target."retain_subtrade_id_num", 0)
    AND COALESCE(base."save_time", 0) = COALESCE(target."save_time", 0)
    AND COALESCE(base."service_survey_score_double", 0) = COALESCE(target."service_survey_score_double", 0)
    AND COALESCE(base."should_test_num", 0) = COALESCE(target."should_test_num", 0)
    AND COALESCE(base."single_continue_merge_new1", 0) = COALESCE(target."single_continue_merge_new1", 0)
    AND COALESCE(base."space_season_l2r_bound_num", 0) = COALESCE(target."space_season_l2r_bound_num", 0)
    AND COALESCE(base."space_season_l2r_num", 0) = COALESCE(target."space_season_l2r_num", 0)
    AND COALESCE(base."student_num", 0) = COALESCE(target."student_num", 0)
    AND COALESCE(base."three_phone_backinterview_num", 0) = COALESCE(target."three_phone_backinterview_num", 0)
    AND COALESCE(base."three_phone_interview_num", 0) = COALESCE(target."three_phone_interview_num", 0)
    AND COALESCE(base."transfer_bound_subtrade_id_num", 0) = COALESCE(target."transfer_bound_subtrade_id_num", 0)
    AND COALESCE(base."transfer_status", 0) = COALESCE(target."transfer_status", 0)
    AND COALESCE(base."transfer_subtrade_id_num", 0) = COALESCE(target."transfer_subtrade_id_num", 0)
    AND COALESCE(base."type11_access_num", 0) = COALESCE(target."type11_access_num", 0)
    AND COALESCE(base."user_a_l2r_bound_num", 0) = COALESCE(target."user_a_l2r_bound_num", 0)
    AND COALESCE(base."user_a_l2r_num", 0) = COALESCE(target."user_a_l2r_num", 0)
    AND COALESCE(base."user_a_num", 0) = COALESCE(target."user_a_num", 0)
    AND COALESCE(base."user_b_l2r_bound_num", 0) = COALESCE(target."user_b_l2r_bound_num", 0)
    AND COALESCE(base."user_b_l2r_num", 0) = COALESCE(target."user_b_l2r_num", 0)
    AND COALESCE(base."user_b_num", 0) = COALESCE(target."user_b_num", 0)
    AND COALESCE(base."user_c_l2r_bound_num", 0) = COALESCE(target."user_c_l2r_bound_num", 0)
    AND COALESCE(base."user_c_l2r_num", 0) = COALESCE(target."user_c_l2r_num", 0)
    AND COALESCE(base."user_c_num", 0) = COALESCE(target."user_c_num", 0)
    AND COALESCE(base."user_d_l2r_bound_num", 0) = COALESCE(target."user_d_l2r_bound_num", 0)
    AND COALESCE(base."user_d_l2r_num", 0) = COALESCE(target."user_d_l2r_num", 0)
    AND COALESCE(base."user_d_num", 0) = COALESCE(target."user_d_num", 0)
    AND COALESCE(base."user_l_l2r_bound_num", 0) = COALESCE(target."user_l_l2r_bound_num", 0)
    AND COALESCE(base."user_l_l2r_num", 0) = COALESCE(target."user_l_l2r_num", 0)
    AND COALESCE(base."user_l_num", 0) = COALESCE(target."user_l_num", 0)
    AND COALESCE(base."user_n_l2r_bound_num", 0) = COALESCE(target."user_n_l2r_bound_num", 0)
    AND COALESCE(base."user_n_l2r_num", 0) = COALESCE(target."user_n_l2r_num", 0)
    AND COALESCE(base."user_n_num", 0) = COALESCE(target."user_n_num", 0)
    AND COALESCE(base."user_nl_l2r_bound_num", 0) = COALESCE(target."user_nl_l2r_bound_num", 0)
    AND COALESCE(base."user_nl_l2r_num", 0) = COALESCE(target."user_nl_l2r_num", 0)
    AND COALESCE(base."user_nl_num", 0) = COALESCE(target."user_nl_num", 0)
    AND COALESCE(base."user_type", '') = COALESCE(target."user_type", '')
    AND COALESCE(base."valid_session_count", 0) = COALESCE(target."valid_session_count", 0)
    AND COALESCE(base."visit_label_num", 0) = COALESCE(target."visit_label_num", 0)
    AND COALESCE(base."wechat_add_num", 0) = COALESCE(target."wechat_add_num", 0)
    AND COALESCE(base."wechat_bind_num", 0) = COALESCE(target."wechat_bind_num", 0)
    AND COALESCE(base."wechat_msg_num", 0) = COALESCE(target."wechat_msg_num", 0)
    AND COALESCE(base."wechat_reply_num", 0) = COALESCE(target."wechat_reply_num", 0)
    AND COALESCE(base."wechat_reply_cnt_v1", 0) = COALESCE(target."wechat_reply_cnt_v1", 0)
