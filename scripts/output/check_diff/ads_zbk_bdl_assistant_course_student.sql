SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_bdl_assistant_course_student

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_bdl_assistant_course_student

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_bdl_assistant_course_student base
JOIN zyb_zbk_bzr_ads.ads_zbk_bdl_assistant_course_student target
    ON COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."class_id", 0) = COALESCE(target."class_id", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."reg_time", 0) = COALESCE(target."reg_time", 0)
    AND COALESCE(base."status", 0) = COALESCE(target."status", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)