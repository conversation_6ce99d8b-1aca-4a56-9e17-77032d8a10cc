SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_bdl_course_lesson_info

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_bdl_course_lesson_info

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_bdl_course_lesson_info base
JOIN zyb_zbk_bzr_ads.ads_zbk_bdl_course_lesson_info target
    ON COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."is_main", 0) = COALESCE(target."is_main", 0)
    AND COALESCE(base."lesson_id", 0) = COALESCE(target."lesson_id", 0)
    AND COALESCE(base."main_index", 0) = COALESCE(target."main_index", 0)