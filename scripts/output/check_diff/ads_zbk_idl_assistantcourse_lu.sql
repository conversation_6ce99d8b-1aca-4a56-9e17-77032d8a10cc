SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_assistantcourse_lu

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_assistantcourse_lu

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_assistantcourse_lu base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_assistantcourse_lu target
    ON COALESCE(base."attend_duration", 0) = COALESCE(target."attend_duration", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."is_asscourse_attend", 0) = COALESCE(target."is_asscourse_attend", 0)
    AND COALESCE(base."is_asscourse_finish", 0) = COALESCE(target."is_asscourse_finish", 0)
    AND COALESCE(base."lesson_id", 0) = COALESCE(target."lesson_id", 0)
    AND COALESCE(base."playback_time", 0) = COALESCE(target."playback_time", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)
