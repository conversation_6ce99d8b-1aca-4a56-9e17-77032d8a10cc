SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_bdl_exam_relation_v1

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_bdl_exam_relation_v1

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_bdl_exam_relation_v1 base
JOIN zyb_zbk_bzr_ads.ads_zbk_bdl_exam_relation_v1 target
    ON COALESCE(base."bind_id", 0) = COALESCE(target."bind_id", 0)
    AND COALESCE(base."bind_status", 0) = COALESCE(target."bind_status", 0)
    AND COALESCE(base."bind_type", 0) = COALESCE(target."bind_type", 0)
    AND COALESCE(base."exam_id", 0) = COALESCE(target."exam_id", 0)
    AND COALESCE(base."exam_tag", 0) = COALESCE(target."exam_tag", 0)
    AND COALESCE(base."exam_type", 0) = COALESCE(target."exam_type", 0)
    AND COALESCE(base."is_artificial_correct", 0) = COALESCE(target."is_artificial_correct", 0)
    AND COALESCE(base."question_detail_new", '') = COALESCE(target."question_detail_new", '')
    AND COALESCE(base."relation_type", 0) = COALESCE(target."relation_type", 0)
    AND COALESCE(base."total_num", 0) = COALESCE(target."total_num", 0)