SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_junior_assistant_index

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_adl_referral_activity_junior_assistant_index

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_junior_assistant_index base
JOIN zyb_zbk_bzr_ads.ads_zbk_adl_referral_activity_junior_assistant_index target
    ON COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."config_id", 0) = COALESCE(target."config_id", 0)
    AND COALESCE(base."dt", 0) = COALESCE(target."dt", 0)
    AND COALESCE(base."invitee_uid_bind_pv", 0) = COALESCE(target."invitee_uid_bind_pv", 0)
    AND COALESCE(base."invitee_uid_uv", 0) = COALESCE(target."invitee_uid_uv", 0)
    AND COALESCE(base."inviter_uid_uv", 0) = COALESCE(target."inviter_uid_uv", 0)
    AND COALESCE(base."poster_uv", 0) = COALESCE(target."poster_uv", 0)
    AND COALESCE(base."season", 0) = COALESCE(target."season", 0)
    AND COALESCE(base."student_uid_uv", 0) = COALESCE(target."student_uid_uv", 0)
    AND COALESCE(base."year", 0) = COALESCE(target."year", 0)