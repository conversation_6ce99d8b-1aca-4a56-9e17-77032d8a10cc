SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_tid_answer_info

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_course_stage_student_tid_answer_info

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_tid_answer_info base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_course_stage_student_tid_answer_info target
    ON COALESCE(base."answer_id", '') = COALESCE(target."answer_id", '')
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."exam_id", 0) = COALESCE(target."exam_id", 0)
    AND COALESCE(base."exam_type", 0) = COALESCE(target."exam_type", 0)
    AND COALESCE(base."is_right", 0) = COALESCE(target."is_right", 0)
    AND COALESCE(base."is_submit", 0) = COALESCE(target."is_submit", 0)
    AND COALESCE(base."stage_id", 0) = COALESCE(target."stage_id", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)
    AND COALESCE(base."submit_time", 0) = COALESCE(target."submit_time", 0)
    AND COALESCE(base."tid", 0) = COALESCE(target."tid", 0)
    AND COALESCE(base."tid_idx", 0) = COALESCE(target."tid_idx", 0)