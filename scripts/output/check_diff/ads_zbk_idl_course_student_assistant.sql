-- set statement_timeout = '240min';
-- set hg_experimental_query_hash_join_max_in_memory_rows to 3000000;

SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_course_student_assistant WHERE m_subject=3

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1 WHERE m_subject=3

UNION ALL

SELECT
    COUNT(*)
FROM
    (SELECT * FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_course_student_assistant WHERE m_subject=3) base
JOIN (SELECT * FROM zyb_zbk_bzr_ads.ads_zbk_idl_course_student_assistant_v1 WHERE m_subject=3) target
    ON COALESCE(base."is_transfer_student_typeB", 0) = COALESCE(target."is_transfer_student_typeB", 0)
    AND COALESCE(base."after_test_participate_num", 0) = COALESCE(target."after_test_participate_num", 0)
    AND COALESCE(base."after_test_right_num", 0) = COALESCE(target."after_test_right_num", 0)
    AND COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."attend_count", 0) = COALESCE(target."attend_count", 0)
    AND COALESCE(base."attend_count_5m", 0) = COALESCE(target."attend_count_5m", 0)
    AND COALESCE(base."attend_count_quarter", 0) = COALESCE(target."attend_count_quarter", 0)
    AND COALESCE(base."attend_type_label", '') = COALESCE(target."attend_type_label", '')
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."course_name", '') = COALESCE(target."course_name", '')
    AND COALESCE(base."course_type", 0) = COALESCE(target."course_type", 0)
    AND COALESCE(base."department", 0) = COALESCE(target."department", 0)
    AND COALESCE(base."effective_wechat_num", 0) = COALESCE(target."effective_wechat_num", 0)
    AND COALESCE(base."exam32_total_submit_num", 0) = COALESCE(target."exam32_total_submit_num", 0)
    AND COALESCE(base."finish_num", 0) = COALESCE(target."finish_num", 0)
    AND COALESCE(base."has_auto_enterprise_wechat", 0) = COALESCE(target."has_auto_enterprise_wechat", 0)
    AND COALESCE(base."has_backinterview", 0) = COALESCE(target."has_backinterview", 0)
    AND COALESCE(base."has_backinterview_tag", 0) = COALESCE(target."has_backinterview_tag", 0)
    AND COALESCE(base."has_bangbang_tag", 0) = COALESCE(target."has_bangbang_tag", 0)
    AND COALESCE(base."has_interview", 0) = COALESCE(target."has_interview", 0)
    AND COALESCE(base."has_manual_enterprise_wechat", 0) = COALESCE(target."has_manual_enterprise_wechat", 0)
    AND COALESCE(base."has_manual_wechat", 0) = COALESCE(target."has_manual_wechat", 0)
    AND COALESCE(base."has_retain_tag", 0) = COALESCE(target."has_retain_tag", 0)
    AND COALESCE(base."has_tag", 0) = COALESCE(target."has_tag", 0)
    AND COALESCE(base."has_wechat", 0) = COALESCE(target."has_wechat", 0)
    AND COALESCE(base."homework_submit_count", 0) = COALESCE(target."homework_submit_count", 0)
    AND COALESCE(base."interview_type", 0) = COALESCE(target."interview_type", 0)
    AND COALESCE(base."is_after_class_phone_back_interview", 0) = COALESCE(target."is_after_class_phone_back_interview", 0)
    AND COALESCE(base."is_after_class_phone_back_interview_1min", 0) = COALESCE(target."is_after_class_phone_back_interview_1min", 0)
    AND COALESCE(base."is_backinterview_by_phone", 0) = COALESCE(target."is_backinterview_by_phone", 0)
    AND COALESCE(base."is_interview_by_phone", 0) = COALESCE(target."is_interview_by_phone", 0)
    AND COALESCE(base."is_interview_call_3min", 0) = COALESCE(target."is_interview_call_3min", 0)
    AND COALESCE(base."is_interview_finish", 0) = COALESCE(target."is_interview_finish", 0)
    AND COALESCE(base."is_phone_access", 0) = COALESCE(target."is_phone_access", 0)
    AND COALESCE(base."is_phone_cover", 0) = COALESCE(target."is_phone_cover", 0)
    AND COALESCE(base."is_refund", 0) = COALESCE(target."is_refund", 0)
    AND COALESCE(base."is_test_complete", 0) = COALESCE(target."is_test_complete", 0)
    AND COALESCE(base."is_transfer_student", 0) = COALESCE(target."is_transfer_student", 0)
    AND COALESCE(base."is_transfer_student_delayed", 0) = COALESCE(target."is_transfer_student_delayed", 0)
    AND COALESCE(base."is_wechat_bind", 0) = COALESCE(target."is_wechat_bind", 0)
    AND COALESCE(base."last_backinterview_time", 0) = COALESCE(target."last_backinterview_time", 0)
    AND COALESCE(base."m_grade", 0) = COALESCE(target."m_grade", 0)
    AND COALESCE(base."m_subject", 0) = COALESCE(target."m_subject", 0)
    AND COALESCE(base."new_user_type", '') = COALESCE(target."new_user_type", '')
    AND COALESCE(base."phone_backinterview_count", 0) = COALESCE(target."phone_backinterview_count", 0)
    AND COALESCE(base."playback_cnt_attend_after_unlock_7d", 0) = COALESCE(target."playback_cnt_attend_after_unlock_7d", 0)
    AND COALESCE(base."playback_cnt_finish_after_unlock_7d", 0) = COALESCE(target."playback_cnt_finish_after_unlock_7d", 0)
    AND COALESCE(base."pre_continue", 0) = COALESCE(target."pre_continue", 0)
    AND COALESCE(base."pre_finish_num", 0) = COALESCE(target."pre_finish_num", 0)
    AND COALESCE(base."refund_time", 0) = COALESCE(target."refund_time", 0)
    AND COALESCE(base."service_survey_detail", '') = COALESCE(target."service_survey_detail", '')
    AND COALESCE(base."start_time", 0) = COALESCE(target."start_time", 0)
    AND COALESCE(base."stop_time", 0) = COALESCE(target."stop_time", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)
    AND COALESCE(base."test_complete_time", 0) = COALESCE(target."test_complete_time", 0)
    AND COALESCE(base."trade_id", 0) = COALESCE(target."trade_id", 0)
    AND COALESCE(base."trade_time", 0) = COALESCE(target."trade_time", 0)
    AND COALESCE(base."view_count_5m", 0) = COALESCE(target."view_count_5m", 0)
    AND COALESCE(base."wechat_cnt", 0) = COALESCE(target."wechat_cnt", 0)
    AND COALESCE(base."wechat_reply_cnt", 0) = COALESCE(target."wechat_reply_cnt", 0)
    AND COALESCE(base."wechat_time", 0) = COALESCE(target."wechat_time", 0)
