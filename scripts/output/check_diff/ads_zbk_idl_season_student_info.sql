SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_season_student_info

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_season_student_info

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_season_student_info base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_season_student_info target
    ON COALESCE(base."grade_id", 0) = COALESCE(target."grade_id", 0)
    AND COALESCE(base."pre_subject", '') = COALESCE(target."pre_subject", '')
    AND COALESCE(base."pre_subject_num", '') = COALESCE(target."pre_subject_num", '')
    AND COALESCE(base."season_id", 0) = COALESCE(target."season_id", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)
    AND COALESCE(base."subject", '') = COALESCE(target."subject", '')
    AND COALESCE(base."subject_num", 0) = COALESCE(target."subject_num", 0)
    AND COALESCE(base."year", 0) = COALESCE(target."year", 0)