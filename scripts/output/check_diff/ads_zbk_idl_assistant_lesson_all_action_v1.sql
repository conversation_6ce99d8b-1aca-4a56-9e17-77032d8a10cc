SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_all_action_v1

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_all_action_v1

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_all_action_v1 base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_all_action_v1 target
    ON COALESCE(base."all_index", 0) = COALESCE(target."all_index", 0)
    AND COALESCE(base."assistant_first_access_d_value", 0) = COALESCE(target."assistant_first_access_d_value", 0)
    AND COALESCE(base."assistant_first_access_time", 0) = COALESCE(target."assistant_first_access_time", 0)
    AND COALESCE(base."assistant_last_leave_time", 0) = COALESCE(target."assistant_last_leave_time", 0)
    AND COALESCE(base."assistant_postclass_live_duration", 0) = COALESCE(target."assistant_postclass_live_duration", 0)
    AND COALESCE(base."assistant_preclass_live_duration", 0) = COALESCE(target."assistant_preclass_live_duration", 0)
    AND COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."brand_id", 0) = COALESCE(target."brand_id", 0)
    AND COALESCE(base."course_assistant_bind_detail", '') = COALESCE(target."course_assistant_bind_detail", '')
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."course_name", '') = COALESCE(target."course_name", '')
    AND COALESCE(base."course_type", 0) = COALESCE(target."course_type", 0)
    AND COALESCE(base."courseware_upload_time", 0) = COALESCE(target."courseware_upload_time", 0)
    AND COALESCE(base."deleted", 0) = COALESCE(target."deleted", 0)
    AND COALESCE(base."grade_period", 0) = COALESCE(target."grade_period", 0)
    AND COALESCE(base."is_assistant_postclass_delay", 0) = COALESCE(target."is_assistant_postclass_delay", 0)
    AND COALESCE(base."is_assistant_preclass_late", 0) = COALESCE(target."is_assistant_preclass_late", 0)
    AND COALESCE(base."is_assistant_preclass_redgift", 0) = COALESCE(target."is_assistant_preclass_redgift", 0)
    AND COALESCE(base."is_courseware_upload_timeout", 0) = COALESCE(target."is_courseware_upload_timeout", 0)
    AND COALESCE(base."is_inner", 0) = COALESCE(target."is_inner", 0)
    AND COALESCE(base."is_main", 0) = COALESCE(target."is_main", 0)
    AND COALESCE(base."learn_season", 0) = COALESCE(target."learn_season", 0)
    AND COALESCE(base."lesson_assistant_status", 0) = COALESCE(target."lesson_assistant_status", 0)
    AND COALESCE(base."lesson_deleted", 0) = COALESCE(target."lesson_deleted", 0)
    AND COALESCE(base."lesson_id", 0) = COALESCE(target."lesson_id", 0)
    AND COALESCE(base."lesson_name", '') = COALESCE(target."lesson_name", '')
    AND COALESCE(base."lesson_start_time", 0) = COALESCE(target."lesson_start_time", 0)
    AND COALESCE(base."lesson_stop_time", 0) = COALESCE(target."lesson_stop_time", 0)
    AND COALESCE(base."main_grade", 0) = COALESCE(target."main_grade", 0)
    AND COALESCE(base."main_index", 0) = COALESCE(target."main_index", 0)
    AND COALESCE(base."main_subject", 0) = COALESCE(target."main_subject", 0)
    AND COALESCE(base."restart_id", 0) = COALESCE(target."restart_id", 0)
    AND COALESCE(base."upload_status", 0) = COALESCE(target."upload_status", 0)
    AND COALESCE(base."year", 0) = COALESCE(target."year", 0)