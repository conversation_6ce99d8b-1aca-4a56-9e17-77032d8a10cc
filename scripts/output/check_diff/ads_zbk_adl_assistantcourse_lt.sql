SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_adl_assistantcourse_lt

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_adl_assistantcourse_lt

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_adl_assistantcourse_lt base
JOIN zyb_zbk_bzr_ads.ads_zbk_adl_assistantcourse_lt target
    ON COALESCE(base."asscourse_attend_num", 0) = COALESCE(target."asscourse_attend_num", 0)
    AND COALESCE(base."asscourse_finish_num", 0) = COALESCE(target."asscourse_finish_num", 0)
    AND COALESCE(base."attend_total", 0) = COALESCE(target."attend_total", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."lesson_id", 0) = COALESCE(target."lesson_id", 0)
    AND COALESCE(base."teacher_id", 0) = COALESCE(target."teacher_id", 0)