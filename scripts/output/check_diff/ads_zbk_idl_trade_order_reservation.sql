SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_reservation

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_trade_order_reservation

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_reservation base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_trade_order_reservation target
    ON COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."create_time", 0) = COALESCE(target."create_time", 0)
    AND COALESCE(base."pre_id", 0) = COALESCE(target."pre_id", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)