SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_tblcontinuenotice

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_tblcontinuenotice

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_tblcontinuenotice base
JOIN zyb_zbk_bzr_ads.ads_zbk_tblcontinuenotice target
    ON COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."is_l2_once", 0) = COALESCE(target."is_l2_once", 0)
    AND COALESCE(base."is_l2r", 0) = COALESCE(target."is_l2r", 0)
    AND COALESCE(base."last_l2r_course", 0) = COALESCE(target."last_l2r_course", 0)
    AND COALESCE(base."last_l2r_sub_trade_id", 0) = COALESCE(target."last_l2r_sub_trade_id", 0)
    AND COALESCE(base."last_l2r_time", 0) = COALESCE(target."last_l2r_time", 0)
    AND COALESCE(base."last_refund_l2_time", 0) = COALESCE(target."last_refund_l2_time", 0)
    AND COALESCE(base."learn_season", 0) = COALESCE(target."learn_season", 0)
    AND COALESCE(base."retain_detail", '') = COALESCE(target."retain_detail", '')
    AND COALESCE(base."status", 0) = COALESCE(target."status", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)
    AND COALESCE(base."sub_trade_id", 0) = COALESCE(target."sub_trade_id", 0)
    AND COALESCE(base."trade_time", 0) = COALESCE(target."trade_time", 0)
    AND COALESCE(base."year", 0) = COALESCE(target."year", 0)
    AND COALESCE(base."year_season", '') = COALESCE(target."year_season", '')