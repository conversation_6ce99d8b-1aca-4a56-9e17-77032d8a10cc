-- set statement_timeout = '240min';
-- set hg_experimental_query_hash_join_max_in_memory_rows to 3000000;

SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_assistant_v1 WHERE year=2021

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_trade_order_assistant_v1 WHERE year=2021

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_assistant_v1 base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_trade_order_assistant_v1 target
    ON COALESCE(base."is_transfer_student_typeB", 0) = COALESCE(target."is_transfer_student_typeB", 0)
    AND COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."change_from", 0) = COALESCE(target."change_from", 0)
    AND COALESCE(base."change_status", 0) = COALESCE(target."change_status", 0)
    AND COALESCE(base."change_time", 0) = COALESCE(target."change_time", 0)
    AND COALESCE(base."change_to", 0) = COALESCE(target."change_to", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."create_time", 0) = COALESCE(target."create_time", 0)
    AND COALESCE(base."current_sale_trans_pv", 0) = COALESCE(target."current_sale_trans_pv", 0)
    AND COALESCE(base."current_trans_pv", 0) = COALESCE(target."current_trans_pv", 0)
    AND COALESCE(base."enroll_deer_programming_last_time", 0) = COALESCE(target."enroll_deer_programming_last_time", 0)
    AND COALESCE(base."enroll_deer_writing_last_time", 0) = COALESCE(target."enroll_deer_writing_last_time", 0)
    AND COALESCE(base."is_bound_discount", 0) = COALESCE(target."is_bound_discount", 0)
    AND COALESCE(base."is_bound_discount_retain", 0) = COALESCE(target."is_bound_discount_retain", 0)
    AND COALESCE(base."is_delete", 0) = COALESCE(target."is_delete", 0)
    AND COALESCE(base."is_l1r", 0) = COALESCE(target."is_l1r", 0)
    AND COALESCE(base."is_l2_once", 0) = COALESCE(target."is_l2_once", 0)
    AND COALESCE(base."is_l2r", 0) = COALESCE(target."is_l2r", 0)
    AND COALESCE(base."is_l2r_bound_space_season", 0) = COALESCE(target."is_l2r_bound_space_season", 0)
    AND COALESCE(base."is_l2r_space_season", 0) = COALESCE(target."is_l2r_space_season", 0)
    AND COALESCE(base."is_new", 0) = COALESCE(target."is_new", 0)
    AND COALESCE(base."is_refund_pre", 0) = COALESCE(target."is_refund_pre", 0)
    AND COALESCE(base."is_refund_start", 0) = COALESCE(target."is_refund_start", 0)
    AND COALESCE(base."is_retain", 0) = COALESCE(target."is_retain", 0)
    AND COALESCE(base."is_trans", 0) = COALESCE(target."is_trans", 0)
    AND COALESCE(base."is_trans_once", 0) = COALESCE(target."is_trans_once", 0)
    AND COALESCE(base."is_trans_once_time", 0) = COALESCE(target."is_trans_once_time", 0)
    AND COALESCE(base."is_transfer", 0) = COALESCE(target."is_transfer", 0)
    AND COALESCE(base."is_transfer_student", 0) = COALESCE(target."is_transfer_student", 0)
    AND COALESCE(base."iss_deer_programming", 0) = COALESCE(target."iss_deer_programming", 0)
    AND COALESCE(base."iss_deer_writing", 0) = COALESCE(target."iss_deer_writing", 0)
    AND COALESCE(base."last_refund_l2_time", 0) = COALESCE(target."last_refund_l2_time", 0)
    AND COALESCE(base."learn_season", 0) = COALESCE(target."learn_season", 0)
    AND COALESCE(base."new_user_type", '') = COALESCE(target."new_user_type", '')
    AND COALESCE(base."next_sale_trans_pv", 0) = COALESCE(target."next_sale_trans_pv", 0)
    AND COALESCE(base."next_trans_pv", 0) = COALESCE(target."next_trans_pv", 0)
    AND COALESCE(base."other_trans_pv", 0) = COALESCE(target."other_trans_pv", 0)
    AND COALESCE(base."pre_retain_detail", '') = COALESCE(target."pre_retain_detail", '')
    AND COALESCE(base."pre_status", 0) = COALESCE(target."pre_status", 0)
    AND COALESCE(base."pre_status_autume_spring", 0) = COALESCE(target."pre_status_autume_spring", 0)
    AND COALESCE(base."pre_time", 0) = COALESCE(target."pre_time", 0)
    AND COALESCE(base."province_name", '') = COALESCE(target."province_name", '')
    AND COALESCE(base."refund_pre_time", 0) = COALESCE(target."refund_pre_time", 0)
    AND COALESCE(base."refund_time", 0) = COALESCE(target."refund_time", 0)
    AND COALESCE(base."reserve_infos", '') = COALESCE(target."reserve_infos", '')
    AND COALESCE(base."retain_detail", '') = COALESCE(target."retain_detail", '')
    AND COALESCE(base."sku_id", 0) = COALESCE(target."sku_id", 0)
    AND COALESCE(base."space_season_bound_detail", '') = COALESCE(target."space_season_bound_detail", '')
    AND COALESCE(base."space_season_retain_detail", '') = COALESCE(target."space_season_retain_detail", '')
    AND COALESCE(base."status", 0) = COALESCE(target."status", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)
    AND COALESCE(base."sub_trade_id", 0) = COALESCE(target."sub_trade_id", 0)
    AND COALESCE(base."trade_id", 0) = COALESCE(target."trade_id", 0)
    AND COALESCE(base."trans_detail", '') = COALESCE(target."trans_detail", '')
    AND COALESCE(base."transfer_available_subject", '') = COALESCE(target."transfer_available_subject", '')
    AND COALESCE(base."transfer_bound_subtrade_id_count", 0) = COALESCE(target."transfer_bound_subtrade_id_count", 0)
    AND COALESCE(base."transfer_subtrade_id_count", 0) = COALESCE(target."transfer_subtrade_id_count", 0)
    AND COALESCE(base."year", 0) = COALESCE(target."year", 0)
    AND COALESCE(base."year_season", '') = COALESCE(target."year_season", '')
WHERE base.year=2021
