SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_assistantcourse_cu

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_assistantcourse_cu

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_assistantcourse_cu base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_assistantcourse_cu target
    ON COALESCE(base."asscourse_attend_count", 0) = COALESCE(target."asscourse_attend_count", 0)
    AND COALESCE(base."asscourse_finish_count", 0) = COALESCE(target."asscourse_finish_count", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."is_refund", 0) = COALESCE(target."is_refund", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)