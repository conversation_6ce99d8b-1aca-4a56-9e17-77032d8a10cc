SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_student_action_v1_test

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_student_action_v1_test

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_student_action_v1_test base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_assistant_lesson_student_action_v1_test target
    ON COALESCE(base."all_lesson_index", 0) = COALESCE(target."all_lesson_index", 0)
    AND COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."attend", 0) = COALESCE(target."attend", 0)
    AND COALESCE(base."attend_detail", '') = COALESCE(target."attend_detail", '')
    AND COALESCE(base."attend_duration", 0) = COALESCE(target."attend_duration", 0)
    AND COALESCE(base."attend_long", 0) = COALESCE(target."attend_long", 0)
    AND COALESCE(base."attend_quarter", 0) = COALESCE(target."attend_quarter", 0)
    AND COALESCE(base."boost_lesson_index", 0) = COALESCE(target."boost_lesson_index", 0)
    AND COALESCE(base."chat_num", 0) = COALESCE(target."chat_num", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."course_name", '') = COALESCE(target."course_name", '')
    AND COALESCE(base."course_type", 0) = COALESCE(target."course_type", 0)
    AND COALESCE(base."exam_answer", '') = COALESCE(target."exam_answer", '')
    AND COALESCE(base."hx_alltest_finishnum", 0) = COALESCE(target."hx_alltest_finishnum", 0)
    AND COALESCE(base."hx_pretest_finishnum", 0) = COALESCE(target."hx_pretest_finishnum", 0)
    AND COALESCE(base."inout_count", 0) = COALESCE(target."inout_count", 0)
    AND COALESCE(base."interaction_answer_detail", '') = COALESCE(target."interaction_answer_detail", '')
    AND COALESCE(base."is_assistantcourse_attend", 0) = COALESCE(target."is_assistantcourse_attend", 0)
    AND COALESCE(base."is_assistantcourse_finish", 0) = COALESCE(target."is_assistantcourse_finish", 0)
    AND COALESCE(base."is_attend_finish", 0) = COALESCE(target."is_attend_finish", 0)
    AND COALESCE(base."is_lbp_attend", 0) = COALESCE(target."is_lbp_attend", 0)
    AND COALESCE(base."is_lbp_attend_finish", 0) = COALESCE(target."is_lbp_attend_finish", 0)
    AND COALESCE(base."is_need_attend", 0) = COALESCE(target."is_need_attend", 0)
    AND COALESCE(base."is_need_attend_after_unlock", 0) = COALESCE(target."is_need_attend_after_unlock", 0)
    AND COALESCE(base."is_playback_finish_after_unlock", 0) = COALESCE(target."is_playback_finish_after_unlock", 0)
    AND COALESCE(base."is_playback_finish_after_unlock_7d", 0) = COALESCE(target."is_playback_finish_after_unlock_7d", 0)
    AND COALESCE(base."is_playback_long_after_unlock", 0) = COALESCE(target."is_playback_long_after_unlock", 0)
    AND COALESCE(base."is_playback_long_after_unlock_7d", 0) = COALESCE(target."is_playback_long_after_unlock_7d", 0)
    AND COALESCE(base."is_playback_quarter", 0) = COALESCE(target."is_playback_quarter", 0)
    AND COALESCE(base."is_postclass_attend", 0) = COALESCE(target."is_postclass_attend", 0)
    AND COALESCE(base."is_postclass_finish_attend", 0) = COALESCE(target."is_postclass_finish_attend", 0)
    AND COALESCE(base."is_preclass_attend", 0) = COALESCE(target."is_preclass_attend", 0)
    AND COALESCE(base."is_preclass_finish_attend", 0) = COALESCE(target."is_preclass_finish_attend", 0)
    AND COALESCE(base."is_view_finish_in_14d", 0) = COALESCE(target."is_view_finish_in_14d", 0)
    AND COALESCE(base."is_view_finished", 0) = COALESCE(target."is_view_finished", 0)
    AND COALESCE(base."last_playback_time", 0) = COALESCE(target."last_playback_time", 0)
    AND COALESCE(base."lbp_attend_duration", 0) = COALESCE(target."lbp_attend_duration", 0)
    AND COALESCE(base."learn_season", 0) = COALESCE(target."learn_season", 0)
    AND COALESCE(base."lesson_deleted", 0) = COALESCE(target."lesson_deleted", 0)
    AND COALESCE(base."lesson_id", 0) = COALESCE(target."lesson_id", 0)
    AND COALESCE(base."lesson_name", '') = COALESCE(target."lesson_name", '')
    AND COALESCE(base."lesson_start_time", 0) = COALESCE(target."lesson_start_time", 0)
    AND COALESCE(base."lesson_status", 0) = COALESCE(target."lesson_status", 0)
    AND COALESCE(base."lesson_stop_time", 0) = COALESCE(target."lesson_stop_time", 0)
    AND COALESCE(base."lesson_type", 0) = COALESCE(target."lesson_type", 0)
    AND COALESCE(base."lesson_unlock_time", 0) = COALESCE(target."lesson_unlock_time", 0)
    AND COALESCE(base."main_department", 0) = COALESCE(target."main_department", 0)
    AND COALESCE(base."main_grade", 0) = COALESCE(target."main_grade", 0)
    AND COALESCE(base."main_lesson_index", 0) = COALESCE(target."main_lesson_index", 0)
    AND COALESCE(base."main_subject", 0) = COALESCE(target."main_subject", 0)
    AND COALESCE(base."new_user_type", '') = COALESCE(target."new_user_type", '')
    AND COALESCE(base."photo_assistant_uid", 0) = COALESCE(target."photo_assistant_uid", 0)
    AND COALESCE(base."playback_participate_num", 0) = COALESCE(target."playback_participate_num", 0)
    AND COALESCE(base."playback_right_num", 0) = COALESCE(target."playback_right_num", 0)
    AND COALESCE(base."playback_time", 0) = COALESCE(target."playback_time", 0)
    AND COALESCE(base."playback_time_after_unlock", 0) = COALESCE(target."playback_time_after_unlock", 0)
    AND COALESCE(base."playback_time_after_unlock_7d", 0) = COALESCE(target."playback_time_after_unlock_7d", 0)
    AND COALESCE(base."playback_time_in_14d", 0) = COALESCE(target."playback_time_in_14d", 0)
    AND COALESCE(base."playback_time_in_7d", 0) = COALESCE(target."playback_time_in_7d", 0)
    AND COALESCE(base."postclass_attend_duration", 0) = COALESCE(target."postclass_attend_duration", 0)
    AND COALESCE(base."pre_attend", 0) = COALESCE(target."pre_attend", 0)
    AND COALESCE(base."preclass_attend_duration", 0) = COALESCE(target."preclass_attend_duration", 0)
    AND COALESCE(base."restart_id", 0) = COALESCE(target."restart_id", 0)
    AND COALESCE(base."student_attend_label", '') = COALESCE(target."student_attend_label", '')
    AND COALESCE(base."student_interaction_label", '') = COALESCE(target."student_interaction_label", '')
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)
    AND COALESCE(base."sub_trade_id", 0) = COALESCE(target."sub_trade_id", 0)
    AND COALESCE(base."teacher_id", 0) = COALESCE(target."teacher_id", 0)
    AND COALESCE(base."trade_change_status", 0) = COALESCE(target."trade_change_status", 0)
    AND COALESCE(base."trade_change_time", 0) = COALESCE(target."trade_change_time", 0)
    AND COALESCE(base."trade_create_time", 0) = COALESCE(target."trade_create_time", 0)
    AND COALESCE(base."trade_refund_time", 0) = COALESCE(target."trade_refund_time", 0)
    AND COALESCE(base."trade_status", 0) = COALESCE(target."trade_status", 0)
    AND COALESCE(base."year", 0) = COALESCE(target."year", 0)