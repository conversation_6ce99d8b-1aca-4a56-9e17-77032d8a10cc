set statement_timeout = '240min';
set hg_experimental_query_hash_join_max_in_memory_rows to 3000000;

SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopcal WHERE save_time=20211221

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_tbldatasopcal WHERE save_time=20211221

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopcal base
JOIN zyb_zbk_bzr_ads.ads_zbk_tbldatasopcal target
    ON COALESCE(base."homework_SorA_num", 0) = COALESCE(target."homework_SorA_num", 0)
    AND COALESCE(base."after_unlock_7d_attend_playback_num", 0) = COALESCE(target."after_unlock_7d_attend_playback_num", 0)
    AND COALESCE(base."after_unlock_7d_finish_playback_num", 0) = COALESCE(target."after_unlock_7d_finish_playback_num", 0)
    AND COALESCE(base."after_unlock_attend_playback_num", 0) = COALESCE(target."after_unlock_attend_playback_num", 0)
    AND COALESCE(base."after_unlock_finish_playback_num", 0) = COALESCE(target."after_unlock_finish_playback_num", 0)
    AND COALESCE(base."after_unlock_need_attend_num", 0) = COALESCE(target."after_unlock_need_attend_num", 0)
    AND COALESCE(base."after_unlock_playback_num", 0) = COALESCE(target."after_unlock_playback_num", 0)
    AND COALESCE(base."ai_attend_num", 0) = COALESCE(target."ai_attend_num", 0)
    AND COALESCE(base."ai_attend_total", 0) = COALESCE(target."ai_attend_total", 0)
    AND COALESCE(base."ai_class_finish_num", 0) = COALESCE(target."ai_class_finish_num", 0)
    AND COALESCE(base."all_time_homework_submit_num", 0) = COALESCE(target."all_time_homework_submit_num", 0)
    AND COALESCE(base."all_view_num", 0) = COALESCE(target."all_view_num", 0)
    AND COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."assistantcourse_attend_finish_num", 0) = COALESCE(target."assistantcourse_attend_finish_num", 0)
    AND COALESCE(base."assistantcourse_attend_num", 0) = COALESCE(target."assistantcourse_attend_num", 0)
    AND COALESCE(base."assistantcourse_need_attend_num", 0) = COALESCE(target."assistantcourse_need_attend_num", 0)
    AND COALESCE(base."attend_num", 0) = COALESCE(target."attend_num", 0)
    AND COALESCE(base."attend_num_5m", 0) = COALESCE(target."attend_num_5m", 0)
    AND COALESCE(base."attend_num_quarter", 0) = COALESCE(target."attend_num_quarter", 0)
    AND COALESCE(base."attend_one_third_num", 0) = COALESCE(target."attend_one_third_num", 0)
    AND COALESCE(base."attend_total", 0) = COALESCE(target."attend_total", 0)
    AND COALESCE(base."attend_two_third_num", 0) = COALESCE(target."attend_two_third_num", 0)
    AND COALESCE(base."challenge_attend_right_num", 0) = COALESCE(target."challenge_attend_right_num", 0)
    AND COALESCE(base."challenge_attend_total_num", 0) = COALESCE(target."challenge_attend_total_num", 0)
    AND COALESCE(base."challenge_finish_num", 0) = COALESCE(target."challenge_finish_num", 0)
    AND COALESCE(base."challenge_total_num", 0) = COALESCE(target."challenge_total_num", 0)
    AND COALESCE(base."chat_num", 0) = COALESCE(target."chat_num", 0)
    AND COALESCE(base."class_finish_num", 0) = COALESCE(target."class_finish_num", 0)
    AND COALESCE(base."corrections_homework_num48", 0) = COALESCE(target."corrections_homework_num48", 0)
    AND COALESCE(base."corrections_homework_num72", 0) = COALESCE(target."corrections_homework_num72", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."exam33_similar_question_amend_num", 0) = COALESCE(target."exam33_similar_question_amend_num", 0)
    AND COALESCE(base."exam33_similar_question_submit_num", 0) = COALESCE(target."exam33_similar_question_submit_num", 0)
    AND COALESCE(base."exam33_submit_and_unamend_num", 0) = COALESCE(target."exam33_submit_and_unamend_num", 0)
    AND COALESCE(base."exam33_wrong_similar_expound_video_view_num", 0) = COALESCE(target."exam33_wrong_similar_expound_video_view_num", 0)
    AND COALESCE(base."exam7_submit_and_unamend_num", 0) = COALESCE(target."exam7_submit_and_unamend_num", 0)
    AND COALESCE(base."exam7_wrong_expound_video_view_num", 0) = COALESCE(target."exam7_wrong_expound_video_view_num", 0)
    AND COALESCE(base."homework_amend_num", 0) = COALESCE(target."homework_amend_num", 0)
    AND COALESCE(base."homework_amend_num_in_14d", 0) = COALESCE(target."homework_amend_num_in_14d", 0)
    AND COALESCE(base."homework_num", 0) = COALESCE(target."homework_num", 0)
    AND COALESCE(base."homework_recorrection_num", 0) = COALESCE(target."homework_recorrection_num", 0)
    AND COALESCE(base."homework_submit_3_times_or_amend_num", 0) = COALESCE(target."homework_submit_3_times_or_amend_num", 0)
    AND COALESCE(base."homework_tid_first_correct_cnt", 0) = COALESCE(target."homework_tid_first_correct_cnt", 0)
    AND COALESCE(base."homework_tid_first_right_cnt", 0) = COALESCE(target."homework_tid_first_right_cnt", 0)
    AND COALESCE(base."interactive_participate_num", 0) = COALESCE(target."interactive_participate_num", 0)
    AND COALESCE(base."interactive_right_num", 0) = COALESCE(target."interactive_right_num", 0)
    AND COALESCE(base."interactive_total_num", 0) = COALESCE(target."interactive_total_num", 0)
    AND COALESCE(base."is_follow", 0) = COALESCE(target."is_follow", 0)
    AND COALESCE(base."is_follow_a", 0) = COALESCE(target."is_follow_a", 0)
    AND COALESCE(base."lesson_finish_num", 0) = COALESCE(target."lesson_finish_num", 0)
    AND COALESCE(base."lesson_id", 0) = COALESCE(target."lesson_id", 0)
    AND COALESCE(base."lesson_test_finish_num", 0) = COALESCE(target."lesson_test_finish_num", 0)
    AND COALESCE(base."lesson_test_participate_num", 0) = COALESCE(target."lesson_test_participate_num", 0)
    AND COALESCE(base."lesson_test_right_num", 0) = COALESCE(target."lesson_test_right_num", 0)
    AND COALESCE(base."lesson_test_total_num", 0) = COALESCE(target."lesson_test_total_num", 0)
    AND COALESCE(base."lesson_total_num", 0) = COALESCE(target."lesson_total_num", 0)
    AND COALESCE(base."niudao_finish_num", 0) = COALESCE(target."niudao_finish_num", 0)
    AND COALESCE(base."niudao_total_num", 0) = COALESCE(target."niudao_total_num", 0)
    AND COALESCE(base."playback_all_num", 0) = COALESCE(target."playback_all_num", 0)
    AND COALESCE(base."playback_participate_num", 0) = COALESCE(target."playback_participate_num", 0)
    AND COALESCE(base."playback_right_num", 0) = COALESCE(target."playback_right_num", 0)
    AND COALESCE(base."postclass_attend_num", 0) = COALESCE(target."postclass_attend_num", 0)
    AND COALESCE(base."postclass_finish_attend_num", 0) = COALESCE(target."postclass_finish_attend_num", 0)
    AND COALESCE(base."pre_attend_is5_num", 0) = COALESCE(target."pre_attend_is5_num", 0)
    AND COALESCE(base."pre_attend_num", 0) = COALESCE(target."pre_attend_num", 0)
    AND COALESCE(base."preclass_attend_num", 0) = COALESCE(target."preclass_attend_num", 0)
    AND COALESCE(base."preclass_finish_attend_num", 0) = COALESCE(target."preclass_finish_attend_num", 0)
    AND COALESCE(base."preview_bef_fin_num", 0) = COALESCE(target."preview_bef_fin_num", 0)
    AND COALESCE(base."preview_finish_num", 0) = COALESCE(target."preview_finish_num", 0)
    AND COALESCE(base."reg_num", 0) = COALESCE(target."reg_num", 0)
    AND COALESCE(base."save_time", 0) = COALESCE(target."save_time", 0)
    AND COALESCE(base."stage_test_finish_num", 0) = COALESCE(target."stage_test_finish_num", 0)
    AND COALESCE(base."transfer_status", 0) = COALESCE(target."transfer_status", 0)
    AND COALESCE(base."user_type", '') = COALESCE(target."user_type", '')
    AND COALESCE(base."view_finish_num", 0) = COALESCE(target."view_finish_num", 0)
    AND COALESCE(base."view_finish_num_in_14d", 0) = COALESCE(target."view_finish_num_in_14d", 0)
    AND COALESCE(base."view_num", 0) = COALESCE(target."view_num", 0)
    AND COALESCE(base."view_num_5m", 0) = COALESCE(target."view_num_5m", 0)
    AND COALESCE(base."view_num_quarter", 0) = COALESCE(target."view_num_quarter", 0)
WHERE base.save_time=20211221
