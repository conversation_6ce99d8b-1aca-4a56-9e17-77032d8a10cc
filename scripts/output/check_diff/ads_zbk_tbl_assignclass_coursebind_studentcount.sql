SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_tbl_assignclass_coursebind_studentcount

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_tbl_assignclass_coursebind_studentcount

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_tbl_assignclass_coursebind_studentcount base
JOIN zyb_zbk_bzr_ads.ads_zbk_tbl_assignclass_coursebind_studentcount target
    ON COALESCE(base."all_cnt", 0) = COALESCE(target."all_cnt", 0)
    AND COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."continue_cnt", 0) = COALESCE(target."continue_cnt", 0)
    AND COALESCE(base."continue_error_cnt", 0) = COALESCE(target."continue_error_cnt", 0)
    AND COALESCE(base."course_grade", 0) = COALESCE(target."course_grade", 0)
    AND COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."course_is_inner", 0) = COALESCE(target."course_is_inner", 0)
    AND COALESCE(base."course_subject", 0) = COALESCE(target."course_subject", 0)
    AND COALESCE(base."course_type", 0) = COALESCE(target."course_type", 0)
    AND COALESCE(base."delay_transfer_new_cnt", 0) = COALESCE(target."delay_transfer_new_cnt", 0)
    AND COALESCE(base."delay_transfer_old_cnt", 0) = COALESCE(target."delay_transfer_old_cnt", 0)
    AND COALESCE(base."deleted", 0) = COALESCE(target."deleted", 0)
    AND COALESCE(base."new_cnt", 0) = COALESCE(target."new_cnt", 0)
    AND COALESCE(base."no_transfer_new_cnt", 0) = COALESCE(target."no_transfer_new_cnt", 0)
    AND COALESCE(base."no_transfer_old_cnt", 0) = COALESCE(target."no_transfer_old_cnt", 0)
    AND COALESCE(base."nomal_transfer_new_cnt", 0) = COALESCE(target."nomal_transfer_new_cnt", 0)
    AND COALESCE(base."normal_transfer_old_cnt", 0) = COALESCE(target."normal_transfer_old_cnt", 0)
    AND COALESCE(base."old_cnt", 0) = COALESCE(target."old_cnt", 0)
    AND COALESCE(base."person_uid", 0) = COALESCE(target."person_uid", 0)
    AND COALESCE(base."pull_new_duty", 0) = COALESCE(target."pull_new_duty", 0)
    AND COALESCE(base."season", 0) = COALESCE(target."season", 0)
    AND COALESCE(base."season_num", 0) = COALESCE(target."season_num", 0)
    AND COALESCE(base."season_year", 0) = COALESCE(target."season_year", 0)
    AND COALESCE(base."series_new_cnt", 0) = COALESCE(target."series_new_cnt", 0)
    AND COALESCE(base."single_new_cnt", 0) = COALESCE(target."single_new_cnt", 0)