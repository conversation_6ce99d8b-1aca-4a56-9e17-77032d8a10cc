SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_action

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_idl_course_stage_student_action

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_action base
JOIN zyb_zbk_bzr_ads.ads_zbk_idl_course_stage_student_action target
    ON COALESCE(base."course_id", 0) = COALESCE(target."course_id", 0)
    AND COALESCE(base."exam27", '') = COALESCE(target."exam27", '')
    AND COALESCE(base."exam35", '') = COALESCE(target."exam35", '')
    AND COALESCE(base."exam36", '') = COALESCE(target."exam36", '')
    AND COALESCE(base."exam37", '') = COALESCE(target."exam37", '')
    AND COALESCE(base."exam38", '') = COALESCE(target."exam38", '')
    AND COALESCE(base."exam39", '') = COALESCE(target."exam39", '')
    AND COALESCE(base."exam40", '') = COALESCE(target."exam40", '')
    AND COALESCE(base."exam41", '') = COALESCE(target."exam41", '')
    AND COALESCE(base."exam42", '') = COALESCE(target."exam42", '')
    AND COALESCE(base."exam43", '') = COALESCE(target."exam43", '')
    AND COALESCE(base."exam44", '') = COALESCE(target."exam44", '')
    AND COALESCE(base."exam45", '') = COALESCE(target."exam45", '')
    AND COALESCE(base."exam46", '') = COALESCE(target."exam46", '')
    AND COALESCE(base."is_need_participate", 0) = COALESCE(target."is_need_participate", 0)
    AND COALESCE(base."is_stage_finish", 0) = COALESCE(target."is_stage_finish", 0)
    AND COALESCE(base."stage_id", 0) = COALESCE(target."stage_id", 0)
    AND COALESCE(base."stage_release_time", 0) = COALESCE(target."stage_release_time", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)