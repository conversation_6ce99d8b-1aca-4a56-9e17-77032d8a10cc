SELECT COUNT(*) FROM zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_aas

UNION ALL

SELECT COUNT(*) FROM zyb_zbk_bzr_ads.ads_zbk_adl_referral_activity_aas

UNION ALL

SELECT
    COUNT(*)
FROM
    zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_aas base
JOIN zyb_zbk_bzr_ads.ads_zbk_adl_referral_activity_aas target
    ON COALESCE(base."assistant_uid", 0) = COALESCE(target."assistant_uid", 0)
    AND COALESCE(base."bind_status", 0) = COALESCE(target."bind_status", 0)
    AND COALESCE(base."config_id", 0) = COALESCE(target."config_id", 0)
    AND COALESCE(base."is_poster", 0) = COALESCE(target."is_poster", 0)
    AND COALESCE(base."new_user_pv", 0) = COALESCE(target."new_user_pv", 0)
    AND COALESCE(base."new_user_uv", 0) = COALESCE(target."new_user_uv", 0)
    AND COALESCE(base."student_uid", 0) = COALESCE(target."student_uid", 0)