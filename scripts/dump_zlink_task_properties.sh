#!/bin/bash
# 从zlink平台批量下载任务文件
# 1、获取任务id列表 http://zlink.zuoyebang.cc/zlink/api/task/tree 结果返回写入tree.json
# jq .data[0].children[1].children[].id tree.json > tasks.txt
# 2、批量导出
readonly cookie=""
readonly version="3.3"
readonly OUTPUT_DIR="output"

if [ -z "$cookie" ]; then
    echo "cookie required"
    exit -1
fi

if [ ! -e "$OUTPUT_DIR" ]; then
  mkdir -p $OUTPUT_DIR
fi

tmpfile=$(mktemp)
while read line
do
  curl "http://zlink.zuoyebang.cc/zlink/api/task/${line}" \
    -H 'Connection: keep-alive' \
    -H 'Pragma: no-cache' \
    -H 'Cache-Control: no-cache' \
    -H 'Access-Control-Allow-Origin: *' \
    -H 'Accept: application/json, text/plain, */*' \
    -H 'DNT: 1' \
    -H 'redirect-path: http://zlink.zuoyebang.cc/#/develop' \
    -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36' \
    -H 'ips-service: http://zlink.zuoyebang.cc/#/develop' \
    -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7' \
    -H "version: ${version}" \
    -H "Cookie: ${cookie}" \
    --compressed \
    --insecure \
    -o $tmpfile

  filename=$(jq .data.name $tmpfile | sed 's#"##g')
  content=$(jq .data.config $tmpfile | sed 's#^"##g; s#"$##g')

  if [ ! -z "$filename" ]; then
    # echo会把多个空格变成一个，缩进样式丢失了，用双引号包起来就正常了
    # 解决方案：https://stackoverflow.com/questions/29378566/i-just-assigned-a-variable-but-echo-variable-shows-something-else
    echo "$content" > "${OUTPUT_DIR}/${filename}.properties"
  fi
done

rm $tmpfile
