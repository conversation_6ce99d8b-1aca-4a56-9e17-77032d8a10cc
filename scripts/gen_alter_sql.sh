#!/bin/bash
# 修改Hologres表结构Alter语句
if [ -z "$1" ]; then
    echo "生成修改Hologres表结构ALTER语句\n"
    echo "sh $0 < column2type.txt。字段格式：一行一个，可参考column2type.txt"
    echo "e.g sh $0 < column2type.txt"
    exit -1
fi

ret=""

while read line
do
    column=$(echo $line | cut -d' ' -f 1)
    type=$(echo $line | cut -d' ' -f 2)

    ret="ADD COLUMN \""${column}"\" ${type}, ${ret}"
done

echo $ret | sed 's#,$#;#g'
