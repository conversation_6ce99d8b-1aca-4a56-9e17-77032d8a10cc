#!/bin/bash
# 功能：生成重建表的核心语句
readonly ROOT_DIR=$(dirname $(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd))

if [ -z "$1" ]; then
    echo "重建表辅助脚本\n"
    echo "sh $0 scheme.table[需要重建的表]"
    echo "e.g sh $0 zyb_zbk_bzr_ads.idl_xxx"
    exit -1
fi

# cut的话，echo a | cut -d'.' -f 2 会输出a
scheme=$(echo $1 | awk -F "." '{print $1}')
table=$(echo $1 | awk -F "." '{print $2}')
if [ -z "$table" ];then
    table=$scheme
    scheme="zyb_zbk_bzr_ads"
fi

temp_table="${table}_temp"
# https://stackoverflow.com/questions/1167746/how-to-assign-a-heredoc-value-to-a-variable-in-bash
cat <<EOF
###########生成的SQL如下###########

-- 停写（停同步任务），然后导入数据
INSERT INTO ${scheme}.${temp_table} SELECT * FROM ${scheme}.${table};

-- 数据导入完成后，修改表名及视图
BEGIN;
  -- 备份原表
  ALTER TABLE ${scheme}.${table} RENAME TO ${table}_backup;
  -- 新表重命名
  ALTER TABLE ${scheme}.${temp_table} RENAME TO ${table};
  -- 更新视图，少了这个步骤，视图指向的还是老表
  CREATE OR REPLACE VIEW ${scheme}.${table}_view AS SELECT * FROM ${scheme}.${table};
END;
EOF
