#!/bin/env python
import sys
import pathlib
from simple_ddl_parser import parse_from_file

if len(sys.argv) <= 1:
    print("""Usage:
    python3 {} ddl.txt

根据Hologres DDL生成zlink实时计算平台的任务SQL以及全量同步Hologres表的SQL，并将结果输出到output文件夹下
    """.format(sys.argv[0]))
    sys.exit(-1)

pathlib.Path("output/zlink").mkdir(parents=True, exist_ok=True)
pathlib.Path("output/sync").mkdir(parents=True, exist_ok=True)

# 类型对应关系
# https://nightlies.apache.org/flink/flink-docs-release-1.12/dev/table/types.html
# https://help.aliyun.com/document_detail/130398.html
data_types = {
    "integer": "INT",
    "int4": "INT",
    "bigint": "BIGINT",
    "int8": "BIGINT",
    "text": "STRING",
    "decimal": "DECIMAL",
    "numeric": "DECIMAL",
}

dest_schema="zyb_zbk_bzr_ads"

zlink_task_tpl = """-- 声明用到的所有表
CREATE TABLE source_{schema}_{table} (
    `hg_binlog_event_type` BIGINT,
    {columns_ddl}
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${{hologres.endpoint}}:${{hologres.port}}',
    'username' = '${{hologres.username}}',
    'password' = '${{hologres.password}}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = '{schema}.{table}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_{dest_schema}_{table} (
    {columns_ddl}
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${{hologres.endpoint}}:${{hologres.port}}',
    'username' = '${{hologres.username}}',
    'password' = '${{hologres.password}}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = '{dest_schema}.{table}',
    'mutatetype' = 'insertOrUpdate',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_{dest_schema}_{table}
SELECT
    {columns},
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_{schema}_{table}
WHERE
    `hg_binlog_event_type` <> 3;
"""

# 显式指定下字段，避免两张表的字段顺序不一致
full_sync_sql_tpl="""EXPLAIN INSERT INTO {dest_schema}.{table} (
   {columns}
) SELECT
   {columns}
FROM {schema}.{table}
"""

results = parse_from_file(sys.argv[1])

for result in results:
    # print(table)
    schema, table = result["schema"], result["table_name"]

    if schema and table and len(result["columns"]) > 0:
        columns = sorted(result["columns"], key=lambda column:column["name"])

        ddl_list = []
        sink_list = []
        for column in columns:
            type = column["type"]
            # 部分字段包含大写字母，会用双引号包起来
            name = column["name"].replace("\"", "")
            if type in data_types:
                flink_data_type = data_types[type]
            else:
                flink_data_type = "<{}>".format(type)
                print("Unknown data type {}".format(type), file=sys.stderr)

            ddl_list.append("`{}` {}".format(name, flink_data_type))
            sink_list.append("`{}`".format(name))

        # zlink任务
        with open("output/zlink/{}.properties".format(table).replace("ads_zbk_", ""), "w") as f:
            zlink_rtsql = zlink_task_tpl.format(
                schema=schema,
                dest_schema=dest_schema,
                table=table,
                columns_ddl=",\n    ".join(ddl_list),
                columns=",\n    ".join(sink_list),
            )

            f.write(zlink_rtsql)

        # 生成全量导入sql
        with open("output/sync/{}.sql".format(table), "w") as f:
            full_sync_sql = full_sync_sql_tpl.format(
                table=table,
                columns=",\n   ".join(sink_list).replace("`", "\""),
                schema=schema,
                dest_schema=dest_schema
            )

            f.write(full_sync_sql)
