#!/bin/env python
import sys
import pathlib
from simple_ddl_parser import parse_from_file

if len(sys.argv) <= 1:
    print("""Usage:
    python3 {} ddl.txt

根据Hologres DDL生成diff SQL，并将结果输出到output文件夹下
    """.format(sys.argv[0]))
    sys.exit(-1)

pathlib.Path("output/check_diff").mkdir(parents=True, exist_ok=True)

diff_sql_tpl = """-- 若报内存超限，可以设置这两个参数，或者改成两个子查询间join，抽部分数据进行diff
-- set statement_timeout = '120min';
-- set hg_experimental_query_hash_join_max_in_memory_rows to 30000;

SELECT COUNT(*) FROM {schema}.{table}

UNION ALL

SELECT COUNT(*) FROM {target_schema}.{table}

UNION ALL

SELECT
    COUNT(*)
FROM
    {schema}.{table} base
JOIN {target_schema}.{table} target
    ON {condition}"""

skip_columns = {
    "zbk_update_time": 0,
    "update_time": 0,
}

results = parse_from_file(sys.argv[1])
for result in results:
    schema, table = result["schema"], result["table_name"]

    if schema and table and len(result["columns"]) > 0:
        columns = sorted(result["columns"], key=lambda column:column["name"])

        conditions = []
        for column in columns:
            type = column["type"]
            # 部分字段包含大写字母，会用双引号包起来
            name = column["name"].replace("\"", "")

            if name not in skip_columns:
                if "integer" == type or "bigint" == type:
                    val = 0
                if "text" == type:
                    val = "''"

                conditions.append(
                    "COALESCE(base.\"{column}\", {val}) = COALESCE(target.\"{column}\", {val})".format(column=name, val=val)
                )

        # 生成全量导入sql
        with open("output/check_diff/{}.sql".format(table), "w") as f:
            f.write(
                diff_sql_tpl.format(
                    schema="zyb_zbk_bzr_ads_base",
                    target_schema="zyb_zbk_bzr_ads",
                    table=table,
                    condition="\n    AND ".join(conditions)
                )
            )
