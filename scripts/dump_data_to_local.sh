#!/bin/bash
# 从Hologres导出最多指定条数（默认1w条）数据至本地当前output目录下
# 参考文档：https://help.aliyun.com/document_detail/151708.html
readonly ROOT_DIR=$(dirname $(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd))

source $ROOT_DIR/conf/hologres.conf

# 默认导出1w条
count=10000
if [ ! -z $1 ]; then
    count=$1
fi
if [ ! -d "output" ];then
    mkdir output
fi

while read table
do
    echo "dump ${table}"
    # *号注意转义
    command='\copy (select * from'" ${table} limit ${count}) to 'output/${table}.${count}.sample.txt';"
    PGPASSWORD=$password psql -h $endpoint -p $port -U $username -d $dbname -c "$command"
done
