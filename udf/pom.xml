<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.zyb.udf</groupId>
  <artifactId>udf-parent</artifactId>
  <packaging>pom</packaging>
  <version>1.0-SNAPSHOT</version>
  <name>ZYB UDF Parent Project</name>

  <modules>
    <module>flink</module>
    <module>hive</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <java.version>1.8</java.version>
    <junit-jupiter.version>5.5.2</junit-jupiter.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <skip.assembly>true</skip.assembly>
  </properties>

  <!-- junit 5 -->
  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>${junit-jupiter.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <!-- https://stackoverflow.com/questions/34188861/maven-multimodule-project-can-i-jar-with-dependencies -->
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptorRefs>
            <descriptorRef>jar-with-dependencies</descriptorRef>
          </descriptorRefs>
          <skipAssembly>${skip.assembly}</skipAssembly>
        </configuration>
        <!-- <executions> -->
        <!--   <execution> -->
        <!--     <id>make-assembly</id> -->
        <!--     <phase>package</phase> -->
        <!--     <goals> -->
        <!--       <goal>single</goal> -->
        <!--     </goals> -->
        <!--   </execution> -->
        <!-- </executions> -->
      </plugin>
    </plugins>
  </build>
</project>
