/**
 * 办公网出口IP（V4）查询工区信息
 */
package com.streaming.flink.udf;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;

public class IP2Location {
    public class Result {
        String city;
        String area;

        public Result(String city, String area) {
            this.city = city;
            this.area = area;
        }

        public String getCity() {
            return city;
        }

        public String getArea() {
            return area;
        }
    }

    private static IP2Location instance = null;

    private Map<String, Result> database = new HashMap<String, Result>();

    private IP2Location(){}

    public static IP2Location getInstance() {
        System.out.print("ip2Location");
        if (null == instance) {
            instance = new IP2Location();
            instance.Init();
        }

        return instance;
    }

    // 加载配置文件
    public boolean Init() {
        System.out.print("load ip2Location dict");
        try {
            // https://git.zuoyebang.cc/xiongchao/felog-consumer
            InputStream input = this.getClass().getClassLoader().getResourceAsStream("ip2location.tsv");
            Scanner scanner = new Scanner(input);

            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                String[] list = line.split("\t");

                if (list.length == 3) {
                    Result result = new Result(list[1], list[2]);

                    database.put(list[0], result);
                }
            }
            scanner.close();
        } catch (Exception e) {
            return false;
        }

        return true;
    }

    public Result Query(String ip) {
        ip = ip.trim();

        if (database.containsKey(ip)) {
            return database.get(ip);
        }

        return null;
    }
}
