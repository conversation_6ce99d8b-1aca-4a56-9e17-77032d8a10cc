package com.streaming.flink.parser;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ODPLogParser {

    // 原始日志格式：
    // Original format string: %L: %{%m-%d %H:%M:%S}t %{app}x * %{pid}x [logid=%l
    // requestId=%r spanid=%{spanid}x force_sampling=%{force_sampling}x filename=%f
    // lineno=%N errno=%{err_no}x %{encoded_str_array}x errmsg=%{u_err_msg}x]
    // NOTICE: 04-17 15:47:47 assistantdesk * 13 [k=value(urlencode) ...]
    private static final Pattern LOG_PATTERN = Pattern
            .compile("^(\\w+):\\s(\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2})\\s(\\w+)\\s\\*\\s(?:\\d+)\\s\\[(.*)\\]");

    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("文件路径未提供");
            return;
        }

        String filePath = args[0];
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                ODPLogEntry logEntry = parse(line);
                if (logEntry != null) {
                    System.out.println(logEntry.toString());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 将ODP模块日志解析成结构化数据
     *
     * @param 日志字符串
     * @return {@link ODPLogEntry}
     */
    public static ODPLogEntry parse(String line) throws IllegalArgumentException {
        line = line.trim();
        if (line == null || line.isEmpty()) {
            throw new IllegalArgumentException("日志内容不能为空");
        }
        Matcher matcher = LOG_PATTERN.matcher(line);
        if (!matcher.matches()) {
            throw new IllegalArgumentException("日志格式不正确");
        }

        String level = matcher.group(1).trim();
        String date = matcher.group(2).trim();
        String module = matcher.group(3).trim();
        String content = matcher.group(4).trim();
        if (level.isEmpty() || date.isEmpty()) {
            throw new IllegalArgumentException("日志格式不正确。日志级别或时间不能为空");
        }

        // 自定义字段
        String[] kvpairs = content.split(" ");
        Map<String, String> customFields = new HashMap<>();
        for (int i = 0; i < kvpairs.length; i++) {
            String[] list = kvpairs[i].split("=", 2);
            if (list.length >= 2) {
                String key = list[0];
                String value = "";
                try {
                    value = URLDecoder.decode(list[1], "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    System.out.println(e);
                    continue;
                }

                if (!customFields.containsKey(key)) {
                    customFields.put(key, value);
                }
            }
        }

        // 公共字段
        long timestamp = getLogTimestamp(date);
        String uri = customFields.get("url");
        String referer = customFields.get("refer");
        String filename = customFields.get("filename");
        String lineno = customFields.get("lineno");
        String message = customFields.get("errmsg");

        ODPLogEntry logEntry = new ODPLogEntry(level, timestamp, module, filename, toInt(lineno, 0));
        logEntry.setRequestUri(extractPathFromUri(uri));
        logEntry.setReferer(extractPathFromUri(referer));
        logEntry.setCustomFields(customFields);
        logEntry.setMessage(message);

        return logEntry;
    }

    /**
     * 将字符串转换为整数，转换失败则返回指定的默认值
     *
     * @param str          要转换的字符串
     * @param defaultValue 如果转换失败时返回的默认值
     * @return 转换后的整数或默认值
     */
    private static int toInt(String str, int defaultValue) {
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private static String extractPathFromUri(String uri) {
        if (uri == null || uri.isEmpty()) {
            return "";
        }

        int queryIndex = uri.indexOf('?');
        if (queryIndex != -1) {
            return uri.substring(0, queryIndex);
        }

        return uri;
    }

    /**
     * 将日期字符串转换成时间戳
     *
     * omp日志里没有年份，补全下年份。默认补当前年份，跨年的情况取前一年
     * e.g "04-17 15:47:48"
     *
     * @param dateStr 要转换的日期字符串
     * @return 转换后的时间戳，转换失败返回0
     */
    private static long getLogTimestamp(String dateStr) {
        if (dateStr == "" || dateStr.isEmpty()) {
            return 0;
        }

        try {
            LocalDateTime now = LocalDateTime.now();
            int currentYear = now.getYear();
            String fullDateStr = currentYear + "-" + dateStr;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime parsedTime = LocalDateTime.parse(fullDateStr, formatter);
            // 如果解析出的时间在当前时间之后，则将年份减去1，跨年的情况
            if (parsedTime.isAfter(now)) {
                parsedTime = parsedTime.minusYears(1);
            }

            return parsedTime.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
        } catch (Exception e) {
            return 0;
        }
    }
}
