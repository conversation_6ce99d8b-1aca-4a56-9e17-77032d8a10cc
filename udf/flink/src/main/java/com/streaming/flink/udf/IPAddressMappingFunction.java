/**
 * 办公网出口IP（V4）查询工区信息
 */
package com.streaming.flink.udf;

import java.net.UnknownHostException;

import com.streaming.flink.utils.IPAddressMapping;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

public class IPAddressMappingFunction extends ScalarFunction {
    private IPAddressMapping ipAddressMapping;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        this.ipAddressMapping = IPAddressMapping.getInstance();
    }

    /**
     * @param str 内网ip地址
     * @return 内网地址对应工区公网出口ip
     */
    public String eval() {
        return "内网ip映射为对应工区办公网出口ip";
    }

    /**
     * @param str 内网ip地址
     * @return 内网地址对应工区公网出口ip，找不到返回0.0.0.0
     */
    public String eval(String str) {
        try {
            return ipAddressMapping.getPublicIP(str);
        } catch (UnknownHostException e) {
            return "0.0.0.0";
        }
    }
}
