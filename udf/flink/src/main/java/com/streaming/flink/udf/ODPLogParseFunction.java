/**
 * ODP模块日志parser udf
 *
 * 参考文档： https://nightlies.apache.org/flink/flink-docs-release-1.12/dev/table/functions/udfs.html
 */
package com.streaming.flink.udf;

import com.streaming.flink.parser.ODPLogEntry;
import com.streaming.flink.parser.ODPLogParser;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

@FunctionHint(output = @DataTypeHint("ROW<log_level STRING, event_time BIGINT, module STRING, filename STRING, lineno INT, request_uri STRING, referer STRING, custom MAP<STRING, STRING>, message STRING>"))
public class ODPLogParseFunction extends TableFunction<Row> {
    // Help
    public void eval() {
        collect(Row.of("ODP模块日志ETL程序。支持module.log.new日志", null, null, null, null, null, null, null, null));
    }

    public void eval(String s) {
        if (s == null || s.isEmpty()) {
            collect(null);
        } else {
            try {
                ODPLogEntry entry = ODPLogParser.parse(s);
                collect(Row.of(entry.getLevel(), entry.getTimestamp(), entry.getModule(), entry.getFileName(),
                        entry.getLineNo(), entry.getRequestUri(), entry.getReferer(), entry.getCustomFields(),
                        entry.getMessage()));
            } catch (IllegalArgumentException e) {
                collect(null);
            }
        }
    }
}
