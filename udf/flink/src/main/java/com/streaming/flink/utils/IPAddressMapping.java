package com.streaming.flink.utils;

import java.io.InputStream;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * 内网ip转换为公网ip
 *
 * 内网cidr格式ip展开后，数据量比较大，50w+，写SQL join ip配置表统计不方便，这里换成对应工区公网出口ip
 *
 *
 * 内网ip：https://docs.zuoyebang.cc/doc?fileId=1872631987043840412
 * https://docs.zuoyebang.cc/doc?fileId=1909608823611711490
 */
public class IPAddressMapping {
    private static IPAddressMapping instance = null;
    private List<CIDRUtils> database = new ArrayList<>();

    private IPAddressMapping() {
    }

    public static IPAddressMapping getInstance() {
        if (null == instance) {
            instance = new IPAddressMapping();
            instance.Init();
        }
        return instance;
    }

    // 加载配置文件
    public boolean Init() {
        System.out.println("load internal network ip conf");
        try {
            // 表头：region,access_point,inter_cidr,export_ip
            InputStream input = this.getClass().getClassLoader()
                    .getResourceAsStream("zyb_internal_network_ip2location.tsv");
            Scanner scanner = new Scanner(input);
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                String[] list = line.split("\t");

                if (list.length == 4) {
                    try {
                        CIDRUtils cidr = new CIDRUtils(list[2], list[3]);
                        database.add(cidr);
                    } catch (Exception e) {
                        System.out.println("invalid cidr address. line: " + line);
                    }
                }
            }
            scanner.close();
        } catch (Exception e) {
            System.out.println("catch exception: " + e.getMessage());
            return false;
        }

        return true;
    }

    /**
     * getPublicIP 找到对应公网ip
     *
     * 数据量多了可以改用二分查找
     */
    public String getPublicIP(String ipAddress) throws UnknownHostException {
        for (int i = 0; i < database.size(); i++) {
            if (database.get(i).isInRange(ipAddress)) {
                return database.get(i).getLocation();
            }
        }
        return "0.0.0.0";
    }
}
