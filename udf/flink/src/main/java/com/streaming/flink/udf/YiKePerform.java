/**
 * yikeperform.gif日志ETL UDTF
 *
 * 参考文档： https://nightlies.apache.org/flink/flink-docs-release-1.12/dev/table/functions/udfs.html
 */
package com.streaming.flink.udf;

import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;

import com.streaming.flink.udf.IP2Location.Result;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.json.JSONObject;

@FunctionHint(output = @DataTypeHint("ROW<authkey STRING, url STRING, name STRING, type STRING, errorType STRING, message STRING, cookie STRING, ua STRING, dateTime STRING, clientIP STRING, location MAP<STRING, STRING>, custom MAP<STRING, STRING>, urlParams MAP<STRING, STRING>, indexSuffix STRING>"))
public class YiKePerform extends TableFunction<Row> {

    private IP2Location ip2Location;

    private Map<String, String> authKeyMap = new HashMap<String, String>();

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        ip2Location = IP2Location.getInstance();

        authKeyMap.put("fe/pc-work-station", "一线工作站");
        authKeyMap.put("fe/assistant-pangu", "批改");
    }

    // Help
    public void eval() {
        collect(Row.of("yikefeperform.gif日志ETL程序", null, null, null, null, null, null, null, null, null, null, null, null));
    }

    public void eval(String s) {
        Map<String, Object> ret = parser(s);

        if (ret != null && !ret.isEmpty()) {
            collect(Row.of(ret.get("authkey"), ret.get("url"), ret.get("name"), ret.get("type"), ret.get("errorType"), ret.get("message"), ret.get("cookie"), ret.get("ua"), ret.get("date"), ret.get("clientIP"), ret.get("location"), ret.get("custom"), ret.get("urlParams"), ret.get("indexSuffix")));
        }
        // else {
        //     collect(Row.of("日志格式错误", s, null, null, null, null, null, null, null, null, null, null));
        // }
    }

    public Map<String, Object> parser(String line) {
        Map<String, Object> ret = new HashMap<String, Object>();
        String[] list = line.split("\t");
        if (list.length != 8) {
            return null;
        }
        // trim [、]
        for (int i = 0; i < list.length; i++) {
            list[i] = list[i].trim();

            if (list[i].startsWith("[")) {
                list[i] = list[i].substring(1);
            }
            if (list[i].endsWith("]")) {
                list[i] = list[i].substring(0, (list[i].length()-1));
            }
        }

        String clientIP = list[0];
        String date = list[1];
        String query = list[3];
        String cookie = list[4];
        String ua = list[5];
        String url = list[2].split(" ")[1];
        if (!query.equals("-")) {
            url = url + "&" + query;
        }

        try {
            URL url2 = new URL("https://nlogtj.zuoyebang.cc" + url);
            String qs = URLDecoder.decode(url2.getQuery(), StandardCharsets.UTF_8.name());
            Map<String, String> queryMap = splitStr(qs, "&", "=");
            queryMap.remove("agent");
            String authKey = queryMap.get("authkey");
            // 有其他业务往这个gif里面写数据，这里根据authkey挑出我们的日志
            if (!authKeyMap.containsKey(authKey)) {
                return null;
            }

            Map<String, String> custom = new HashMap<String, String>();
            // 不在公共字段里的，全部放custome字段里
            ret.put("type", "-");
            ret.put("errorType", "-");
            ret.put("url", "-");
            ret.put("name", "-");
            ret.put("message", "-");
            ret.put("urlParams", "");

            for (String k: queryMap.keySet()) {
                // skip empty string
                k = k.trim();
                // es不支持空串作为key
                if (!k.equals("")) {
                    if (ret.containsKey(k)) {
                        ret.put(k, queryMap.get(k));
                    } else {
                        custom.put(k, queryMap.get(k));
                    }
                }
            }
            ret.put("custom", custom);

            if (ret.get("urlParams").equals("")) {
                ret.put("urlParams", null);
            } else {
                Map<String, String> urlParams = new HashMap<String, String>();
                JSONObject jsonObj = new JSONObject(queryMap.get("urlParams"));
                Iterator<String> it = jsonObj.keys();

                while (it.hasNext()) {
                    String k = it.next();
                    k = k.trim();
                    if (!k.equals("")) {
                        urlParams.put(k, jsonObj.getString(k));
                    }
                }
                ret.put("urlParams", urlParams);
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MMM/yyyy:HH:mm:ss Z", Locale.US);
            DateTimeFormatter zonedFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ");
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(date, formatter);
            // ip 反查工区
            Result result = ip2Location.Query(clientIP);
            Map<String, String> location = new HashMap<String, String>();
            if (result == null) {
                location.put("city", "未知");
                location.put("area", "未知");
            } else {
                location.put("city", result.getCity());
                location.put("area", result.getArea());
            }

            // 固定字段，防止被覆盖
            ret.put("authkey", authKey);
            ret.put("cookie", cookie);
            ret.put("ua", ua);
            ret.put("date", zonedDateTime.format(zonedFormatter));
            ret.put("clientIP", clientIP);
            ret.put("location", location);
            /**
             * 单独加一个字段用于动态生成索引名
             * TIMESTAMP类型写到es里的时间（date类型）不带时区，不能通过毫秒查询
             * 动态Index https://nightlies.apache.org/flink/flink-docs-release-1.12/dev/table/connectors/elasticsearch.html#dynamic-index
             */
            ret.put("indexSuffix", zonedDateTime.format(dateFormatter));
        } catch(Exception e) {
            return null;
        }

        return ret;
    }

    public static Map<String, String> splitStr(String s, String sep1, String sep2) {
        Map<String, String> ret = new HashMap<String, String>();
        if ("" == s) {
            return ret;
        }

        String[] list = s.split(sep1);
        for (int i = 0; i < list.length; i++) {
            String[] tmp = list[i].split(sep2, 2);

            if (2 == tmp.length) {
                ret.put(tmp[0].trim(), tmp[1].trim());
            } else {
                ret.put(tmp[0].trim(), "");
            }
        }

        return ret;
    }
}
