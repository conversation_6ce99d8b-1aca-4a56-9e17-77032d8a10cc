package com.streaming.flink.parser;

import java.util.HashMap;
import java.util.Map;

public class ODPLogEntry {
    private String level; // 日志级别
    private long timestamp; // 时间戳
    private String module; // 模块
    private String filename; // 文件名
    private int lineno; // 行号
    private String requestUri; // 请求URI
    private String referer; // 来源URL
    private Map<String, String> customFields; // 自定义字段
    private String message; // 日志信息

    // 构造函数
    public ODPLogEntry(String level, long timestamp, String module, String filename, int lineno) {
        this.level = level;
        this.timestamp = timestamp;
        this.module = module;
        this.filename = filename;
        this.lineno = lineno;
        this.customFields = new HashMap<>();
    }

    public String getLevel() {
        return level;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public String getModule() {
        return module;
    }

    public String getFileName() {
        return filename;
    }

    public int getLineNo() {
        return lineno;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public String getMessage() {
        return message;
    }

    // 获取所有自定义字段
    public Map<String, String> getCustomFields() {
        return customFields;
    }

    public String getReferer() {
        return referer;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    public void setCustomFields(Map<String, String> customFields) {
        this.customFields = customFields;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("level=").append(getLevel());
        sb.append("; timestamp=").append(getTimestamp());
        sb.append("; module=").append(getModule());
        sb.append("; filename=").append(getFileName());
        sb.append("; fileno=").append(getLineNo());
        sb.append("; request_uri=").append(requestUri);
        sb.append("; referer=").append(referer);
        sb.append("; custom=").append(customFields.toString());
        sb.append("; message=").append(message);
        return sb.toString();
    }
}
