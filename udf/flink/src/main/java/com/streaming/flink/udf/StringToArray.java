/**
 * 使用正则表达式切分字符串，并返回数组
 */
package com.streaming.flink.udf;

import org.apache.flink.table.functions.ScalarFunction;

public class StringToArray extends ScalarFunction {
    /**
     * @param str 待切分的字符串，使用英文逗号分隔
     * @return 字符串数组，空串或null则返回空数组
     */
    public String[] eval(String str) {
        return split(str, ",");
    }

    /**
     * @param str 待切分的字符串
     * @param regex 正则表达式，正则表达式里有特殊含义的字符的字面量用作分隔符时需转义
     * @return 字符串数组，空串或null则返回空数组
     */
    public String[] eval(String str, String regex) {
        return split(str, regex);
    }

    public static String[] split(String str, String regex) {
        if (str == null || str.trim().isEmpty() || regex == null) {
            return new String[0];
        }
        return str.trim().split(regex);
    }
}
