/**
 * idl_lesson_student_action表ExamAnswer字段（源表里是string类型的json串）decode成Map<String, Map<String, String>>结构
 * 支持2层的Map，部分字段不是string，也会存成string类型
 */
package com.streaming.flink.udf;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.apache.flink.table.functions.ScalarFunction;
import org.json.JSONObject;

public class ExamAnswer extends ScalarFunction {
    /**
     * @param 至少2层嵌套json，一级path对应的值可以是null，2级path下还有json的话，会返回字符串。例如：
     *  {
     *    "exam1": null,
     *    "exam7": {
     *      "amend_time": null,
     *      "amend_valid_time": null,
     *      "correct_level": 11,
     *      "ext": {
     *        "depth": 3
     *      }
     *    }
     *  }
     * @return
     */
    public Map<String, Map<String, String>> eval(String s) {
        if (s == null) {
            return null;
        }

        Map<String, Map<String, String>> ret = new HashMap<>();
        JSONObject jsonObj = new JSONObject(s);
        JSONObject jsonTmp = null;
        Iterator<String> keys = null;
        Iterator<String> it = jsonObj.keys();

        while (it.hasNext()) {
            String k = it.next();

            if (jsonObj.isNull(k)) {
                ret.put(k, null);
            } else {
                jsonTmp = jsonObj.getJSONObject(k);
                keys = jsonTmp.keys();
                Map<String, String> tmp = new HashMap<>();

                while (keys.hasNext()) {
                    String kk = keys.next();

                    if (jsonTmp.isNull(kk)) {
                        tmp.put(kk, null);
                    } else {
                        tmp.put(kk, jsonTmp.get(kk).toString());
                    }
                }
                ret.put(k, tmp);
            }
        }

        return ret;
    }
}
