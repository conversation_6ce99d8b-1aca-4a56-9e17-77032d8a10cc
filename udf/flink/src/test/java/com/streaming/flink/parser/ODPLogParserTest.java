package com.streaming.flink.parser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.Map;

import org.junit.jupiter.api.Test;

public class ODPLogParserTest {
    protected void runTestException(String value, String message) {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            ODPLogParser.parse(value);
        });

        assertEquals(message, exception.getMessage());
    }

    @Test
    public void normal() {
        String line = "NOTICE: 04-17 15:47:47 assistantdesk * 13 [logid=2866760332 requestId=1b5c1631eedb8397:4b31c58664020f34:1b5c1631eedb8397:1 spanid=0 force_sampling=0 filename=/mnt/homework/php/phplib/saf/base/Log.php lineno=28 errno=0 optime=1744876067.1276 client_ip=************ local_ip=************* product=homework subsys=question module=assistantdesk uniqid=0 cgid=0 uid=0 innerNetwork=1 ncmCount=9%2C7%2C2 ipsEnv=test LoginType=2 imgPid=qa_65be52d22871c8d490d4957cc9b5f000.png idc=test time=%7B%27ts_all%27%3A363%2C%27ts_init%27%3A98%2C%27rpc_ips_POST%27%3A66%2C%27rpc_userprofile_POST%27%3A23%2C%27ts_invoke%27%3A265%2C%27rpc_tower_POST%27%3A18%2C%27rpc_zbcore_dal_POST%27%3A66%2C%27rpc_moat_POST%27%3A87%2C%27rpc_coursebase_GET%27%3A5%2C%27multi_rpc%27%3A68%2C%27rpc_allocate_POST%27%3A7%2C%27ts_display%27%3A0%7D request_param=%7B%22year%22%3A%222025%22%7D netWork=%7B%27inNetwork%27%3Atrue%2C%27testNetwork%27%3Atrue%7D userName=%E6%B5%8B%E8%AF%95%E7%9C%9F%E4%BA%BA%E8%B4%A6%E5%8F%B755 api_module=task api_action=courselistandcardbyyear pro_errno=0 pro_un=%E6%B5%8B%E8%AF%95%E7%9C%9F%E4%BA%BA%E8%B4%A6%E5%8F%B755 pro_uid=2474119418 pro_deviceUid=4310486648 app_version=v1 pv=task%2Fv1%2Fcourselistandcardbyyear AssistantDesk_ActionCommon=1 isPressure=0 resource_stat=%7B%22http_zbcore_dal%22%3A%7B%22max%22%3A23509%2C%22count%22%3A3%2C%22total%22%3A66642%7D%2C%22mysql%22%3A%7B%22max%22%3A978%2C%22count%22%3A1%2C%22total%22%3A978%7D%7D un= mobilephone= email= baiduid= url=%2Fassistantdesk%2Fdeskv1%2Ftask%2Fcourselistandcardbyyear%3Fyear%3D2025 refer=https%3A%2F%2Fassistantdesk-abc-cc.suanshubang.cc%2Fassistantdesk%2Fview%2Fassistant-first-line-teacher-v2%2Ffirst-line-teacher%2Ftask%2Fcrm-task-v2 uip=************ ua=Mozilla%2F5.0%20%28Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7%29%20AppleWebKit%2F537.36%20%28KHTML%2C%20like%20Gecko%29%20Chrome%2F135.0.0.0%20Safari%2F537.36 host=assistantdesk-abc-cc.suanshubang.cc req_start_time=1744876066.7608 reqStartTime=1744876066.7608 cost=366 errmsg=notice]";

        ODPLogEntry entry = ODPLogParser.parse(line);
        assertEquals(entry.getLevel(), "NOTICE");
        assertEquals(entry.getTimestamp(), 1744876067);
        assertEquals(entry.getModule(), "assistantdesk");
        assertEquals(entry.getFileName(), "/mnt/homework/php/phplib/saf/base/Log.php");
        assertEquals(entry.getLineNo(), 28);
        assertEquals(entry.getMessage(), "notice");
        assertEquals(entry.getRequestUri(), "/assistantdesk/deskv1/task/courselistandcardbyyear");
        assertEquals(entry.getReferer(),
                "https://assistantdesk-abc-cc.suanshubang.cc/assistantdesk/view/assistant-first-line-teacher-v2/first-line-teacher/task/crm-task-v2");
        Map<String, String> custom = entry.getCustomFields();
        assertEquals(custom.size(), 50);
        assertEquals(custom.get("uip"), "************");
        assertEquals(custom.get("pro_deviceUid"), "4310486648");
    }

    @Test
    public void testParamsError() {
        runTestException("", "日志内容不能为空");
        runTestException("   ", "日志内容不能为空");

        runTestException("DEBUG: ", "日志格式不正确");
        runTestException(
                "NOTICE: 04-17 15:47:47 assistantdesk * 13 [logid=2866760332 requestId=1b5c1631eedb8397 ",
                "日志格式不正确");
        runTestException(
                " : 04-37 01:47:47 assistantdesk * 13 [logid=2866760332 requestId=1b5c1631eedb8397 ",
                "日志格式不正确");
        runTestException(
                " : assistantdesk * 13 [logid=2866760332 requestId=1b5c1631eedb8397]",
                "日志格式不正确");
    }
}
