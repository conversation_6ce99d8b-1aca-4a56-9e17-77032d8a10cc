package com.streaming.flink.udf;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;

public class StringToArrayTest {
    protected void runTestSplitByComma(String value, String[] exp, StringToArray udf) {
        String[] result = udf.eval(value);

        assertNotNull(result);
        assertArrayEquals(exp, result);
    }

    protected void runTestSplitByRegex(String value, String[] exp, String regex, StringToArray udf) {
        String[] result = udf.eval(value, regex);

        assertNotNull(result);
        assertArrayEquals(exp, result);
    }

    @Test
    public void testSplitByComma() {
        runTestSplitByComma(null, new String[0], new StringToArray());
        runTestSplitByComma(new String(""), new String[0], new StringToArray());
        runTestSplitByComma(new String(" "), new String[0], new StringToArray());
        runTestSplitByComma(new String("z"), new String[]{"z"}, new StringToArray());
        runTestSplitByComma(new String(" z "), new String[]{"z"}, new StringToArray());
        runTestSplitByComma(new String(" z"), new String[]{"z"}, new StringToArray());
        runTestSplitByComma(new String("z, y"), new String[]{"z", " y"}, new StringToArray());
        runTestSplitByComma(new String("z,y,b"), new String[]{"z", "y", "b"}, new StringToArray());
    }

    // @Test
    // public void testTestSplitByRegex() {
    //     runTest(null, new String[0], new StringToArray());
    //     runTest(new String(""), new String[0], new StringToArray());
    //     runTest(new String(" "), new String[0], new StringToArray());
    //     runTest(new String("z"), new String[]{"z"}, new StringToArray());
    //     runTest(new String("z "), new String[]{"z"}, new StringToArray());
    //     runTest(new String(" z"), new String[]{"z"}, new StringToArray());
    //     runTest(new String("z,y,b"), new String[]{"z", "y", "b"}, new StringToArray());
    // }
}
