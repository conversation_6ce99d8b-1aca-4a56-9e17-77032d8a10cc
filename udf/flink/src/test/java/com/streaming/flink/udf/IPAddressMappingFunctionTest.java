package com.streaming.flink.udf;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;

public class IPAddressMappingFunctionTest {
    protected void runTest(String value, String exp) {
        IPAddressMappingFunction udf = new IPAddressMappingFunction();
        String result = udf.eval(value);

        assertNotNull(result);
        assertEquals(exp, result);
    }

    @Test
    public void testIPAddressMapping() {
        // 和配置文件有关
        // runTest("**********", "**************");
        // runTest("***********", "**************");
        // runTest("*************", "*************");
    }
}
