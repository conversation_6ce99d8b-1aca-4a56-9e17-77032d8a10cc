package main

import "C"

import (
	"git.zuoyebang.cc/pkg/golib/v2/utils"
)

// 以动态链接库的形式供Java调用
//
// 解密函数输出范围/加密函数输入范围参考
// 基础架构的Go实现的pack https://git.zuoyebang.cc/pkg/golib/-/blob/master/utils/php_internal.go
// php pack https://www.php.net/manual/en/function.pack

//export DecodeQid
func DecodeQid(s string, qType int) (int, *C.char) {
	id, err := utils.DecodeQid(s, qType)
	if err != nil {
		return 0, C.CString(err.Error())
	}
	return id, nil
}

//export DecodeAQid
func DecodeAQid(s string) (int, *C.char) {
	id, err := utils.DecodeAQid(s)
	if err != nil {
		return 0, C.CString(err.Error())
	}
	return id, nil
}

//export DecodeUid
func DecodeUid(s string) (int, *C.char) {
	id, err := utils.DecodeUid(s)
	if err != nil {
		return 0, C.CString(err.Error())
	}
	return id, nil
}

//export DecodeCid
func DecodeCid(s string) (int, *C.char) {
	id, err := utils.DecodeCid(s)
	if err != nil {
		return 0, C.CString(err.Error())
	}
	return id, nil
}

//export DecodeLid
func DecodeLid(s string) (int, *C.char) {
	id, err := utils.DecodeLid(s)
	if err != nil {
		return 0, C.CString(err.Error())
	}
	return id, nil
}

//export DecodeOuid
func DecodeOuid(s string) (int, *C.char) {
	id, err := utils.DecodeOuid(s)
	if err != nil {
		return 0, C.CString(err.Error())
	}
	return id, nil
}

// required
func main() {}
