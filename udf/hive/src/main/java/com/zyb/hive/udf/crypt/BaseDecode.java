package com.zyb.hive.udf.crypt;

import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.StringObjectInspector;
import org.apache.hadoop.io.LongWritable;

public abstract class BaseDecode extends GenericUDF {
    private final LongWritable result = new LongWritable();
    private transient StringObjectInspector inputOI0;

    // 初始化的时候校验参数数量及类型
    @Override
    public ObjectInspector initialize(ObjectInspector[] arg0) throws UDFArgumentException {
        if (arg0.length != 1) {
            throw new UDFArgumentLengthException(getFuncName() + " requires 1 argument, got " + arg0.length);
        }
        // create an ObjectInspector for the input
        ObjectInspector input0 = arg0[0];
        // check to make sure the input is a string
        if (!(input0 instanceof StringObjectInspector)) {
            throw new UDFArgumentTypeException(0, getFuncName() + " only takes STRING types, got " + input0.getTypeName());
        }
        this.inputOI0 = (StringObjectInspector) input0;

        // 返回值类型
        return PrimitiveObjectInspectorFactory.writableLongObjectInspector;
    }

    @Override
    public Object evaluate(DeferredObject[] arg0) throws HiveException {
        Object input0 = arg0[0].get();
        if (input0 == null) {
            return null;
        }

        String cipher = this.inputOI0.getPrimitiveJavaObject(input0).toString();
        Long id = decode(cipher);
        if (id == null) {
            return null;
        }
        result.set(id);
        return result;
    }

    @Override
    public String getDisplayString(String[] children) {
        assert (children.length == 1);
        return getStandardDisplayString(getFuncName(), children);
    }

    protected abstract Long decode(String cipher);
}
