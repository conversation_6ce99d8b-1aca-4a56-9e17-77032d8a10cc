package com.zyb.hive.udf.crypt;

import com.zyb.go.crypt.IDCrypt;

import org.apache.hadoop.hive.ql.exec.Description;

@Description(name = "decode_aqid", value = "_FUNC_(str) - decode Hk_Util_IdCrypt::encodeAQid/golib.utils.EncodeAQid output")
public class DecodeAQid extends BaseDecode {
    @Override
    protected Long decode(String cipher) {
        return IDCrypt.DecodeAQid(cipher);
    }

    @Override
    protected String getFuncName() {
        return "decode_aqid";
    }
}
