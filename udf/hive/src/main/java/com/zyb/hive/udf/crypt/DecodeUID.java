package com.zyb.hive.udf.crypt;

import com.zyb.go.crypt.IDCrypt;

import org.apache.hadoop.hive.ql.exec.Description;

@Description(name = "decode_uid", value = "_FUNC_(str) - decode Hk_Util_IdCrypt::encodeUid/golib.utils.EncodeUid output")
public class DecodeUID extends BaseDecode {
    @Override
    protected Long decode(String cipher) {
        return IDCrypt.DecodeUid(cipher);
    }

    @Override
    protected String getFuncName() {
        return "decode_uid";
    }
}
