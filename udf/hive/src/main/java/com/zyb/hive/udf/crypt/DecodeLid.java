package com.zyb.hive.udf.crypt;

import com.zyb.go.crypt.IDCrypt;

import org.apache.hadoop.hive.ql.exec.Description;

@Description(name = "decode_lid", value = "_FUNC_(str) - decode Hk_Util_IdCrypt::encodeLid/golib.utils.EncodeLid output")
public class DecodeLid extends BaseDecode {
    @Override
    protected Long decode(String cipher) {
        return IDCrypt.DecodeLid(cipher);
    }

    @Override
    protected String getFuncName() {
        return "decode_lid";
    }
}
