package com.zyb.hive.udf.crypt;

import com.zyb.go.crypt.IDCrypt;

import org.apache.hadoop.hive.ql.exec.Description;

@Description(name = "decode_cid", value = "_FUNC_(str) - decode Hk_Util_IdCrypt::encodeCid/golib.utils.EncodeCid output")
public class DecodeCid extends BaseDecode {
    @Override
    protected Long decode(String cipher) {
        return IDCrypt.DecodeCid(cipher);
    }

    @Override
    protected String getFuncName() {
        return "decode_cid";
    }
}
