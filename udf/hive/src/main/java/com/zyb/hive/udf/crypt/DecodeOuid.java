package com.zyb.hive.udf.crypt;

import com.zyb.go.crypt.IDCrypt;

import org.apache.hadoop.hive.ql.exec.Description;

@Description(name = "decode_ouid", value = "_FUNC_(str) - decode Hk_Util_IdCrypt::encodeOuid/golib.utils.EncodeOuid output")
public class DecodeOuid extends BaseDecode {
    @Override
    protected Long decode(String cipher) {
        return IDCrypt.DecodeOuid(cipher);
    }

    @Override
    protected String getFuncName() {
        return "decode_ouid";
    }
}
