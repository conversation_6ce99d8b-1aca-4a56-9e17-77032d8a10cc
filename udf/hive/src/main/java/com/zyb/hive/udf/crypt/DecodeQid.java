package com.zyb.hive.udf.crypt;

import com.zyb.go.crypt.IDCrypt;

import org.apache.hadoop.hive.ql.exec.Description;
import org.apache.hadoop.hive.ql.exec.UDFArgumentException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.IntObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.StringObjectInspector;
import org.apache.hadoop.io.LongWritable;

@Description(name = "decode_qid", value = "_FUNC_(str, [type = 0|1]) - decode Hk_Util_IdCrypt::encodeQid/golib.utils.EncodeQid output")
public class DecodeQid extends GenericUDF {
    private final LongWritable result = new LongWritable();
    private transient StringObjectInspector inputOI0;
    private transient IntObjectInspector type;

    @Override
    public ObjectInspector initialize(ObjectInspector[] arg0) throws UDFArgumentException {
        if (arg0.length == 0 || arg0.length > 2) {
            throw new UDFArgumentLengthException("decode_qid requires 1 or 2 argument, got " + arg0.length);
        }

        ObjectInspector input0 = arg0[0];
        if (!(input0 instanceof StringObjectInspector)) {
            throw new UDFArgumentTypeException(0, "decode_qid input only takes STRING types, got " + input0.getTypeName());
        }
        this.inputOI0 = (StringObjectInspector) input0;
        if (arg0.length == 2) {
            ObjectInspector type = arg0[1];
            if (!(type instanceof IntObjectInspector)) {
                throw new UDFArgumentTypeException(1, "decode_qid second argument only takes INT types, got " + type.getTypeName());
            }
            this.type = (IntObjectInspector) type;
        }

        return PrimitiveObjectInspectorFactory.writableLongObjectInspector;
    }

    @Override
    public Object evaluate(DeferredObject[] arg0) throws HiveException {
        Object input0 = arg0[0].get();
        if (input0 == null) {
            return null;
        }
        int type = 0;
        if (arg0.length == 2) {
            Object input1 = arg0[1].get();
            if (input1 != null) {
                type = this.type.get(input1);
            }
        }

        String cipher = this.inputOI0.getPrimitiveJavaObject(input0).toString();
        Long id = IDCrypt.DecodeQid(cipher, type);
        if (id == null) {
            return null;
        }
        result.set(id);
        return result;
    }

    @Override
    public String getDisplayString(String[] children) {
        assert (children.length == 1 || children.length == 2);
        return getStandardDisplayString("decode_qid", children);
    }
}
