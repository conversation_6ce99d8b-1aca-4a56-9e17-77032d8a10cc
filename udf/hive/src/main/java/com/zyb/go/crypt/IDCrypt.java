package com.zyb.go.crypt;

import java.io.File;

import com.sun.jna.Native;
import com.zyb.go.types.GoString;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

// IDCrypt go utils id crypt wrapper
public class IDCrypt {
    private static final Log LOG = LogFactory.getLog(IDCrypt.class.getName());
    private static LibIDCrypt handle = null;

    static {
        LOG.info("load so");
        try {
            File file = Native.extractFromResourcePath("/libidcrypt.so", IDCrypt.class.getClassLoader());
            LibIDCrypt lib = Native.load(file.getAbsolutePath(), LibIDCrypt.class);
            handle = (LibIDCrypt)Native.synchronizedLibrary(lib);
        } catch (Exception e) {
            throw new RuntimeException("加载so 库失败!" + e.getMessage());
        }
    }

    public static Long DecodeQid(String str, int qType) {
        return handle.DecodeQid(new GoString(str), qType).getResult();
    }

    public static Long DecodeAQid(String str) {
        return handle.DecodeAQid(new GoString(str)).getResult();
    }

    public static Long DecodeUid(String str) {
        return handle.DecodeUid(new GoString(str)).getResult();
    }

    public static Long DecodeCid(String str) {
        return handle.DecodeCid(new GoString(str)).getResult();
    }

    public static Long DecodeLid(String str) {
        return handle.DecodeLid(new GoString(str)).getResult();
    }

    public static Long DecodeOuid(String str) {
        return handle.DecodeOuid(new GoString(str)).getResult();
    }

    // 2458571
    public static void main(String[] argv) {
        String s = "07bf7f44bd494bb152ce4ae14f6a35f8";
        System.out.println(DecodeUid(s));
    }
}
