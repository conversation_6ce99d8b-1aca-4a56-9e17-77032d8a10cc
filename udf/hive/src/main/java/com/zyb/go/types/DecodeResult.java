package com.zyb.go.types;

import java.util.Arrays;
import java.util.List;

import com.sun.jna.Structure;

public class DecodeResult extends Structure implements Structure.ByValue {
    public Long r0;
    public String r1;

    protected List<String> getFieldOrder() {
        return Arrays.asList("r0", "r1");
    }

    /**
     * 返回Decode结果，Decode失败则返回null
     */
    public Long getResult() {
        if (r1 == null) {
            return r0;
        }
        return null;
    }
}
