package com.zyb.go.crypt;

import com.sun.jna.Library;
import com.zyb.go.types.DecodeResult;
import com.zyb.go.types.GoString;

public interface LibIDCrypt extends Library {
    public DecodeResult DecodeQid(GoString str, int qType);
    public DecodeResult DecodeAQid(GoString str);
    public DecodeResult DecodeUid(GoString str);
    public DecodeResult DecodeCid(GoString str);
    public DecodeResult DecodeLid(GoString str);
    public DecodeResult DecodeOuid(GoString str);
}
