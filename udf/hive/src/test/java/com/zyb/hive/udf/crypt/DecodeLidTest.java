package com.zyb.hive.udf.crypt;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredJavaObject;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredObject;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class DecodeLidTest {
    protected void runTest(Text value, LongWritable exp) throws HiveException {
        DecodeLid udf = new DecodeLid();
        ObjectInspector input = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        udf.initialize(new ObjectInspector[] {input});
        LongWritable result = (LongWritable) udf.evaluate(new DeferredObject[] { new DeferredJavaObject(value) });

        if (exp == null) {
            assertNull(result);
        } else {
            assertNotNull(result);
            assertEquals(exp, result, "DecodeLid test");
        }
    }

    @Test
    @DisplayName("input is null shuold return null")
    public void testDecode() throws Exception {
        runTest(null, null);
        runTest(new Text("invalid code"), null);
        // -1
        runTest(new Text("6a7b99d82ea6d1476a7b99d82ea6d147"), null);
        runTest(new Text("2e7fceb13108b08f2a46a08ab92a5410"), new LongWritable(123456L));
        // uint32
        runTest(new Text("fb4ad0ab6abef9c0fb4ad0ab6abef9c0"), new LongWritable(0L));
        runTest(new Text("df3682097fff08ae79e7f7002b75f2d9"), new LongWritable(4294967295L));
    }

    @Test
    @DisplayName("input length is 0 should throw an UDFArgumentLengthException")
    public void testArgumentLen0Exception() {
        DecodeLid udf = new DecodeLid();
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{}));

        assertEquals("decode_lid requires 1 argument, got 0", exception.getMessage());
    }

    @Test
    @DisplayName("input length is 2 should throw an UDFArgumentLengthException")
    public void testArgumentLen2Exception() {
        DecodeLid udf = new DecodeLid();
        ObjectInspector strOI = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{strOI, strOI}));

        assertEquals("decode_lid requires 1 argument, got 2", exception.getMessage());
    }

    @Test
    @DisplayName("input is boolean should throw an UDFArgumentException")
    public void testArgumentIsBooleanException() {
        DecodeLid udf = new DecodeLid();
        ObjectInspector boolOI = PrimitiveObjectInspectorFactory.javaBooleanObjectInspector;
        Exception exception = assertThrows(UDFArgumentTypeException.class, () -> udf.initialize(new ObjectInspector[]{boolOI}));

        assertEquals("decode_lid only takes STRING types, got boolean", exception.getMessage());
    }
}
