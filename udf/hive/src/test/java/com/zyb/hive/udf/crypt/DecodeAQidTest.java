package com.zyb.hive.udf.crypt;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredJavaObject;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredObject;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class DecodeAQidTest {
    protected void runTest(Text value, LongWritable exp) throws HiveException {
        DecodeAQid udf = new DecodeAQid();
        ObjectInspector input = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        udf.initialize(new ObjectInspector[] {input});

        LongWritable result = (LongWritable) udf.evaluate(new DeferredObject[] { new DeferredJavaObject(value) });
        if (exp == null) {
            assertNull(result);
        } else {
            assertNotNull(result);
            assertEquals(exp, result, "DecodeAQid test");
        }
    }

    @Test
    @DisplayName("input is null shuold return null")
    public void testDecode() throws Exception {
        runTest(null, null);
        runTest(new Text(""), null);
        runTest(new Text("invalid code"), null);
        // -1
        runTest(new Text("6392c9578479e2b26392c9578479e2b2"), null);
        runTest(new Text("31fe6d3b3ff615b585f433de42ac7492"), new LongWritable(123456L));
        // uint32
        runTest(new Text("76a08924ea7090b376a08924ea7090b3"), new LongWritable(0L));
        runTest(new Text("7875edafdc317cb8b8f302e170249773"), new LongWritable(4294967295L));
    }

    @Test
    @DisplayName("input length is 0 should throw an UDFArgumentLengthException")
    public void testArgumentLen0Exception() {
        DecodeAQid udf = new DecodeAQid();
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{}));

        assertEquals("decode_aqid requires 1 argument, got 0", exception.getMessage());
    }

    @Test
    @DisplayName("input length is 2 should throw an UDFArgumentLengthException")
    public void testArgumentLen2Exception() {
        DecodeAQid udf = new DecodeAQid();
        ObjectInspector strOI = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{strOI, strOI}));

        assertEquals("decode_aqid requires 1 argument, got 2", exception.getMessage());
    }

    @Test
    @DisplayName("input is boolean should throw an UDFArgumentException")
    public void testArgumentIsBooleanException() {
        DecodeAQid udf = new DecodeAQid();
        ObjectInspector boolOI = PrimitiveObjectInspectorFactory.javaBooleanObjectInspector;
        Exception exception = assertThrows(UDFArgumentTypeException.class, () -> udf.initialize(new ObjectInspector[]{boolOI}));

        assertEquals("decode_aqid only takes STRING types, got boolean", exception.getMessage());
    }
}
