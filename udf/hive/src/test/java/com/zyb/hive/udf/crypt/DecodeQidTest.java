package com.zyb.hive.udf.crypt;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredJavaObject;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredObject;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.io.IntWritable;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class DecodeQidTest {

    protected void runTest(Text value, LongWritable exp) throws HiveException {
        DecodeQid udf = new DecodeQid();
        ObjectInspector strOI = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        udf.initialize(new ObjectInspector[]{strOI});

        LongWritable result = (LongWritable) udf.evaluate(new DeferredObject[] { new DeferredJavaObject(value) });
        if (exp == null) {
            assertNull(result);
        } else {
            assertNotNull(result);
            assertEquals(exp, result, "DecodeQid test");
        }
    }

    protected void runTestWithType(Text value, IntWritable type, LongWritable exp) throws HiveException {
        DecodeQid udf = new DecodeQid();
        ObjectInspector input = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        ObjectInspector intOI = PrimitiveObjectInspectorFactory.writableIntObjectInspector;
        udf.initialize(new ObjectInspector[] {input, intOI});

        LongWritable result = (LongWritable) udf.evaluate(new DeferredObject[] { new DeferredJavaObject(value), new DeferredJavaObject(type) });
        if (exp == null) {
            assertNull(result);
        } else {
            assertNotNull(result);
            assertEquals(exp, result, "DecodeQid with type test");
        }
    }

    @Test
    @DisplayName("input is null shuold return null")
    public void testDecode() throws Exception {
        runTest(null, null);
        runTest(new Text("invalid code"), null);
        // -1
        runTest(new Text("8a6ff87902adef3461c1ee7648e187ce"), null);
        runTest(new Text("33e45370c25b828631bcf17aef982ced"), new LongWritable(123456L));
        // int64
        runTest(new Text("2a2ae571c623e35dd2e571c2369088d0"), new LongWritable(0L));
        runTest(new Text("8304f6c3ffe1fc5eefc8105f6bc88250c2678731f512b94d"), new LongWritable(9223372036854775807L));
    }

    @Test
    @DisplayName("input is null shuold return null")
    public void testDecodeWithType() throws Exception {
        runTestWithType(null, null, null);
        runTestWithType(new Text("invalid code"), null, null);
        // -1
        runTestWithType(new Text("8a6ff87902adef3461c1ee7648e187ce"), null, null);
        runTestWithType(new Text("8336d3ab34547eb114752dad8eb2b98b"), new IntWritable(1) , new LongWritable(123456L));
        // int64
        runTestWithType(new Text("0c69ee76ac36d6ff17978732cbb466fe"), new IntWritable(1), new LongWritable(0L));
        runTestWithType(new Text("149710d9cd1725dcae8ef70034081beb27f2ae10fbe8ff56"), new IntWritable(1), new LongWritable(9223372036854775807L));
    }

    @Test
    @DisplayName("input length is 0 should throw an UDFArgumentLengthException")
    public void testArgumentLen0Exception() {
        DecodeQid udf = new DecodeQid();
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{}));

        assertEquals("decode_qid requires 1 or 2 argument, got 0", exception.getMessage());
    }

    @Test
    @DisplayName("input length is 2 should throw an UDFArgumentLengthException")
    public void testArgumentLen3Exception() {
        DecodeQid udf = new DecodeQid();
        ObjectInspector strOI = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        ObjectInspector intOI = PrimitiveObjectInspectorFactory.javaIntObjectInspector;
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{strOI, intOI, strOI}));

        assertEquals("decode_qid requires 1 or 2 argument, got 3", exception.getMessage());
    }

    @Test
    @DisplayName("input is boolean should throw an UDFArgumentTypeException")
    public void testArgumentTypeException() {
        DecodeQid udf = new DecodeQid();
        ObjectInspector boolOI = PrimitiveObjectInspectorFactory.javaBooleanObjectInspector;
        Exception exception = assertThrows(UDFArgumentTypeException.class, () -> udf.initialize(new ObjectInspector[]{boolOI}));

        assertEquals("decode_qid input only takes STRING types, got boolean", exception.getMessage());

        DecodeQid udf2 = new DecodeQid();
        ObjectInspector strOI = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        ObjectInspector strOI2 = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        Exception exception2 = assertThrows(UDFArgumentTypeException.class, () -> udf2.initialize(new ObjectInspector[]{strOI, strOI2}));

        assertEquals("decode_qid second argument only takes INT types, got string", exception2.getMessage());
    }
}
