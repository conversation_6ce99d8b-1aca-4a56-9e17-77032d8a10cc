package com.zyb.hive.udf.crypt;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredJavaObject;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredObject;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class DecodeOuidTest {
    protected void runTest(Text value, LongWritable exp) throws HiveException {
        DecodeOuid udf = new DecodeOuid();
        ObjectInspector input = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        udf.initialize(new ObjectInspector[] {input});

        LongWritable result = (LongWritable) udf.evaluate(new DeferredObject[] { new DeferredJavaObject(value) });
        if (exp == null) {
            assertNull(result);
        } else {
            assertNotNull(result);
            assertEquals(exp, result, "DecodeOuid test");
        }
    }

    @Test
    @DisplayName("input is null shuold return null")
    public void testDecode() throws Exception {
        runTest(null, null);
        runTest(new Text("invalid code"), null);
        // -1
        runTest(new Text("92b32cf271de651392b32cf271de6513"), null);
        runTest(new Text("8b26e80b03c5fd01fc1c8184a79bea42"), new LongWritable(123456L));
        // uint32
        runTest(new Text("49d9458d6912788249d9458d69127882"), new LongWritable(0L));
        runTest(new Text("cbd56ed5a63755b43afed1ca3568cb9e"), new LongWritable(4294967295L));
    }

    @Test
    @DisplayName("input length is 0 should throw an UDFArgumentLengthException")
    public void testArgumentLen0Exception() {
        DecodeOuid udf = new DecodeOuid();
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{}));

        assertEquals("decode_ouid requires 1 argument, got 0", exception.getMessage());
    }

    @Test
    @DisplayName("input length is 2 should throw an UDFArgumentLengthException")
    public void testArgumentLen2Exception() {
        DecodeOuid udf = new DecodeOuid();
        ObjectInspector strOI = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{strOI, strOI}));

        assertEquals("decode_ouid requires 1 argument, got 2", exception.getMessage());
    }

    @Test
    @DisplayName("input is boolean should throw an UDFArgumentException")
    public void testArgumentIsBooleanException() {
        DecodeOuid udf = new DecodeOuid();
        ObjectInspector boolOI = PrimitiveObjectInspectorFactory.javaBooleanObjectInspector;
        Exception exception = assertThrows(UDFArgumentTypeException.class, () -> udf.initialize(new ObjectInspector[]{boolOI}));

        assertEquals("decode_ouid only takes STRING types, got boolean", exception.getMessage());
    }
}
