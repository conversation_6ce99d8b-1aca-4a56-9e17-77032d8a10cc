package com.zyb.hive.udf.crypt;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.apache.hadoop.hive.ql.exec.UDFArgumentLengthException;
import org.apache.hadoop.hive.ql.exec.UDFArgumentTypeException;
import org.apache.hadoop.hive.ql.metadata.HiveException;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredJavaObject;
import org.apache.hadoop.hive.ql.udf.generic.GenericUDF.DeferredObject;
import org.apache.hadoop.hive.serde2.objectinspector.ObjectInspector;
import org.apache.hadoop.hive.serde2.objectinspector.primitive.PrimitiveObjectInspectorFactory;
import org.apache.hadoop.io.LongWritable;
import org.apache.hadoop.io.Text;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class DecodeCidTest {
    protected void runTest(Text value, LongWritable exp) throws HiveException {
        DecodeCid udf = new DecodeCid();
        ObjectInspector input = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        udf.initialize(new ObjectInspector[] {input});
        LongWritable result = (LongWritable) udf.evaluate(new DeferredObject[] { new DeferredJavaObject(value) });

        if (exp == null) {
            assertNull(result);
        } else {
            assertNotNull(result);
            assertEquals(exp, result, "DecodeCid test");
        }
    }

    @Test
    @DisplayName("input is null shuold return null")
    public void testDecode() throws Exception {
        runTest(null, null);
        runTest(new Text("invalid code"), null);
        // -1
        runTest(new Text("8331fabd1e888f438331fabd1e888f43"), null);
        runTest(new Text("fa1cb0e33621b43a9d553fc9007b7575"), new LongWritable(123456L));
        // uint32
        runTest(new Text("d7bf5072941bd28dd7bf5072941bd28d"), new LongWritable(0L));
        runTest(new Text("9cdb384a05fe8523a375cdd899a7bdc6"), new LongWritable(4294967295L));
    }

    @Test
    @DisplayName("input length is 0 should throw an UDFArgumentLengthException")
    public void testArgumentLen0Exception() {
        DecodeCid udf = new DecodeCid();
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{}));

        assertEquals("decode_cid requires 1 argument, got 0", exception.getMessage());
    }

    @Test
    @DisplayName("input length is 2 should throw an UDFArgumentLengthException")
    public void testArgumentLen2Exception() {
        DecodeCid udf = new DecodeCid();
        ObjectInspector strOI = PrimitiveObjectInspectorFactory.javaStringObjectInspector;
        Exception exception = assertThrows(UDFArgumentLengthException.class, () -> udf.initialize(new ObjectInspector[]{strOI, strOI}));

        assertEquals("decode_cid requires 1 argument, got 2", exception.getMessage());
    }

    @Test
    @DisplayName("input is boolean should throw an UDFArgumentException")
    public void testArgumentIsBooleanException() {
        DecodeCid udf = new DecodeCid();
        ObjectInspector boolOI = PrimitiveObjectInspectorFactory.javaBooleanObjectInspector;
        Exception exception = assertThrows(UDFArgumentTypeException.class, () -> udf.initialize(new ObjectInspector[]{boolOI}));

        assertEquals("decode_cid only takes STRING types, got boolean", exception.getMessage());
    }
}
