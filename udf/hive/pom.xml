<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.zyb.udf</groupId>
    <artifactId>udf-parent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <groupId>zyb.hive.udf</groupId>
  <artifactId>zyb.hive.udf</artifactId>
  <packaging>jar</packaging>
  <version>1.0-SNAPSHOT</version>
  <name>Hive UDF</name>
  <url>http://maven.apache.org</url>

  <properties>
    <hadoop.version>2.7.2</hadoop.version>
    <hive.version>2.3.7</hive.version>
    <skip.assembly>false</skip.assembly>
  </properties>

  <dependencies>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna</artifactId>
      <version>5.12.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.hadoop</groupId>
      <artifactId>hadoop-common</artifactId>
      <version>${hadoop.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hive</groupId>
      <artifactId>hive-exec</artifactId>
      <version>${hive.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.4</version>
      <scope>provided</scope>
    </dependency>
    <!-- junit 5 -->
    <!-- <dependency> -->
    <!--   <groupId>org.junit.jupiter</groupId> -->
    <!--   <artifactId>junit-jupiter-engine</artifactId> -->
    <!--   <version>${junit-jupiter.version}</version> -->
    <!--   <scope>test</scope> -->
    <!-- </dependency> -->
  </dependencies>

  <repositories>
    <repository>
      <id>pentaho-public</id>
      <name>Pentaho Public</name>
      <url>https://nexus.pentaho.org/content/groups/omni</url>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>

  <developers>
    <developer>
      <name>xiongchao</name>
      <id>xiongchao</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
  </developers>
</project>
