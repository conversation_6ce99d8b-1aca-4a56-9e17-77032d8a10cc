#### 服务运营zlink实时计算平台任务

##### zlink任务规范
- 任务名为 负责人邮箱前缀@+表名（可以不要ads_zbk_前缀）
- SQL关键字大写，缩进4个空格。SQL规范可参考：https://help.aliyun.com/document_detail/137491.html
- 统一为生产环境任务，有变更需发单，详见：[生产环境任务上线流程](https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=310614952)
  - pms上线
    - 场景：当需要更新jar包或properties时
  - 开发 - 「上线」
    - 场景：当需要更新jar包、properties之外的信息时
    - 入口：平台开发界面「上线」按钮
    - 限制：不可修改jar包以及properties内容
  - 语义检测，目前需通过建一个非生产环境任务来解决

##### 目录结构
```
.
├── conf 配置文件目录
├── rtsql zlink任务目录  建议按任务类型分目录
│   └── hologres 消费hologres binlog cdc任务
│   └── elasticsearch 消费hologres binlog写es任务
│   └── lpc_ads 消费hologres binlog cdc lpc数仓任务
│   └── other 消费hologres binlog cdc任务
└── scripts 辅助脚本
|    └── output 辅助脚本产出 不一定是最新的
|       ├── check_diff
|        └── sync 全量同步脚本
|        └── zlink zlink初始化任务，需微调
└── udf/flink Flink用户自定义函数（User-defined Functions）
└── udf/hive Hive用户自定义函数（User-defined Functions）
```

##### 新建表流程
1. [建表规范](https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=310094152)
2. 根据ads_base层表的ddl生成全量同步sql，主键不一致的或者not null限制不一致的，需要用```COALESCE```给下默认值。注意，需确保默认值没有业务含义。
    ```sh
    # 可选步骤
    cd scripts
    # 导出ddl语句
    echo table | sh dump_ddl.sh > ddl.txt
    # 在scripts/output目录下生成默认zlink任务配置及同步SQL
    python gen_zlink_properties_and_full_sync_sql_by_hologres_ddl.py ddl.txt
    ```
3. 执行全量同步sql（**涉及SQL变更，统一在HoloWeb平台SQL编辑器上操作**）
4. 根据ads_base层表的ddl生成zlink任务，语义检测通过后，新增一个任务，恢复策略设置成None，并在源表里设置下startTime，往前推1个小时就行
5. 第3步正常消费后，停止任务，将恢复策略改成Latest State(从最新的checkpoint中恢复)，并勾选允许无还原状态
6. 重新启动上述任务

##### 同步DWS层数据
1. CDC Mode false，多个任务写不能用cdc模式；上游承诺不会删数据，hg_binlog_event_type取5（INSERT）、7（AFTER_UPDATE）即可
2. 多个任务里目标表的主键必须一致
3. 任务命名规则，es.表名.{域}.properties，其中域是指数仓定的域，如出勤，直接复用即可

##### 新增字段流程
1. 数据组在ads_base层对应表里新增指定字段
2. 在ads层对应表新增字段（**hologres目前不支持删除字段，新增字段需谨慎**）
    ```sql
    BEGIN;
    -- 加字段
    ALTER TABLE IF EXISTS zyb_zbk_bzr_ads.xxx ADD COLUMN column1 type, ADD COLUMN column2 type
    -- 更新view (不更新的话，通过view查不到新增字段)
    CREATE OR REPLACE VIEW zyb_zbk_bzr_ads.xxx_view AS SELECT * FROM zyb_zbk_bzr_ads.xxx;
    END;
    ```
3. 停止zlink任务，新增对应字段，语义检测通过后，启动任务

##### 重建表/全量同步数据的场景
若现有表结构不满足现有查询场景，如需更换```distribution_key```或```clustering_key```，则需要重新建表
1. 停止同步对应表的zlink任务
2. 建一张新表，如xxx_v2，同步全量数据，流程同新建表流程
3. 修改旧表名如加个后缀；将新表重命名为旧表表名；**更新视图**（不更新的话，视图还是指向旧表）
    ```sh
    -- 辅助脚本，建议在业务低峰期操作
    sh scripts/rebuild_table_sql_generator.sh zyb_zbk_bzr_ads.table_to_rebuild
    ```
4. 启动任务
   - 1、修改任务恢复策略为None；2、修改offset改成同步任务开始前半小时即可，源表配置里增加startTime配置项，详见：https://help.aliyun.com/document_detail/321430.html
   - 启动一段时间后，1、删除startTime配置项；2、将任务恢复策略改为从最新的checkpoint中恢复（这一步是为了避免重启任务后每次都从startTime开始消费）
6. 没问题之后再删除老表

##### Flink UDF
自定义函数列表
1. 前端日志ETL
   - 入口类：com.streaming.flink.udf.YiKePerform
   - 详见：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=114982933
2. ExamAnswer解析
   - 入口类：com.streaming.flink.udf.ExamAnswer

使用方法
```
# 注册udf
flame.table.reg_udf.type=factory
flame.table.reg_udf.sql=create function decode_exam_answer as 'com.streaming.flink.udf.ExamAnswer' LANGUAGE JAVA
```
##### Hive UDF
pms不支持多语言一起编译。暂手动编译Go shared libraries，然后将so文件copy到hive-udf/src/main/resources文件夹下。编译测试后没问题，打包后将jar包上传至大数据的cos。
```bash
# mac下跨平台编译linux so报错了，改为在linux环境下编译
# cd hive-udf/go && CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -o libidcrypt.so -buildmode=c-shared id_crypt.go
# ld: unknown option: -Bsymbolic
# clang: error: linker command failed with exit code 1 (use -v to see invocation)

# cgo c-shared
cd udf/hive/go && go build -o libidcrypt.so -buildmode=c-shared id_crypt.go
```

使用方法
```sql
-- UDA任务
ADD JAR cosn://zyb-offline/user/banzhuren/libs/zyb.hive.udf-1.0-SNAPSHOT-jar-with-dependencies.jar;

CREATE TEMPORARY FUNCTION decode_uid as 'com.zyb.hive.udf.crypt.DecodeUID';

SELECT decode_uid(xxx) FROM xxx;
```

参考资料：
https://github.com/apache/hive/blob/master/ql/src/java/org/apache/hadoop/hive/ql/udf/generic/GenericUDF.java

https://github.com/apache/hive/tree/master/contrib/src/java/org/apache/hadoop/hive/contrib/genericudf/example

https://github.com/apache/doris/blob/master/fe/hive-udf/src/main/java/org/apache/doris/udf

https://github.com/freewind-demos/call-go-function-from-java-demo

https://github.com/vladimirvivien/go-cshared-examples

https://pkg.go.dev/cmd/cgo

https://go.dev/blog/cgo

##### 集群配置管理
上线构建时替换变量，不影响语义检测，方便管理配置文件。已有变量见conf/variables.ini。

##### 环境安装
psql命令行客户端
```sh
brew install postgresql
```
python环境
```sh
docker compose up -d

pip install -r requirements.txt
```
udf开发环境

可使用docker快速搭建，详见：[Flink SQL Demo: Building an End-to-End Streaming Application](https://flink.apache.org/2020/07/28/flink-sql-demo-building-e2e-streaming-application.html)
- jar包编译完成后，复制到/opt/sql-client/lib目录下，退出重新进入sql-client即可
- 调试模式（select xxx）下，报错信息可以看日志：/opt/flink/log/flink--sql-client-xxx.log
- submit query后，可前往Apache Flink Dashboard查看日志

##### 依赖的jar包
lib/flink-sql-connector-elasticsearch7_2.11-1.12.3.jar 来自：https://nightlies.apache.org/flink/flink-docs-release-1.12/dev/table/connectors/elasticsearch.html

##### 更多
[Flink CDC模式消费Hologres Binlog](https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=325230516)

[Flink/Blink实时消费Hologres Binlog](https://help.aliyun.com/document_detail/321430.html)
