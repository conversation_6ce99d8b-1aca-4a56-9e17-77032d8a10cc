-- 前端日志实时ETL
-- https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=114982933
CREATE FUNCTION yikefeperform_parser AS 'com.streaming.flink.udf.YiKePerform' LANGUAGE JAVA;

CREATE TABLE kafka_yikefeperform (
    raw_log STRING
) WITH (
    'connector' = 'kafka',
    'property-version' = '0.11',
    'topic' = 'kube-txprod-bigdata-nlog-natj-nlog-yikeperform',
    'properties.bootstrap.servers' = '*************:9092',
    'properties.group.id' = 'yikefeperform.fwyy.assistant',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'raw'
);

CREATE TABLE es_yikefeperform (
    authkey STRING,
    url STRING,
    name STRING,
    type STRING,
    errorType STRING,
    message STRING,
    cookie STRING,
    ua STRING,
    `@timestamp` STRING,
    client_ip STRING,
    location MAP<STRING, STRING>,
    custom MAP<STRING, STRING>,
    urlParams MAP<STRING, STRING>,
    indexSuffix STRING
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = 'http://*************:9200',
    'index' = 'felog-{indexSuffix}',
    'username' = 'fywwlogservice_app',
    'password' = 'VTMwXzWKydQqOCcnLxvot'
);

CREATE TEMPORARY VIEW temp AS
SELECT
    authkey,
    url,
    name,
    type,
    errorType,
    message,
    cookie,
    ua,
    `dateTime` AS `@timestamp`,
    clientIP AS client_ip,
    location,
    custom,
    urlParams,
    indexSuffix
FROM
    kafka_yikefeperform,
     LATERAL TABLE (
        yikefeperform_parser(kafka_yikefeperform.raw_log)
    );

INSERT INTO
    es_yikefeperform
SELECT
    *
FROM
    temp;