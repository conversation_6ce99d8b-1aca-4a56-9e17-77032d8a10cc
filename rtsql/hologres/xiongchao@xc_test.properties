-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_lu (
    `attend_duration` BIGINT,
    `course_id` BIGINT,
    `is_asscourse_attend` BIGINT,
    `is_asscourse_finish` BIGINT,
    `lesson_id` BIGINT,
    `playback_time` BIGINT,
    `student_uid` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.xc_test_base_idl_assistantcourse_lu',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_lu (
    `attend_duration` BIGINT,
    `course_id` BIGINT,
    `is_asscourse_attend` BIGINT,
    `is_asscourse_finish` BIGINT,
    `lesson_id` BIGINT,
    `playback_time` BIGINT,
    `student_uid` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.xc_test_idl_assistantcourse_lu',
    'mutatetype' = 'insertOrUpdate',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_lu
SELECT
    `attend_duration`,
    COALESCE(`course_id`, 0) AS `course_id`,
    `is_asscourse_attend`,
    `is_asscourse_finish`,
    `lesson_id`,
    `playback_time`,
    `student_uid`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_lu;