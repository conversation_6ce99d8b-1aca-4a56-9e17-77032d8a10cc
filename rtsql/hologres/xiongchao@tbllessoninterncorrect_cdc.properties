-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_tbllessoninterncorrect (
    `course_id` BIGINT,
    `cpu_id` BIGINT,
    `daily_homework_amend_submit_num` BIGINT,
    `daily_homework_correct_in_24h_num` BIGINT,
    `daily_homework_corrected_num` BIGINT,
    `daily_homework_finish_correct_in_24h_num` BIGINT,
    `daily_homework_finish_correct_in_48h_num` BIGINT,
    `daily_homework_finish_correct_in_72h_num` BIGINT,
    `daily_homework_finish_correct_num` BIGINT,
    `daily_homework_not_finish_correct_in_18h36h_num` BIGINT,
    `daily_homework_not_finish_correct_in_24h_num` BIGINT,
    `daily_homework_not_finish_correct_in_48h_num` BIGINT,
    `daily_homework_not_finish_correct_in_72h_num` BIGINT,
    `daily_homework_review_failed_num` BIGINT,
    `daily_homework_review_passed_num` BIGINT,
    `daily_homework_submit_num` BIGINT,
    `daily_homework_waiting_for_review_num` BIGINT,
    `daily_intern_correct_homework_num` BIGINT,
    `daily_intern_correct_overtime_question_num` BIGINT,
    `daily_intern_correct_question_num` BIGINT,
    `daily_intern_review_homework_in24h_num` BIGINT,
    `daily_intern_review_homework_num` BIGINT,
    `daily_intern_review_pass_question_num` BIGINT,
    `daily_intern_review_passed_question_num` BIGINT,
    `daily_intern_review_question_num` BIGINT,
    `daily_intern_reviewed_question_num` BIGINT,
    `daily_intern_waiting_for_review_question_num` BIGINT,
    `daily_question_amend_submit_num` BIGINT,
    `daily_question_correct_overtime_num` BIGINT,
    `daily_question_corrected_num` BIGINT,
    `daily_question_review_failed_num` BIGINT,
    `daily_question_review_passed_num` BIGINT,
    `daily_question_submit_num` BIGINT,
    `exam_type` BIGINT,
    `intern_correct_uid` BIGINT,
    `lesson_id` BIGINT,
    `outline_id` BIGINT,
    `save_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_tbllessoninterncorrect',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_tbllessoninterncorrect (
    `course_id` BIGINT,
    `cpu_id` BIGINT,
    `daily_homework_amend_submit_num` BIGINT,
    `daily_homework_correct_in_24h_num` BIGINT,
    `daily_homework_corrected_num` BIGINT,
    `daily_homework_finish_correct_in_24h_num` BIGINT,
    `daily_homework_finish_correct_in_48h_num` BIGINT,
    `daily_homework_finish_correct_in_72h_num` BIGINT,
    `daily_homework_finish_correct_num` BIGINT,
    `daily_homework_not_finish_correct_in_18h36h_num` BIGINT,
    `daily_homework_not_finish_correct_in_24h_num` BIGINT,
    `daily_homework_not_finish_correct_in_48h_num` BIGINT,
    `daily_homework_not_finish_correct_in_72h_num` BIGINT,
    `daily_homework_review_failed_num` BIGINT,
    `daily_homework_review_passed_num` BIGINT,
    `daily_homework_submit_num` BIGINT,
    `daily_homework_waiting_for_review_num` BIGINT,
    `daily_intern_correct_homework_num` BIGINT,
    `daily_intern_correct_overtime_question_num` BIGINT,
    `daily_intern_correct_question_num` BIGINT,
    `daily_intern_review_homework_in24h_num` BIGINT,
    `daily_intern_review_homework_num` BIGINT,
    `daily_intern_review_pass_question_num` BIGINT,
    `daily_intern_review_passed_question_num` BIGINT,
    `daily_intern_review_question_num` BIGINT,
    `daily_intern_reviewed_question_num` BIGINT,
    `daily_intern_waiting_for_review_question_num` BIGINT,
    `daily_question_amend_submit_num` BIGINT,
    `daily_question_correct_overtime_num` BIGINT,
    `daily_question_corrected_num` BIGINT,
    `daily_question_review_failed_num` BIGINT,
    `daily_question_review_passed_num` BIGINT,
    `daily_question_submit_num` BIGINT,
    `exam_type` BIGINT,
    `intern_correct_uid` BIGINT,
    `lesson_id` BIGINT,
    `outline_id` BIGINT,
    `save_time` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_tbllessoninterncorrect',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_tbllessoninterncorrect
SELECT
    `course_id`,
    `cpu_id`,
    `daily_homework_amend_submit_num`,
    `daily_homework_correct_in_24h_num`,
    `daily_homework_corrected_num`,
    `daily_homework_finish_correct_in_24h_num`,
    `daily_homework_finish_correct_in_48h_num`,
    `daily_homework_finish_correct_in_72h_num`,
    `daily_homework_finish_correct_num`,
    `daily_homework_not_finish_correct_in_18h36h_num`,
    `daily_homework_not_finish_correct_in_24h_num`,
    `daily_homework_not_finish_correct_in_48h_num`,
    `daily_homework_not_finish_correct_in_72h_num`,
    `daily_homework_review_failed_num`,
    `daily_homework_review_passed_num`,
    `daily_homework_submit_num`,
    `daily_homework_waiting_for_review_num`,
    `daily_intern_correct_homework_num`,
    `daily_intern_correct_overtime_question_num`,
    `daily_intern_correct_question_num`,
    `daily_intern_review_homework_in24h_num`,
    `daily_intern_review_homework_num`,
    `daily_intern_review_pass_question_num`,
    `daily_intern_review_passed_question_num`,
    `daily_intern_review_question_num`,
    `daily_intern_reviewed_question_num`,
    `daily_intern_waiting_for_review_question_num`,
    `daily_question_amend_submit_num`,
    `daily_question_correct_overtime_num`,
    `daily_question_corrected_num`,
    `daily_question_review_failed_num`,
    `daily_question_review_passed_num`,
    `daily_question_submit_num`,
    `exam_type`,
    `intern_correct_uid`,
    `lesson_id`,
    `outline_id`,
    `save_time`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_tbllessoninterncorrect;