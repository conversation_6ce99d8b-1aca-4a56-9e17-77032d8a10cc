-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `is_l2_once` BIGINT,
    `is_l2r` BIGINT,
    `last_l2r_course` BIGINT,
    `last_l2r_sub_trade_id` BIGINT,
    `last_l2r_time` BIGINT,
    `last_refund_l2_time` BIGINT,
    `learn_season` BIGINT,
    `retain_detail` STRING,
    `status` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `trade_time` BIGINT,
    `update_time` BIGINT,
    `year` BIGINT,
    `year_season` STRING,
    `is_l2r_space_season` BIGINT,
    `space_season_retain_detail` STRING,
    `is_l2r_space_once` BIGINT,
    `last_refund_l2r_space_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_tblcontinuenotice',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice (
    `course_id` BIGINT,
    `is_l2_once` BIGINT,
    `is_l2r` BIGINT,
    `last_l2r_course` BIGINT,
    `last_l2r_sub_trade_id` BIGINT,
    `last_l2r_time` BIGINT,
    `last_refund_l2_time` BIGINT,
    `learn_season` BIGINT,
    `retain_detail` STRING,
    `status` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `trade_time` BIGINT,
    `update_time` BIGINT,
    `year` BIGINT,
    `is_l2r_space_season` BIGINT,
    `space_season_retain_detail` STRING,
    `is_l2r_space_once` BIGINT,
    `last_refund_l2r_space_time` BIGINT,
    PRIMARY KEY (sub_trade_id, course_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'jdbc',
    'url' = '*********************************************************************',
    'username' = 'dataware_app',
    'password' = 'Zk_vm4P_KJB78Ue',
    'scan.auto-commit' = 'false',
    'sink.buffer-flush.max-rows' = '1000',
    'table-name' = 'tblContinueNoticeV2'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice
SELECT
    COALESCE(`course_id`, 0),
    COALESCE(`is_l2_once`, 0),
    COALESCE(`is_l2r`, 0),
    COALESCE(`last_l2r_course`, 0),
    COALESCE(`last_l2r_sub_trade_id`, 0),
    COALESCE(`last_l2r_time`, 0),
    COALESCE(`last_refund_l2_time`, 0),
    COALESCE(`learn_season`, 0),
    COALESCE(`retain_detail`, ''),
    COALESCE(`status`, 0),
    COALESCE(`student_uid`, 0),
    COALESCE(`sub_trade_id`, 0),
    COALESCE(`trade_time`, 0),
    COALESCE(`update_time`, 0),
    COALESCE(`year`, 0),
    COALESCE(`is_l2r_space_season`, 0),
    COALESCE(`space_season_retain_detail`, ''),
    COALESCE(`is_l2r_space_once`, 0),
    COALESCE(`last_refund_l2r_space_time`, 0)
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice
WHERE
    `hg_binlog_event_type` <> 3;