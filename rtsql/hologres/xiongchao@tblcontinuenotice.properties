-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `is_l2_once` BIGINT,
    `is_l2r` BIGINT,
    `last_l2r_course` BIGINT,
    `last_l2r_sub_trade_id` BIGINT,
    `last_l2r_time` BIGINT,
    `last_refund_l2_time` BIGINT,
    `learn_season` BIGINT,
    `retain_detail` STRING,
    `status` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `trade_time` BIGINT,
    `update_time` BIGINT,
    `year` BIGINT,
    `year_season` STRING,
    `is_l2r_space_season` BIGINT,
    `space_season_retain_detail` STRING,
    `is_l2r_space_once` BIGINT,
    `last_refund_l2r_space_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_tblcontinuenotice',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice (
    `course_id` BIGINT,
    `is_l2_once` BIGINT,
    `is_l2r` BIGINT,
    `last_l2r_course` BIGINT,
    `last_l2r_sub_trade_id` BIGINT,
    `last_l2r_time` BIGINT,
    `last_refund_l2_time` BIGINT,
    `learn_season` BIGINT,
    `retain_detail` STRING,
    `status` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `trade_time` BIGINT,
    `update_time` BIGINT,
    `year` BIGINT,
    `year_season` STRING,
    `is_l2r_space_season` BIGINT,
    `space_season_retain_detail` STRING,
    `is_l2r_space_once` BIGINT,
    `last_refund_l2r_space_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_tblcontinuenotice',
    'mutatetype' = 'insertOrUpdate',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_tblcontinuenotice
SELECT
    `course_id`,
    `is_l2_once`,
    `is_l2r`,
    `last_l2r_course`,
    `last_l2r_sub_trade_id`,
    COALESCE(`last_l2r_time`, 0) AS `last_l2r_time`,
    COALESCE(`last_refund_l2_time`, 0) AS `last_refund_l2_time`,
    `learn_season`,
    `retain_detail`,
    `status`,
    `student_uid`,
    `sub_trade_id`,
    `trade_time`,
    `update_time`,
    `year`,
    `year_season`,
    `is_l2r_space_season`,
    `space_season_retain_detail`,
    `is_l2r_space_once`,
    `last_refund_l2r_space_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_tblcontinuenotice
WHERE
    `hg_binlog_event_type` <> 3;