-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_junior_assistant_index (
    `assistant_uid` BIGINT,
    `config_id` BIGINT,
    `dt` BIGINT,
    `invitee_uid_bind_pv` BIGINT,
    `invitee_uid_uv` BIGINT,
    `inviter_uid_uv` BIGINT,
    `poster_uv` BIGINT,
    `season` BIGINT,
    `student_uid_uv` BIGINT,
    `year` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_junior_assistant_index',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_junior_assistant_index (
    `assistant_uid` BIGINT,
    `config_id` BIGINT,
    `dt` BIGINT,
    `invitee_uid_bind_pv` BIGINT,
    `invitee_uid_uv` BIGINT,
    `inviter_uid_uv` BIGINT,
    `poster_uv` BIGINT,
    `season` BIGINT,
    `student_uid_uv` BIGINT,
    `year` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_adl_referral_activity_junior_assistant_index',
    'mutatetype' = 'insertOrUpdate',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_junior_assistant_index
SELECT
    `assistant_uid`,
    `config_id`,
    `dt`,
    `invitee_uid_bind_pv`,
    `invitee_uid_uv`,
    `inviter_uid_uv`,
    `poster_uv`,
    `season`,
    `student_uid_uv`,
    `year`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_junior_assistant_index;