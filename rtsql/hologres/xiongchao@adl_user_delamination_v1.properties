-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_user_delamination_v1 (
    `hg_binlog_event_type` BIGINT,
    `a_student_num` BIGINT,
    `always_test_exam_finish_num` BIGINT,
    `always_test_exam_right_num` BIGINT,
    `always_test_exam_total_num` BIGINT,
    `assistant_code` STRING,
    `assistant_name` <PERSON><PERSON><PERSON>,
    `assistant_uid` BIGINT,
    `attend_finish_lesson_num` BIGINT,
    `attend_long_lesson_num` BIGINT,
    `b_student_num` BIGINT,
    `branch_school_principal_code` STRING,
    `branch_school_principal_name` STRING,
    `branch_school_principal_uid` BIGINT,
    `c_student_num` BIGINT,
    `challenge_finish_num` BIGINT,
    `challenge_total_num` BIGINT,
    `city_level` BIGINT,
    `city_name` STRING,
    `class_type` STRING,
    `consolidation_exam_finish_num` BIGINT,
    `consolidation_exam_right_num` BIGINT,
    `consolidation_exam_total_num` BIGINT,
    `course_id` BIGINT,
    `course_name` STRING,
    `d_student_num` BIGINT,
    `faculty_director_code` STRING,
    `faculty_director_name` STRING,
    `faculty_director_uid` BIGINT,
    `grade_id` BIGINT,
    `group_leader_code` STRING,
    `group_leader_name` STRING,
    `group_leader_uid` BIGINT,
    `group_user_message_sent_total_num` BIGINT,
    `interactive_exam_finish_num` BIGINT,
    `interactive_exam_right_num` BIGINT,
    `interactive_exam_total_num` BIGINT,
    `is_bound_discount` BIGINT,
    `is_preclass` BIGINT,
    `is_transfer_student` BIGINT,
    `learn_season` STRING,
    `learn_year` STRING,
    `lesson1_finish_num` BIGINT,
    `lesson1_total_num` BIGINT,
    `need_attend_lesson_num` BIGINT,
    `new_user_type` STRING,
    `next_expansion_course_num` BIGINT,
    `playback_all_num` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `preclass_attend_num` BIGINT,
    `preclass_finish_attend_lesson_num` BIGINT,
    `preview_exam_finish_num` BIGINT,
    `preview_exam_right_num` BIGINT,
    `preview_exam_total_num` BIGINT,
    `recent_7_days_tianying_valid_chat_num` BIGINT,
    `save_time` STRING,
    `subject_director_code` STRING,
    `subject_director_name` STRING,
    `subject_director_uid` BIGINT,
    `subject_group_leader_code` STRING,
    `subject_group_leader_name` STRING,
    `subject_group_leader_uid` BIGINT,
    `subject_id` BIGINT,
    `tutor_code` STRING,
    `tutor_name` STRING,
    `tutor_uid` BIGINT,
    `user_active_valid_session_count` BIGINT,
    `user_second_source` STRING,
    `valid_session_total_num` BIGINT,
    `valid_student_num` BIGINT,
    `wechat_cnt` BIGINT,
    `wechat_reply_cnt` BIGINT,
    `attend_long_lesson_num_timeless` BIGINT,
    `attend_finish_lesson_num_timeless` BIGINT,
    `total_voice_chat_num` BIGINT,
    `assistantcourse_attend_num` BIGINT,
    `assistantcourse_attend_finish_num` BIGINT,
    `assistantcourse_need_attend_num` BIGINT,
    `sent_total_num` BIGINT,
    `user_sent_total_num` BIGINT,
    `subject_num` BIGINT,
    `attend_long_lesson_num_14d` BIGINT,
    `attend_finish_lesson_num_14d` BIGINT,
    `total_voice_chat_duration` BIGINT,
    `consolidation_exam_s_num` BIGINT,
    `homework_tid_first_right_cnt` BIGINT,
    `homework_tid_first_correct_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_adl_user_delamination_v1',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_user_delamination_v1 (
    `a_student_num` BIGINT,
    `always_test_exam_finish_num` BIGINT,
    `always_test_exam_right_num` BIGINT,
    `always_test_exam_total_num` BIGINT,
    `assistant_code` STRING,
    `assistant_name` STRING,
    `assistant_uid` BIGINT,
    `attend_finish_lesson_num` BIGINT,
    `attend_long_lesson_num` BIGINT,
    `b_student_num` BIGINT,
    `branch_school_principal_code` STRING,
    `branch_school_principal_name` STRING,
    `branch_school_principal_uid` BIGINT,
    `c_student_num` BIGINT,
    `challenge_finish_num` BIGINT,
    `challenge_total_num` BIGINT,
    `city_level` BIGINT,
    `city_name` STRING,
    `class_type` STRING,
    `consolidation_exam_finish_num` BIGINT,
    `consolidation_exam_right_num` BIGINT,
    `consolidation_exam_total_num` BIGINT,
    `course_id` BIGINT,
    `course_name` STRING,
    `d_student_num` BIGINT,
    `faculty_director_code` STRING,
    `faculty_director_name` STRING,
    `faculty_director_uid` BIGINT,
    `grade_id` BIGINT,
    `group_leader_code` STRING,
    `group_leader_name` STRING,
    `group_leader_uid` BIGINT,
    `group_user_message_sent_total_num` BIGINT,
    `interactive_exam_finish_num` BIGINT,
    `interactive_exam_right_num` BIGINT,
    `interactive_exam_total_num` BIGINT,
    `is_bound_discount` BIGINT,
    `is_preclass` BIGINT,
    `lesson_transfer_type` BIGINT,
    `learn_season` STRING,
    `learn_year` STRING,
    `lesson1_finish_num` BIGINT,
    `lesson1_total_num` BIGINT,
    `need_attend_lesson_num` BIGINT,
    `new_user_type` STRING,
    `next_expansion_course_num` BIGINT,
    `playback_all_num` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `preclass_attend_num` BIGINT,
    `preclass_finish_attend_lesson_num` BIGINT,
    `preview_exam_finish_num` BIGINT,
    `preview_exam_right_num` BIGINT,
    `preview_exam_total_num` BIGINT,
    `recent_7_days_tianying_valid_chat_num` BIGINT,
    `save_time` STRING,
    `subject_director_code` STRING,
    `subject_director_name` STRING,
    `subject_director_uid` BIGINT,
    `subject_group_leader_code` STRING,
    `subject_group_leader_name` STRING,
    `subject_group_leader_uid` BIGINT,
    `subject_id` BIGINT,
    `tutor_code` STRING,
    `tutor_name` STRING,
    `tutor_uid` BIGINT,
    `user_active_valid_session_count` BIGINT,
    `user_second_source` STRING,
    `valid_session_total_num` BIGINT,
    `valid_student_num` BIGINT,
    `wechat_cnt` BIGINT,
    `wechat_reply_cnt` BIGINT,
    `attend_long_lesson_num_timeless` BIGINT,
    `attend_finish_lesson_num_timeless` BIGINT,
    `total_voice_chat_num` BIGINT,
    `assistantcourse_attend_num` BIGINT,
    `assistantcourse_attend_finish_num` BIGINT,
    `assistantcourse_need_attend_num` BIGINT,
    `sent_total_num` BIGINT,
    `user_sent_total_num` BIGINT,
    `subject_num` BIGINT,
    `attend_long_lesson_num_14d` BIGINT,
    `attend_finish_lesson_num_14d` BIGINT,
    `total_voice_chat_duration` BIGINT,
    `consolidation_exam_s_num` BIGINT,
    `homework_tid_first_right_cnt` BIGINT,
    `homework_tid_first_correct_cnt` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_adl_user_delamination_v1',
    'mutatetype' = 'insertOrUpdate',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_adl_user_delamination_v1
SELECT
    `a_student_num`,
    `always_test_exam_finish_num`,
    `always_test_exam_right_num`,
    `always_test_exam_total_num`,
    `assistant_code`,
    `assistant_name`,
    `assistant_uid`,
    `attend_finish_lesson_num`,
    `attend_long_lesson_num`,
    `b_student_num`,
    `branch_school_principal_code`,
    `branch_school_principal_name`,
    `branch_school_principal_uid`,
    `c_student_num`,
    `challenge_finish_num`,
    `challenge_total_num`,
    `city_level`,
    `city_name`,
    `class_type`,
    `consolidation_exam_finish_num`,
    `consolidation_exam_right_num`,
    `consolidation_exam_total_num`,
    `course_id`,
    `course_name`,
    `d_student_num`,
    `faculty_director_code`,
    `faculty_director_name`,
    `faculty_director_uid`,
    `grade_id`,
    `group_leader_code`,
    `group_leader_name`,
    `group_leader_uid`,
    `group_user_message_sent_total_num`,
    `interactive_exam_finish_num`,
    `interactive_exam_right_num`,
    `interactive_exam_total_num`,
    `is_bound_discount`,
    `is_preclass`,
    `is_transfer_student` AS `lesson_transfer_type`,
    `learn_season`,
    `learn_year`,
    `lesson1_finish_num`,
    `lesson1_total_num`,
    `need_attend_lesson_num`,
    `new_user_type`,
    `next_expansion_course_num`,
    `playback_all_num`,
    `playback_participate_num`,
    `playback_right_num`,
    `preclass_attend_num`,
    `preclass_finish_attend_lesson_num`,
    `preview_exam_finish_num`,
    `preview_exam_right_num`,
    `preview_exam_total_num`,
    `recent_7_days_tianying_valid_chat_num`,
    `save_time`,
    `subject_director_code`,
    `subject_director_name`,
    `subject_director_uid`,
    `subject_group_leader_code`,
    `subject_group_leader_name`,
    `subject_group_leader_uid`,
    `subject_id`,
    `tutor_code`,
    `tutor_name`,
    `tutor_uid`,
    `user_active_valid_session_count`,
    `user_second_source`,
    `valid_session_total_num`,
    `valid_student_num`,
    `wechat_cnt`,
    `wechat_reply_cnt`,
    `attend_long_lesson_num_timeless`,
    `attend_finish_lesson_num_timeless`,
    `total_voice_chat_num`,
    `assistantcourse_attend_num`,
    `assistantcourse_attend_finish_num`,
    `assistantcourse_need_attend_num`,
    `sent_total_num`,
    `user_sent_total_num`,
    `subject_num`,
    `attend_long_lesson_num_14d`,
    `attend_finish_lesson_num_14d`,
    `total_voice_chat_duration`,
    `consolidation_exam_s_num`,
    `homework_tid_first_right_cnt`,
    `homework_tid_first_correct_cnt`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_adl_user_delamination_v1
WHERE
    `hg_binlog_event_type` <> 3;