-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_student_day_lesson_num (
    `c_start_date` BIGINT,
    `is_deleted` BIGINT,
    `live_lesson_detail` STRING,
    `live_lesson_num` BIGINT,
    `student_uid` BIGINT,
    `update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_student_day_lesson_num',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_student_day_lesson_num (
    `c_start_date` BIGINT,
    `is_deleted` BIGINT,
    `live_lesson_detail` STRING,
    `live_lesson_num` BIGINT,
    `student_uid` BIGINT,
    `update_time` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_student_day_lesson_num',
    'mutatetype' = 'insertOrUpdate',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_student_day_lesson_num
SELECT
    `c_start_date`,
    `is_deleted`,
    `live_lesson_detail`,
    `live_lesson_num`,
    `student_uid`,
    `update_time`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_student_day_lesson_num;