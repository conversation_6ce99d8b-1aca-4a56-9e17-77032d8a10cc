-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_tbldatasop<PERSON> (
    `hg_binlog_event_type` BIGINT,
    `access_num` INT,
    `add_backinterview_duration` INT,
    `add_backinterview_num` INT,
    `add_interview_duration` INT,
    `add_interview_num` INT,
    `after_class_phone_back_interview_1min_num` INT,
    `after_class_phone_back_interview_num` INT,
    `after_classrefund_num` INT,
    `all_backinterview_num` INT,
    `all_backinterview_standard_num` INT,
    `all_duration` INT,
    `all_enterprise_wechat_num` INT,
    `all_num` INT,
    `all_wechat_add_num` INT,
    `assistant_uid` BIGINT,
    `automaticl_enterprise_wechat_num` INT,
    `back_interview_num` INT,
    `before2_reg_num` INT,
    `before_reg_num` INT,
    `bound_conversion_reg_num` INT,
    `bound_reserved_reg_num` INT,
    `bound_retain_num` INT,
    `change_num` INT,
    `complete_test_num` INT,
    `continue_access_num` INT,
    `continue_label_num` INT,
    `continue_level_num_1` INT,
    `continue_level_num_2` INT,
    `continue_oncelevel_num` INT,
    `course_id` INT,
    `daily_change` INT,
    `daily_not_bound_l2r_num` INT,
    `day_3min_back_interview_num` INT,
    `day_3min_continue_num` INT,
    `day_3min_interview_num` INT,
    `day_3min_num` INT,
    `day_3min_wechat_num` INT,
    `day_all_backinterview_duration` INT,
    `day_all_backinterview_num` INT,
    `day_back_interview_num` INT,
    `day_back_interview_touch_num` INT,
    `day_bound_discount_retain_num` INT,
    `day_continue_touch_num` INT,
    `day_interview_num` INT,
    `day_interview_touch_num` INT,
    `day_phone_interview_num` INT,
    `day_touch_num` INT,
    `day_wechat_access_num` INT,
    `day_wechat_duration` INT,
    `day_wechat_num` INT,
    `day_wechat_touch_num` INT,
    `deer_programming_reg_num` INT,
    `deer_programming_target_num` INT,
    `delete_num` INT,
    `homevisit_num` INT,
    `interview_access_num` INT,
    `interview_must_label_num` INT,
    `interview_num` INT,
    `long_season_l2r_num` INT,
    `manual_enterprise_wechat_num` INT,
    `manual_wechat_add_num` INT,
    `need_retain_num` INT,
    `need_retain_user_join_reg_num` INT,
    `need_retain_user_l2r_num` INT,
    `not_bound_discound_l2r_num` INT,
    `not_bound_discound_retain_num` INT,
    `not_bound_discount_num` INT,
    `not_bound_l2r_discound_retain_num` INT,
    `not_continue_num` INT,
    `not_join_reg_num` INT,
    `not_retain_phone_access_num` INT,
    `not_retain_phone_cover_num` INT,
    `once_l2r_num` INT,
    `phone_back_interview_num` INT,
    `phone_backinterview_standard_num` INT,
    `phone_cover_access_num` INT,
    `phone_cover_num` INT,
    `phone_interview_num` INT,
    `price_guaranteed_continue_level_num_2` INT,
    `price_guaranteed_daily_continue_level_num_2` INT,
    `price_guaranteed_join_reg_num` INT,
    `price_guaranteed_num` INT,
    `questionnaire_num` INT,
    `refund_num` INT,
    `reg_num_continue` INT,
    `reg_num_other` INT,
    `reg_num_refund` INT,
    `retain_num` INT,
    `retain_subtrade_id_num` INT,
    `save_time` INT,
    `service_survey_score_double` INT,
    `should_test_num` INT,
    `single_continue_merge_new1` INT,
    `space_season_l2r_bound_num` INT,
    `space_season_l2r_num` INT,
    `student_num` INT,
    `three_phone_backinterview_num` INT,
    `three_phone_interview_num` INT,
    `transfer_bound_subtrade_id_num` INT,
    `transfer_status` INT,
    `transfer_subtrade_id_num` INT,
    `type11_access_num` INT,
    `update_time` BIGINT,
    `user_a_l2r_bound_num` INT,
    `user_a_l2r_num` INT,
    `user_a_num` INT,
    `user_b_l2r_bound_num` INT,
    `user_b_l2r_num` INT,
    `user_b_num` INT,
    `user_c_l2r_bound_num` INT,
    `user_c_l2r_num` INT,
    `user_c_num` INT,
    `user_d_l2r_bound_num` INT,
    `user_d_l2r_num` INT,
    `user_d_num` INT,
    `user_l_l2r_bound_num` INT,
    `user_l_l2r_num` INT,
    `user_l_num` INT,
    `user_n_l2r_bound_num` INT,
    `user_n_l2r_num` INT,
    `user_n_num` INT,
    `user_nl_l2r_bound_num` INT,
    `user_nl_l2r_num` INT,
    `user_nl_num` INT,
    `user_type` STRING,
    `valid_session_count` INT,
    `visit_label_num` INT,
    `wechat_add_num` INT,
    `wechat_bind_num` INT,
    `wechat_msg_num` INT,
    `wechat_reply_num` INT,
    `wechat_reply_cnt_v1` BIGINT,
    `not_space_season_l2r_phone_cover_num` INT,
    `not_space_season_l2r_phone_cover_access_num` INT,
    `day_space_season_l2r_num` INT,
    `day_space_season_l2r_bound_num` INT,
    `leap_single_num` BIGINT,
    `leap_single_l2r_num` BIGINT,
    `single_suject_num` BIGINT,
    `single_suject_expand_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopca',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_tbldatasopca (
    `access_num` INT,
    `add_backinterview_duration` INT,
    `add_backinterview_num` INT,
    `add_interview_duration` INT,
    `add_interview_num` INT,
    `after_class_phone_back_interview_1min_num` INT,
    `after_class_phone_back_interview_num` INT,
    `after_classrefund_num` INT,
    `all_backinterview_num` INT,
    `all_backinterview_standard_num` INT,
    `all_duration` INT,
    `all_enterprise_wechat_num` INT,
    `all_num` INT,
    `all_wechat_add_num` INT,
    `assistant_uid` BIGINT,
    `automaticl_enterprise_wechat_num` INT,
    `back_interview_num` INT,
    `before2_reg_num` INT,
    `before_reg_num` INT,
    `bound_conversion_reg_num` INT,
    `bound_reserved_reg_num` INT,
    `bound_retain_num` INT,
    `change_num` INT,
    `complete_test_num` INT,
    `continue_access_num` INT,
    `continue_label_num` INT,
    `continue_level_num_1` INT,
    `continue_level_num_2` INT,
    `continue_oncelevel_num` INT,
    `course_id` INT,
    `daily_change` INT,
    `daily_not_bound_l2r_num` INT,
    `day_3min_back_interview_num` INT,
    `day_3min_continue_num` INT,
    `day_3min_interview_num` INT,
    `day_3min_num` INT,
    `day_3min_wechat_num` INT,
    `day_all_backinterview_duration` INT,
    `day_all_backinterview_num` INT,
    `day_back_interview_num` INT,
    `day_back_interview_touch_num` INT,
    `day_bound_discount_retain_num` INT,
    `day_continue_touch_num` INT,
    `day_interview_num` INT,
    `day_interview_touch_num` INT,
    `day_phone_interview_num` INT,
    `day_touch_num` INT,
    `day_wechat_access_num` INT,
    `day_wechat_duration` INT,
    `day_wechat_num` INT,
    `day_wechat_touch_num` INT,
    `deer_programming_reg_num` INT,
    `deer_programming_target_num` INT,
    `delete_num` INT,
    `homevisit_num` INT,
    `interview_access_num` INT,
    `interview_must_label_num` INT,
    `interview_num` INT,
    `long_season_l2r_num` INT,
    `manual_enterprise_wechat_num` INT,
    `manual_wechat_add_num` INT,
    `need_retain_num` INT,
    `need_retain_user_join_reg_num` INT,
    `need_retain_user_l2r_num` INT,
    `not_bound_discound_l2r_num` INT,
    `not_bound_discound_retain_num` INT,
    `not_bound_discount_num` INT,
    `not_bound_l2r_discound_retain_num` INT,
    `not_continue_num` INT,
    `not_join_reg_num` INT,
    `not_retain_phone_access_num` INT,
    `not_retain_phone_cover_num` INT,
    `once_l2r_num` INT,
    `phone_back_interview_num` INT,
    `phone_backinterview_standard_num` INT,
    `phone_cover_access_num` INT,
    `phone_cover_num` INT,
    `phone_interview_num` INT,
    `price_guaranteed_continue_level_num_2` INT,
    `price_guaranteed_daily_continue_level_num_2` INT,
    `price_guaranteed_join_reg_num` INT,
    `price_guaranteed_num` INT,
    `questionnaire_num` INT,
    `refund_num` INT,
    `reg_num_continue` INT,
    `reg_num_other` INT,
    `reg_num_refund` INT,
    `retain_num` INT,
    `retain_subtrade_id_num` INT,
    `save_time` INT,
    `service_survey_score_double` INT,
    `should_test_num` INT,
    `single_continue_merge_new1` INT,
    `space_season_l2r_bound_num` INT,
    `space_season_l2r_num` INT,
    `student_num` INT,
    `three_phone_backinterview_num` INT,
    `three_phone_interview_num` INT,
    `transfer_bound_subtrade_id_num` INT,
    `transfer_status` INT,
    `transfer_subtrade_id_num` INT,
    `type11_access_num` INT,
    `update_time` BIGINT,
    `user_a_l2r_bound_num` INT,
    `user_a_l2r_num` INT,
    `user_a_num` INT,
    `user_b_l2r_bound_num` INT,
    `user_b_l2r_num` INT,
    `user_b_num` INT,
    `user_c_l2r_bound_num` INT,
    `user_c_l2r_num` INT,
    `user_c_num` INT,
    `user_d_l2r_bound_num` INT,
    `user_d_l2r_num` INT,
    `user_d_num` INT,
    `user_l_l2r_bound_num` INT,
    `user_l_l2r_num` INT,
    `user_l_num` INT,
    `user_n_l2r_bound_num` INT,
    `user_n_l2r_num` INT,
    `user_n_num` INT,
    `user_nl_l2r_bound_num` INT,
    `user_nl_l2r_num` INT,
    `user_nl_num` INT,
    `user_type` STRING,
    `valid_session_count` INT,
    `visit_label_num` INT,
    `wechat_add_num` INT,
    `wechat_bind_num` INT,
    `wechat_msg_num` INT,
    `wechat_reply_num` INT,
    `wechat_reply_cnt_v1` BIGINT,
    `not_space_season_l2r_phone_cover_num` INT,
    `not_space_season_l2r_phone_cover_access_num` INT,
    `day_space_season_l2r_num` INT,
    `day_space_season_l2r_bound_num` INT,
    `leap_single_num` BIGINT,
    `leap_single_l2r_num` BIGINT,
    `single_suject_num` BIGINT,
    `single_suject_expand_num` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_tbldatasopca',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_tbldatasopca
SELECT
    `access_num`,
    `add_backinterview_duration`,
    `add_backinterview_num`,
    `add_interview_duration`,
    `add_interview_num`,
    `after_class_phone_back_interview_1min_num`,
    `after_class_phone_back_interview_num`,
    `after_classrefund_num`,
    `all_backinterview_num`,
    `all_backinterview_standard_num`,
    `all_duration`,
    `all_enterprise_wechat_num`,
    `all_num`,
    `all_wechat_add_num`,
    `assistant_uid`,
    `automaticl_enterprise_wechat_num`,
    `back_interview_num`,
    `before2_reg_num`,
    `before_reg_num`,
    `bound_conversion_reg_num`,
    `bound_reserved_reg_num`,
    `bound_retain_num`,
    `change_num`,
    `complete_test_num`,
    `continue_access_num`,
    `continue_label_num`,
    `continue_level_num_1`,
    `continue_level_num_2`,
    `continue_oncelevel_num`,
    `course_id`,
    `daily_change`,
    `daily_not_bound_l2r_num`,
    `day_3min_back_interview_num`,
    `day_3min_continue_num`,
    `day_3min_interview_num`,
    `day_3min_num`,
    `day_3min_wechat_num`,
    `day_all_backinterview_duration`,
    `day_all_backinterview_num`,
    `day_back_interview_num`,
    `day_back_interview_touch_num`,
    `day_bound_discount_retain_num`,
    `day_continue_touch_num`,
    `day_interview_num`,
    `day_interview_touch_num`,
    `day_phone_interview_num`,
    `day_touch_num`,
    `day_wechat_access_num`,
    `day_wechat_duration`,
    `day_wechat_num`,
    `day_wechat_touch_num`,
    `deer_programming_reg_num`,
    `deer_programming_target_num`,
    `delete_num`,
    `homevisit_num`,
    `interview_access_num`,
    `interview_must_label_num`,
    `interview_num`,
    `long_season_l2r_num`,
    `manual_enterprise_wechat_num`,
    `manual_wechat_add_num`,
    `need_retain_num`,
    `need_retain_user_join_reg_num`,
    `need_retain_user_l2r_num`,
    `not_bound_discound_l2r_num`,
    `not_bound_discound_retain_num`,
    `not_bound_discount_num`,
    `not_bound_l2r_discound_retain_num`,
    `not_continue_num`,
    `not_join_reg_num`,
    `not_retain_phone_access_num`,
    `not_retain_phone_cover_num`,
    `once_l2r_num`,
    `phone_back_interview_num`,
    `phone_backinterview_standard_num`,
    `phone_cover_access_num`,
    `phone_cover_num`,
    `phone_interview_num`,
    `price_guaranteed_continue_level_num_2`,
    `price_guaranteed_daily_continue_level_num_2`,
    `price_guaranteed_join_reg_num`,
    `price_guaranteed_num`,
    `questionnaire_num`,
    `refund_num`,
    `reg_num_continue`,
    `reg_num_other`,
    `reg_num_refund`,
    `retain_num`,
    `retain_subtrade_id_num`,
    `save_time`,
    `service_survey_score_double`,
    `should_test_num`,
    `single_continue_merge_new1`,
    `space_season_l2r_bound_num`,
    `space_season_l2r_num`,
    `student_num`,
    `three_phone_backinterview_num`,
    `three_phone_interview_num`,
    `transfer_bound_subtrade_id_num`,
    `transfer_status`,
    `transfer_subtrade_id_num`,
    `type11_access_num`,
    UNIX_TIMESTAMP() AS `update_time`,
    `user_a_l2r_bound_num`,
    `user_a_l2r_num`,
    `user_a_num`,
    `user_b_l2r_bound_num`,
    `user_b_l2r_num`,
    `user_b_num`,
    `user_c_l2r_bound_num`,
    `user_c_l2r_num`,
    `user_c_num`,
    `user_d_l2r_bound_num`,
    `user_d_l2r_num`,
    `user_d_num`,
    `user_l_l2r_bound_num`,
    `user_l_l2r_num`,
    `user_l_num`,
    `user_n_l2r_bound_num`,
    `user_n_l2r_num`,
    `user_n_num`,
    `user_nl_l2r_bound_num`,
    `user_nl_l2r_num`,
    `user_nl_num`,
    `user_type`,
    `valid_session_count`,
    `visit_label_num`,
    `wechat_add_num`,
    `wechat_bind_num`,
    `wechat_msg_num`,
    `wechat_reply_num`,
    `wechat_reply_cnt_v1`,
    `not_space_season_l2r_phone_cover_num`,
    `not_space_season_l2r_phone_cover_access_num`,
    `day_space_season_l2r_num`,
    `day_space_season_l2r_bound_num`,
    `leap_single_num`,
    `leap_single_l2r_num`,
    `single_suject_num`,
    `single_suject_expand_num`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_tbldatasopca
WHERE
    `hg_binlog_event_type` <> 3;