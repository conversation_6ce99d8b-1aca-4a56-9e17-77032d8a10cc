CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_leads_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `course_mode` BIGINT,
    `person_uid` BIGINT,
    `course_id` BIGINT,
    `is_real_alloc` BIGINT,
    `alloc_time` BIGINT,
    `source` BIGINT,
    `activity_id` BIGINT
) WITH (
     'connector'='hologres',
     'dbname'='zyb_zbk_bzr',
     'tablename'='zyb_zbk_lpc_dws.dws_zbk_baseinfo_leads_lpc',
     'username' = '${hologres.username}',
     'password' = '${hologres.password}',
     'binlogMaxRetryTimes'='10',
     'binlogRetryIntervalMs'='500',
     'binlogBatchReadSize'='100',
     'binlog'='true',
     'cdcMode'='false',
     'endpoint'='${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE zyb_zbk_lpc_ads_idl_lpc_leads (
    `leads_id` BIGINT,
    `has_course` BIGINT,
    `lpc_uid` BIGINT,
    `course_id` BIGINT,
    `is_real_alloc` BIGINT,
    `lpc_alloc_time` BIGINT,
    `leads_source` BIGINT,
    `activity_id` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.idl_lpc_leads',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);

INSERT INTO
    zyb_zbk_lpc_ads_idl_lpc_leads
SELECT
    `leads_id`,
    `course_mode` AS `has_course`,
    `person_uid` AS `lpc_uid`,
    `course_id`,
    `is_real_alloc`,
    `alloc_time` AS `lpc_alloc_time`,
    `source` AS `leads_source`,
    `activity_id`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_leads_lpc
WHERE
    `hg_binlog_event_type` IN(5, 7);