CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_ca_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `person_uid` BIGINT,
    `leader_course_id` BIGINT,
    `first_channel_id` BIGINT,
    `second_channel_id` BIGINT,
    `attend_num` BIGINT,
    `attend_finish_num` BIGINT,
    `playback_num` BIGINT,
    `playback_finish_num` BIGINT,
    `no_attend_playback_leads_num` BIGINT,
    `attend_n_lesson_num` STRING,
    `finish_n_lesson_num` STRING,
    `reg_leads_num` BIGINT,
    `buy_leads_num` BIGINT,
    `need_attend_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_ca_sub_course_lpc',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);
-- ddl
CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_sub_course_data (
    `lpc_uid` BIGINT,
    `course_id` BIGINT,
    `leader_course_id` BIGINT,
    `first_channel_id` STRING,
    `second_channel_id` STRING,
    `leads_num` BIGINT,
    `attend_num` BIGINT,
    `finish_num` BIGINT,
    `playback_num` BIGINT,
    `buy_leads_num` BIGINT,
    `should_attend_num` BIGINT,
    `attend_n_lesson_num` STRING,
    `finish_n_lesson_num` STRING,
    `finish_playback_num` BIGINT,
    `not_attend_playback_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_sub_course_data',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
-- sub
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_sub_course_data
SELECT
    person_uid AS lpc_uid,
    course_id,
    leader_course_id,
    CAST(first_channel_id AS STRING) AS first_channel_id,
    CAST(second_channel_id AS STRING) AS second_channel_id,
    reg_leads_num AS leads_num,
    attend_num AS attend_num,
    attend_finish_num AS finish_num,
    playback_num AS playback_num,
    buy_leads_num AS buy_leads_num,
    need_attend_num AS should_attend_num,
    COALESCE(attend_n_lesson_num, '{}') AS attend_n_lesson_num,
    COALESCE(finish_n_lesson_num, '{}') AS finish_n_lesson_num,
    playback_finish_num AS finish_playback_num,
    no_attend_playback_leads_num AS not_attend_playback_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_ca_sub_course_lpc
WHERE
    hg_binlog_event_type IN(5, 7);