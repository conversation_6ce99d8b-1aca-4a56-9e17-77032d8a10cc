-- 声明用到的所有表
CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_la_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `leader_course_id` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `person_uid` BIGINT,
    `first_channel_id` BIGINT,
    `second_channel_id` BIGINT,
    `reg_leads_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_la_sub_course_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
-- ddl
CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_give_lesson_data (
    `leader_course_id` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` STRING,
    `second_channel_id` STRING,
    `reg_leads_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_give_lesson_data',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_give_lesson_data
SELECT
    leader_course_id,
    course_id,
    lesson_id,
    person_uid AS lpc_uid,
    CAST(first_channel_id AS STRING),
    CAST(second_channel_id AS STRING),
    reg_leads_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_la_sub_course_lpc
WHERE
    `hg_binlog_event_type` IN (5, 7);