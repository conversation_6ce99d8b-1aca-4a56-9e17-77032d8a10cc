CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_svc_trans_hascourse_leads_trade_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `trade_id` BIGINT,
    `person_uid` BIGINT,
    `is_trans` BIGINT,
    `first_channel_id` BIGINT,
    `trade_time` BIGINT,
    `course_id` BIGINT,
    `trans_pv` DECIMAL(10, 2),
    `trans_uv` DECIMAL(10, 2),
    `trans_gmv` DECIMAL(10, 2),
    `refund_pv` DECIMAL(10, 2),
    `refund_gmv` DECIMAL(10, 2),
    `class_before_refund_pv` DECIMAL(10, 2),
    `class_after_refund_pv` DECIMAL(10, 2),
    `han_trans_pv` DECIMAL(10, 2),
    `han_trans_gmv` DECIMAL(10, 2),
    `han_trans_uv` DECIMAL(10, 2),
    `chun_trans_pv` DECIMAL(10, 2),
    `chun_trans_gmv` DECIMAL(10, 2),
    `chun_trans_uv` DECIMAL(10, 2),
    `shu_trans_pv` DECIMAL(10, 2),
    `shu_trans_gmv` DECIMAL(10, 2),
    `shu_trans_uv` DECIMAL(10, 2),
    `qiu_trans_pv` DECIMAL(10, 2),
    `qiu_trans_gmv` DECIMAL(10, 2),
    `qiu_trans_uv` DECIMAL(10, 2),
    `bound_pv` DECIMAL(10, 2),
    `bound_amount_pv` DECIMAL(10, 2),
    `bound_gmv` DECIMAL(10, 2),
    `preschool_trans_pv` DECIMAL(10, 2),
    `primary_school_trans_pv` DECIMAL(10, 2),
    `junior_school_trans_pv` DECIMAL(10, 2),
    `senior_high_school_trans_pv` DECIMAL(10, 2),
    `preschool_trans_uv` BIGINT,
    `primary_school_trans_uv` BIGINT,
    `junior_school_trans_uv` BIGINT,
    `senior_high_school_trans_uv` BIGINT,
    `detail_pv` VARCHAR,
    `detail_uv` VARCHAR,
    `detail_department_subject_gmv` VARCHAR,
    `detail_department_subject_pv` VARCHAR,
    `detail_department_subject_uv` VARCHAR,
    `shu_qiu_bound_pv` DECIMAL(10, 2),
    `han_chun_bound_pv` DECIMAL(10, 2)
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = 'LTAI5tG16GTBncxpCa7ggDdn',
    'password' = '******************************',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_svc_trans_hascourse_leads_trade_agg_lpc',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);
-- dd
CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_course_leads_trans_agg (
    `leads_id` BIGINT,
    `trade_id` BIGINT,
    `lpc_uid` BIGINT,
    `is_trans` BIGINT,
    `first_channel_id` BIGINT,
    `trade_time` BIGINT,
    `course_id` BIGINT,
    `trans_pv` DECIMAL(10, 2),
    `trans_uv` DECIMAL(10, 2),
    `trans_gmv` DECIMAL(10, 2),
    `refund_pv` DECIMAL(10, 2),
    `refund_gmv` DECIMAL(10, 2),
    `class_before_refund_pv` DECIMAL(10, 2),
    `class_after_refund_pv` DECIMAL(10, 2),
    `han_trans_pv` DECIMAL(10, 2),
    `han_trans_gmv` DECIMAL(10, 2),
    `han_trans_uv` DECIMAL(10, 2),
    `chun_trans_pv` DECIMAL(10, 2),
    `chun_trans_gmv` DECIMAL(10, 2),
    `chun_trans_uv` DECIMAL(10, 2),
    `shu_trans_pv` DECIMAL(10, 2),
    `shu_trans_gmv` DECIMAL(10, 2),
    `shu_trans_uv` DECIMAL(10, 2),
    `qiu_trans_pv` DECIMAL(10, 2),
    `qiu_trans_gmv` DECIMAL(10, 2),
    `qiu_trans_uv` DECIMAL(10, 2),
    `bound_pv` DECIMAL(10, 2),
    `bound_amount_pv` DECIMAL(10, 2),
    `bound_gmv` DECIMAL(10, 2),
    `preschool_trans_pv` DECIMAL(10, 2),
    `primary_school_trans_pv` DECIMAL(10, 2),
    `junior_school_trans_pv` DECIMAL(10, 2),
    `senior_high_school_trans_pv` DECIMAL(10, 2),
    `preschool_trans_uv` BIGINT,
    `primary_school_trans_uv` BIGINT,
    `junior_school_trans_uv` BIGINT,
    `senior_high_school_trans_uv` BIGINT,
    `detail_pv` VARCHAR,
    `detail_uv` VARCHAR,
    `detail_department_subject_gmv` VARCHAR,
    `detail_department_subject_pv` VARCHAR,
    `detail_department_subject_uv` VARCHAR,
    `shu_qiu_bound_pv` DECIMAL(10, 2),
    `han_chun_bound_pv` DECIMAL(10, 2),
    `zbk_update_time` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_course_leads_trans_agg',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
-- sink
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_course_leads_trans_agg
SELECT
    `leads_id`,
    `trade_id`,
    COALESCE(`person_uid`, 0) AS `lpc_uid`,
    `is_trans`,
    `first_channel_id`,
    COALESCE(`trade_time`, 0) AS `trade_time`,
    COALESCE(`course_id`, 0) AS `course_id`,
    `trans_pv`,
    `trans_uv`,
    `trans_gmv`,
    `refund_pv`,
    `refund_gmv`,
    `class_before_refund_pv`,
    `class_after_refund_pv`,
    `han_trans_pv`,
    `han_trans_gmv`,
    `han_trans_uv`,
    `chun_trans_pv`,
    `chun_trans_gmv`,
    `chun_trans_uv`,
    `shu_trans_pv`,
    `shu_trans_gmv`,
    `shu_trans_uv`,
    `qiu_trans_pv`,
    `qiu_trans_gmv`,
    `qiu_trans_uv`,
    `bound_pv`,
    `bound_amount_pv`,
    `bound_gmv`,
    `preschool_trans_pv`,
    `primary_school_trans_pv`,
    `junior_school_trans_pv`,
    `senior_high_school_trans_pv`,
    `preschool_trans_uv`,
    `primary_school_trans_uv`,
    `junior_school_trans_uv`,
    `senior_high_school_trans_uv`,
    `detail_pv`,
    `detail_uv`,
    `detail_department_subject_gmv`,
    `detail_department_subject_pv`,
    `detail_department_subject_uv`,
    `shu_qiu_bound_pv`,
    `han_chun_bound_pv`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    zyb_zbk_lpc_dws_dws_zbk_svc_trans_hascourse_leads_trade_agg_lpc
WHERE
    hg_binlog_event_type IN(5, 7);