CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_la_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `person_uid` BIGINT,
    `first_channel_id` BIGINT,
    `attend_leads_num` BIGINT,
    `attend_finish_leads_num` BIGINT,
    `playback_leads_num` BIGINT,
    `no_attend_playback_leads_num` BIGINT,
    `participate_class_leads_num` BIGINT,
    `unlock_playback_attend_leads_num` BIGINT,
    `unlock_playback_finish_leads_num` BIGINT,
    `all_attend_leads_num` BIGINT,
    `all_attend_finish_leads_num` BIGINT,
    `ai_attend_leads_num` BIGINT,
    `ai_attend_finish_leads_num` BIGINT,
    `playback_finish_leads_num` BIGINT,
    `lbp_attend_leads_num` BIGINT,
    `lbp_finish_leads_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_la_agg_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
-- ddl
CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_lesson_data (
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` BIGINT,
    `attend_num` BIGINT,
    `attend_finish_num` BIGINT,
    `playback_num` BIGINT,
    `no_attend_playback_leads_num` BIGINT,
    `participate_class_num` BIGINT,
    `unlock_playback_attend_num` BIGINT,
    `unlock_playback_finish_num` BIGINT,
    `all_attend_num` BIGINT,
    `all_attend_finish_num` BIGINT,
    `ai_attend_num` BIGINT,
    `ai_attend_finish_num` BIGINT,
    `playback_finish_num` BIGINT,
    `lbp_attend_num` BIGINT,
    `lbp_finish_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_lesson_data',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
-- attendance
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_lesson_data
SELECT
    course_id,
    lesson_id,
    person_uid AS lpc_uid,
    first_channel_id,
    attend_leads_num AS attend_num,
    attend_finish_leads_num AS attend_finish_num,
    playback_leads_num AS playback_num,
    no_attend_playback_leads_num,
    participate_class_leads_num AS participate_class_num,
    unlock_playback_attend_leads_num AS unlock_playback_attend_num,
    unlock_playback_finish_leads_num AS unlock_playback_finish_num,
    all_attend_leads_num AS all_attend_num,
    all_attend_finish_leads_num AS all_attend_finish_num,
    ai_attend_leads_num AS ai_attend_num,
    ai_attend_finish_leads_num AS ai_attend_finish_num,
    playback_finish_leads_num AS playback_finish_num,
    lbp_attend_leads_num AS lbp_attend_num,
    lbp_finish_leads_num AS lbp_finish_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_la_agg_lpc
WHERE
    `hg_binlog_event_type` IN (5, 7);