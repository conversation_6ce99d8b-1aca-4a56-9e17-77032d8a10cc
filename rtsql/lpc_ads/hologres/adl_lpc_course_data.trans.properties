CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_lpc_trans_compound_agg (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `person_uid` BIGINT,
    `first_channel_id` BIGINT,
    `add_wechat_trans_pv` DECIMAL(10, 2),
    attend_trans_pv DECIMAL(10, 2),
    attend_trans_uv DECIMAL(10, 2),
    bound_amount_pv DECIMAL(10, 2),
    bound_gmv DECIMAL(10, 2),
    bound_pv DECIMAL(10, 2),
    chun_trans_gmv DECIMAL(10, 2),
    chun_trans_pv DECIMAL(10, 2),
    chun_trans_uv BIGINT,
    class_after_refund_pv DECIMAL(10, 2),
    class_before_refund_pv DECIMAL(10, 2),
    detail_attend_pv VARCHAR,
    detail_department_subject_gmv VARCHAR,
    detail_department_subject_pv VARCHAR,
    detail_department_subject_uv VARCHAR,
    detail_gmv VARCHAR,
    detail_pv VARCHAR,
    detail_uv VARCHAR,
    finish_playback_trans_pv DECIMAL(10, 2),
    finish_playback_trans_uv BIGINT,
    finish_trans_pv DECIMAL(10, 2),
    finish_trans_uv DECIMAL(10, 2),
    han_chun_bound_pv DECIMAL(10, 2),
    han_trans_gmv DECIMAL(10, 2),
    han_trans_pv DECIMAL(10, 2),
    han_trans_uv DECIMAL(10, 2),
    junior_school_trans_pv DECIMAL(10, 2),
    junior_school_trans_uv DECIMAL(10, 2),
    playback_trans_pv DECIMAL(10, 2),
    playback_trans_uv DECIMAL(10, 2),
    preschool_trans_pv DECIMAL(10, 2),
    preschool_trans_uv DECIMAL(10, 2),
    primary_school_trans_pv DECIMAL(10, 2),
    primary_school_trans_uv DECIMAL(10, 2),
    qiu_trans_gmv DECIMAL(10, 2),
    qiu_trans_pv DECIMAL(10, 2),
    qiu_trans_uv DECIMAL(10, 2),
    refund_gmv DECIMAL(10, 2),
    refund_pv DECIMAL(10, 2),
    sale_trans_pv DECIMAL(10, 2),
    sale_trans_uv DECIMAL(10, 2),
    senior_high_school_trans_pv DECIMAL(10, 2),
    senior_high_school_trans_uv DECIMAL(10, 2),
    shu_qiu_bound_pv DECIMAL(10, 2),
    shu_trans_gmv DECIMAL(10, 2),
    shu_trans_pv DECIMAL(10, 2),
    shu_trans_uv DECIMAL(10, 2),
    trans_gmv DECIMAL(10, 2),
    trans_pv DECIMAL(10, 2),
    trans_uv DECIMAL(10, 2)
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_lpc_trans_compound_agg',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_course_data (
    `course_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` BIGINT,
    `add_wechat_trans_pv` DECIMAL(10, 2),
    attend_trans_pv DECIMAL(10, 2),
    attend_trans_uv DECIMAL(10, 2),
    bound_amount_pv DECIMAL(10, 2),
    bound_gmv DECIMAL(10, 2),
    bound_pv DECIMAL(10, 2),
    chun_trans_gmv DECIMAL(10, 2),
    chun_trans_pv DECIMAL(10, 2),
    chun_trans_uv BIGINT,
    class_after_refund_pv DECIMAL(10, 2),
    class_before_refund_pv DECIMAL(10, 2),
    detail_attend_pv VARCHAR,
    detail_department_subject_gmv VARCHAR,
    detail_department_subject_pv VARCHAR,
    detail_department_subject_uv VARCHAR,
    detail_gmv VARCHAR,
    detail_pv VARCHAR,
    detail_uv VARCHAR,
    finish_playback_trans_pv DECIMAL(10, 2),
    finish_playback_trans_uv BIGINT,
    finish_trans_pv DECIMAL(10, 2),
    finish_trans_uv DECIMAL(10, 2),
    han_chun_bound_pv DECIMAL(10, 2),
    han_trans_gmv DECIMAL(10, 2),
    han_trans_pv DECIMAL(10, 2),
    han_trans_uv BIGINT,
    junior_school_trans_pv DECIMAL(10, 2),
    junior_school_trans_uv BIGINT,
    playback_trans_pv DECIMAL(10, 2),
    playback_trans_uv BIGINT,
    preschool_trans_pv DECIMAL(10, 2),
    preschool_trans_uv BIGINT,
    primary_school_trans_pv DECIMAL(10, 2),
    primary_school_trans_uv BIGINT,
    qiu_trans_gmv DECIMAL(10, 2),
    qiu_trans_pv DECIMAL(10, 2),
    qiu_trans_uv BIGINT,
    refund_gmv DECIMAL(10, 2),
    refund_pv DECIMAL(10, 2),
    sale_trans_pv DECIMAL(10, 2),
    sale_trans_uv BIGINT,
    senior_high_school_trans_pv DECIMAL(10, 2),
    senior_high_school_trans_uv BIGINT,
    shu_qiu_bound_pv DECIMAL(10, 2),
    shu_trans_gmv DECIMAL(10, 2),
    shu_trans_pv DECIMAL(10, 2),
    shu_trans_uv BIGINT,
    trans_gmv DECIMAL(10, 2),
    trans_pv DECIMAL(10, 2),
    trans_uv BIGINT,
    zbk_update_time BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_course_data',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_course_data
SELECT
    `course_id`,
    `person_uid` AS lpc_uid,
    `first_channel_id`,
    `add_wechat_trans_pv`,
    attend_trans_pv,
    attend_trans_uv,
    bound_amount_pv,
    bound_gmv,
    bound_pv,
    chun_trans_gmv,
    chun_trans_pv,
    chun_trans_uv,
    class_after_refund_pv,
    class_before_refund_pv,
    detail_attend_pv,
    detail_department_subject_gmv,
    detail_department_subject_pv,
    detail_department_subject_uv,
    detail_gmv,
    detail_pv,
    detail_uv,
    finish_playback_trans_pv,
    finish_playback_trans_uv,
    finish_trans_pv,
    finish_trans_uv,
    han_chun_bound_pv,
    han_trans_gmv,
    han_trans_pv,
    CAST(han_trans_uv AS BIGINT) AS han_trans_uv,
    junior_school_trans_pv,
    CAST(junior_school_trans_uv AS BIGINT) AS junior_school_trans_uv,
    playback_trans_pv,
    CAST(playback_trans_uv AS BIGINT) AS playback_trans_uv,
    preschool_trans_pv,
    CAST(preschool_trans_uv AS BIGINT) AS preschool_trans_uv,
    primary_school_trans_pv,
    CAST(primary_school_trans_uv AS BIGINT) AS primary_school_trans_uv,
    qiu_trans_gmv,
    qiu_trans_pv,
    CAST(qiu_trans_uv AS BIGINT) AS qiu_trans_uv,
    refund_gmv,
    refund_pv,
    sale_trans_pv,
    CAST(sale_trans_uv AS BIGINT) AS sale_trans_uv,
    senior_high_school_trans_pv,
    CAST(senior_high_school_trans_uv AS BIGINT) AS senior_high_school_trans_uv,
    shu_qiu_bound_pv,
    shu_trans_gmv,
    shu_trans_pv,
    CAST(shu_trans_uv AS BIGINT) AS shu_trans_uv,
    trans_gmv,
    trans_pv,
    CAST(trans_uv AS BIGINT) AS trans_uv,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_lpc_trans_compound_agg
WHERE
    hg_binlog_event_type IN(5, 7);