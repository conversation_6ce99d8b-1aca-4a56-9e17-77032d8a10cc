CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_exam_la_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `person_uid` BIGINT,
    `first_channel_id` BIGINT,
    `preview_finish_leads_num` BIGINT,
    `homework_submit_leads_num` BIGINT,
    `before_expire_homework_submit_leads_num` BIGINT,
    `first_all_right_homework_submit_leads_num` BIGINT,
    `corrections_homework_24h_leads_num` BIGINT,
    `homework_amend_leads_num` BIGINT,
    `corrections_homework_48h_leads_num` BIGINT,
    `waiting_for_corrections_homework_leads_num` BIGINT,
    `waiting_for_corrections_again_homework_leads_num` BIGINT,
    `waiting_for_submission_again_homework_leads_num` BIGINT,
    `corrections_homework_72h_leads_num` BIGINT,
    `exam9_submit_leads_num` BIGINT,
    `exam10_submit_leads_num` BIGINT,
    `exam10_submit_question_num` BIGINT,
    `exam10_right_question_num` BIGINT,
    `exam10_total_question_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_exam_la_agg_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
-- ddl
CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_lesson_data (
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` BIGINT,
    `preview_finish_num` BIGINT,
    `homework_submit_num` BIGINT,
    `before_expire_homework_submit_num` BIGINT,
    `first_all_right_homework_submit_num` BIGINT,
    `corrections_homework_num24` BIGINT,
    `homework_amend_num` BIGINT,
    `corrections_homework_num48` BIGINT,
    `waiting_for_corrections_homework_num` BIGINT,
    `waiting_for_corrections_again_homework_num` BIGINT,
    `waiting_for_submission_again_homework_num` BIGINT,
    `corrections_homework_num72` BIGINT,
    `exam9_submit_num` BIGINT,
    `exam10_submit_num` BIGINT,
    `exam10_submit_question_num` BIGINT,
    `exam10_right_question_num` BIGINT,
    `exam10_total_question_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_lesson_data',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
-- exam
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_lesson_data
SELECT
    course_id,
    lesson_id,
    person_uid AS lpc_uid,
    first_channel_id,
    preview_finish_leads_num AS preview_finish_num,
    homework_submit_leads_num AS homework_submit_num,
    before_expire_homework_submit_leads_num AS before_expire_homework_submit_num,
    first_all_right_homework_submit_leads_num AS first_all_right_homework_submit_num,
    corrections_homework_24h_leads_num AS corrections_homework_num24,
    homework_amend_leads_num AS homework_amend_num,
    corrections_homework_48h_leads_num AS corrections_homework_num48,
    waiting_for_corrections_homework_leads_num AS waiting_for_corrections_homework_num,
    waiting_for_corrections_again_homework_leads_num AS waiting_for_corrections_again_homework_num,
    waiting_for_submission_again_homework_leads_num AS waiting_for_submission_again_homework_num,
    corrections_homework_72h_leads_num AS corrections_homework_num72,
    exam9_submit_leads_num AS exam9_submit_num,
    exam10_submit_leads_num AS exam10_submit_num,
    exam10_submit_question_num,
    exam10_right_question_num,
    exam10_total_question_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_exam_la_agg_lpc
WHERE
    `hg_binlog_event_type` IN (5, 7);