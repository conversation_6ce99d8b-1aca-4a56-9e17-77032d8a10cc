CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_sop_outbound_today_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    course_id BIGINT,
    person_uid BIGINT,
    leads_num BIGINT,
    call_out_leads_num BIGINT,
    call_access_leads_num BIGINT,
    call_valid_access_leads_num BIGINT,
    call_duration BIGINT,
    call_duration_per_access_leads BIGINT,
    call_out_num BIGINT,
    call_valid_access_num BIGINT,
    attend_call_leads_num BIGINT,
    finish_call_leads_num BIGINT,
    attend_num BIGINT,
    finish_num BIGINT,
    add_wechat_num BIGINT,
    not_add_wechat_call_leads_num BIGINT,
    not_add_wechat_call_num BIGINT,
    playback_leads_num BIGINT,
    playback_call_leads_num BIGINT,
    save_time BIGINT,
    first_channel_id BIGINT,
    whole_call_out_leads_num BIGINT,
    whole_call_access_leads_num BIGINT,
    whole_call_valid_access_leads_num BIGINT,
    whole_call_duration BIGINT,
    whole_lpc_call_num BIGINT,
    whole_lpc_call_valid_num BIGINT,
    all_leads_num BIGINT,
    access_num_5min BIGINT,
    access_num_8min BIGINT,
    access_leads_num_5min BIGINT,
    access_leads_num_8min BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_sop_outbound_today_agg_lpc',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);
-- ddl
CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_course_by_day (
    `course_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` BIGINT,
    `save_time` BIGINT,
    `leads_num` BIGINT,
    `call_out_num` BIGINT,
    `call_access_num` BIGINT,
    `call_valid_access_num` BIGINT,
    `call_duration` BIGINT,
    `avg_access_duration` BIGINT,
    `update_time` BIGINT,
    `due_today_attend_num` BIGINT,
    `due_today_finish_num` BIGINT,
    `attend_call_num` BIGINT,
    `finish_call_num` BIGINT,
    `total_call_num` BIGINT,
    `total_call_valid_num` BIGINT,
    `due_today_add_wechat_num` BIGINT,
    `not_add_wechat_call_num` BIGINT,
    `not_add_wechat_total_call_num` BIGINT,
    `playback_action_num` BIGINT,
    `playback_action_call_num` BIGINT,
    `whole_call_num` BIGINT,
    `whole_call_access_num` BIGINT,
    `whole_call_valid_num` BIGINT,
    `whole_call_duration` BIGINT,
    `whole_lpc_call_num` BIGINT,
    `whole_lpc_call_valid_num` BIGINT,
    `all_leads_num` BIGINT,
    `lpc_phone_across_gt_5min_num` BIGINT,
    `lpc_phone_across_gt_8min_num` BIGINT,
    `lpc_phone_across_gt_5min_lead_num` BIGINT,
    `lpc_phone_across_gt_8min_lead_num` BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_course_by_day',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
-- sink
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_course_by_day
SELECT
    `course_id`,
    person_uid AS lpc_uid,
    `first_channel_id`,
    `save_time`,
    `leads_num`,
    `call_out_leads_num` AS call_out_num,
    `call_access_leads_num` AS call_access_num,
    `call_valid_access_leads_num` AS call_valid_access_num,
    `call_duration`,
    `call_duration_per_access_leads` AS avg_access_duration,
    UNIX_TIMESTAMP() AS update_time,
    `attend_num` AS due_today_attend_num,
    `finish_num` AS due_today_finish_num,
    `attend_call_leads_num` AS attend_call_num,
    `finish_call_leads_num` AS finish_call_num,
    `call_out_leads_num` AS total_call_num,
    `call_valid_access_num` AS total_call_valid_num,
    `add_wechat_num` AS due_today_add_wechat_num,
    `not_add_wechat_call_leads_num` AS not_add_wechat_call_num,
    `not_add_wechat_call_leads_num` AS not_add_wechat_total_call_num,
    `playback_leads_num` AS playback_action_num,
    `playback_call_leads_num` AS playback_action_call_num,
    `whole_call_out_leads_num` AS whole_call_num,
    `whole_call_access_leads_num` AS whole_call_access_num,
    `whole_call_valid_access_leads_num` AS whole_call_valid_num,
    `whole_call_duration`,
    `whole_lpc_call_num`,
    `whole_lpc_call_valid_num`,
    `all_leads_num`,
    `access_num_5min` AS lpc_phone_across_gt_5min_num,
    `access_num_8min` AS lpc_phone_across_gt_8min_num,
    `access_leads_num_5min` AS lpc_phone_across_gt_5min_lead_num,
    `access_leads_num_8min` AS lpc_phone_across_gt_8min_lead_num
FROM
    zyb_zbk_lpc_dws_dws_zbk_sop_outbound_today_agg_lpc
WHERE
    hg_binlog_event_type IN (5, 7);