CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_exam_ca_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    `person_uid` BIGINT,
    `course_id` BIGINT,
    `first_channel_id` BIGINT,
    `preview_leads_num` BIGINT,
    `before_class_modi_finish_leads_num` BIGINT,
    `before_class_modi_finish_leads_num_detail` VARCHAR,
    `whole_modi_test_leads_num_detail` VARCHAR
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_exam_ca_agg_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE zyb_zbk_lpc_ads_adl_lpc_course_data (
    lpc_uid BIGINT,
    course_id BIGINT,
    first_channel_id BIGINT,
    preview_num BIGINT,
    placement_test_before_course_submit_num BIGINT,
    all_subject_placement_test_before_course_submit_num VARCHAR,
    whole_all_subject_placement_test_before_course_submit_num VARCHAR,
    zbk_update_time BIGINT
) WITH (
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'connector' = 'hologres',
    'mutatetype' = 'insertOrUpdate',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads.adl_lpc_course_data',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'ignoreDelete' = 'false',
    'reserveUpdateBefore' = 'true'
);
INSERT INTO
    zyb_zbk_lpc_ads_adl_lpc_course_data
SELECT
    person_uid AS lpc_uid,
    course_id,
    first_channel_id,
    preview_leads_num AS preview_num,
    before_class_modi_finish_leads_num AS placement_test_before_course_submit_num,
    before_class_modi_finish_leads_num_detail AS all_subject_placement_test_before_course_submit_num,
    whole_modi_test_leads_num_detail AS whole_all_subject_placement_test_before_course_submit_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_exam_ca_agg_lpc
WHERE hg_binlog_event_type IN (5, 7);