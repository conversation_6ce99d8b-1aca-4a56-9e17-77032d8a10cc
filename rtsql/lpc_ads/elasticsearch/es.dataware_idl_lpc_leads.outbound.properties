CREATE TABLE zyb_zbk_lpc_dws_dws_zyb_service_outbound (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `call_out_num` BIGINT,
    `call_duration` BIGINT,
    `call_access_num` BIGINT,
    `call_valid_access_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zyb_service_outbound',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE zyb_zbk_lpc_ads_idl_lpc_leads (
    `leads_id` BIGINT,
    `call_out_num` BIGINT,
    `call_duration` BIGINT,
    `call_access_num` BIGINT,
    `call_valid_access_num` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (leads_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_leads_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    zyb_zbk_lpc_ads_idl_lpc_leads
SELECT
    `leads_id`,
    `call_out_num`,
    `call_duration`,
    `call_access_num`,
    `call_valid_access_num`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    zyb_zbk_lpc_dws_dws_zyb_service_outbound
WHERE
    `hg_binlog_event_type` IN(5, 7);