CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_lesson_student_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `leader_course_id` BIGINT,
    `course_id` BIGINT,
    `course_name` <PERSON><PERSON><PERSON><PERSON>,
    `lesson_name` <PERSON><PERSON><PERSON><PERSON>,
    `lesson_start_time` BIGINT,
    `lesson_stop_time` BIGINT,
    `new_course_type` BIGINT,
    `grade_period_id` BIGINT,
    `grade_id` BIGINT,
    `subject_id` BIGINT,
    `learn_year` BIGINT,
    `learn_season` BIGINT,
    `lesson_type` BIGINT,
    `lesson_play_type` BIGINT,
    `main_lesson_sort` BIGINT,
    `leads_id` BIGINT,
    `leads_status` BIGINT,
    `leads_stage` BIGINT,
    `is_real_alloc` BIGINT,
    `sale_mode` BIGINT,
    `first_channel_id` BIGINT,
    `course_price_tag` BIGINT,
    `is_goal` BIGINT,
    `business_uid` BIGINT,
    `person_uid` BIGIN<PERSON>,
    `from_person_uid` BIGINT,
    `leads_deploy_flag` BIGINT,
    `wx_map_id` BIGINT,
    `course_source` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_lesson_student_sub_course_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_lesson_student_v1 (
    lesson_id BIGINT,
    student_uid BIGINT,
    course_flag BIGINT,
    course_id BIGINT,
    course_name VARCHAR,
    course_price_tag BIGINT,
    data_status BIGINT,
    first_channel_id BIGINT,
    is_goal BIGINT,
    is_real_alloc BIGINT,
    leader_course_id BIGINT,
    leads_deploy_flag BIGINT,
    leads_deploy_lpc_uid BIGINT,
    leads_id BIGINT,
    status BIGINT,
    learn_season BIGINT,
    lesson_name VARCHAR,
    lesson_start_time BIGINT,
    lesson_stop_time BIGINT,
    lesson_type BIGINT,
    lpc_uid BIGINT,
    lpc_wx_id BIGINT,
    main_department BIGINT,
    main_grade BIGINT,
    main_lesson_index BIGINT,
    main_subject BIGINT,
    new_course_type BIGINT,
    play_type BIGINT,
    sale_mode BIGINT,
    stage BIGINT,
    `year` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (lesson_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_lesson_student_v3',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_lesson_student_v1
SELECT
    lesson_id,
    student_uid,
    CAST(
        CASE
            WHEN course_source = 1 THEN 2
            WHEN course_source = 2 THEN 3
        END AS BIGINT
    ) AS course_flag,
    course_id,
    course_name,
    course_price_tag,
    CAST(1 AS BIGINT) AS data_status,
    first_channel_id,
    is_goal,
    is_real_alloc,
    leader_course_id,
    leads_deploy_flag,
    from_person_uid AS leads_deploy_lpc_uid,
    leads_id,
    leads_status AS status,
    learn_season,
    lesson_name,
    lesson_start_time,
    lesson_stop_time,
    lesson_type,
    person_uid AS lpc_uid,
    wx_map_id AS lpc_wx_id,
    grade_period_id AS main_department,
    grade_id AS main_grade,
    main_lesson_sort AS main_lesson_index,
    subject_id AS main_subject,
    new_course_type,
    lesson_play_type AS play_type,
    sale_mode,
    leads_stage AS stage,
    learn_year AS `year`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_lesson_student_sub_course_lpc
WHERE
    hg_binlog_event_type IN (5, 7);