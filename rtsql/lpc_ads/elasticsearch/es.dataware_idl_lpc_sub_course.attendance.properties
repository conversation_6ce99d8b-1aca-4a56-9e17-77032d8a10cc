CREATE FUNCTION STR_TO_ARRAY as 'com.streaming.flink.udf.StringToArray' LANGUAGE JAVA;
CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_cu_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `course_id` BIGINT,
    `leader_course_id` BIGINT,
    `attend_5minute_lesson_num` BIGINT,
    `attend_finish_lesson_num` BIGINT,
    `attend_5minute_lesson_id_detail` VARCHAR,
    `attend_finish_lesson_id_detail` VARCHAR,
    `total_playback_5minute_lesson_num` BIGINT,
    `total_unlock_playback_5minute_lesson_num` BIGINT,
    `total_unlock_playback_30minute_lesson_num` BIGINT,
    `total_playback_5minute_lesson_id_detail` VARCHAR,
    `no_attend_5minute_total_playback_5minute_lesson_num` BIGINT,
    `no_attend_5minute_total_playback_5minute_lesson_id_detail` VARCHAR,
    `is_attend_one_third` BIGINT,
    `is_attend_two_third` BIGINT,
    `attend_one_third_lesson_num` BIGINT,
    `attend_two_third_lesson_num` BIGINT,
    `attend_one_third_lesson_id_detail` VARCHAR,
    `attend_two_third_lesson_id_detail` VARCHAR,
    `all_lesson_attend_duration_sum` BIGINT,
    `all_lesson_duration_sum` BIGINT,
    `all_lesson_playback_duration_sum` BIGINT,
    `is_attend_5minute` BIGINT,
    `is_attend_finish` BIGINT,
    `is_playback_5minute` BIGINT,
    `is_playback_finish` BIGINT,
    `cu_is_all_attend` BIGINT,
    `cu_is_all_finish` BIGINT,
    `cu_is_all_playback` BIGINT,
    `cu_is_all_playback_finish` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_cu_sub_course_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_sub_course_v1 (
    leads_id BIGINT,
    course_id BIGINT,
    leader_course_id BIGINT,
    playback_num BIGINT,
    is_attend BIGINT,
    is_finish BIGINT,
    attend_num BIGINT,
    finish_num BIGINT,
    is_playback BIGINT,
    is_finish_playback BIGINT,
    is_one_third_attend BIGINT,
    is_two_third_attend BIGINT,
    attend_one_third_num BIGINT,
    attend_two_third_num BIGINT,
    attend_lessonid_array ARRAY<VARCHAR>,
    finish_lessonid_array ARRAY<VARCHAR>,
    lessonsum_playback_time BIGINT,
    playback_lessonid_array ARRAY<VARCHAR>,
    lessonsum_attend_duration BIGINT,
    unlock_playback_attend_num BIGINT,
    unlock_playback_finish_num BIGINT,
    is_lessonsum_attend_five_min BIGINT,
    no_attend_playback_lesson_num BIGINT,
    is_lessonsum_attend_three_four BIGINT,
    is_lessonsum_playback_five_min BIGINT,
    attend_one_third_lessonid_array ARRAY<VARCHAR>,
    attend_two_third_lessonid_array ARRAY<VARCHAR>,
    no_attend_playback_lessonid_array ARRAY<VARCHAR>,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id, course_id, leader_course_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_sub_course_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_sub_course_v1
SELECT
    leads_id,
    course_id,
    leader_course_id,
    total_playback_5minute_lesson_num AS playback_num,
    cu_is_all_attend AS is_attend,
    cu_is_all_finish AS is_finish,
    attend_5minute_lesson_num AS attend_num,
    attend_finish_lesson_num AS finish_num,
    cu_is_all_playback AS is_playback,
    cu_is_all_playback_finish AS is_finish_playback,
    is_attend_one_third AS is_one_third_attend,
    is_attend_two_third AS is_two_third_attend,
    attend_one_third_lesson_num AS attend_one_third_num,
    attend_two_third_lesson_num AS attend_two_third_num,
    STR_TO_ARRAY(attend_5minute_lesson_id_detail) AS attend_lessonid_array,
    STR_TO_ARRAY(attend_finish_lesson_id_detail) AS finish_lessonid_array,
    all_lesson_playback_duration_sum AS lessonsum_playback_time,
    STR_TO_ARRAY(total_playback_5minute_lesson_id_detail) AS playback_lessonid_array,
    all_lesson_attend_duration_sum AS lessonsum_attend_duration,
    total_unlock_playback_5minute_lesson_num AS unlock_playback_attend_num,
    total_unlock_playback_30minute_lesson_num AS unlock_playback_finish_num,
    is_attend_5minute AS is_lessonsum_attend_five_min,
    no_attend_5minute_total_playback_5minute_lesson_num AS no_attend_playback_lesson_num,
    is_attend_finish AS is_lessonsum_attend_three_four,
    is_playback_5minute AS is_lessonsum_playback_five_min,
    STR_TO_ARRAY(attend_one_third_lesson_id_detail) AS attend_one_third_lessonid_array,
    STR_TO_ARRAY(attend_two_third_lesson_id_detail) AS attend_two_third_lessonid_array,
    STR_TO_ARRAY(
        no_attend_5minute_total_playback_5minute_lesson_id_detail
    ) AS no_attend_playback_lessonid_array,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_cu_sub_course_lpc
WHERE
    hg_binlog_event_type IN(5, 7);