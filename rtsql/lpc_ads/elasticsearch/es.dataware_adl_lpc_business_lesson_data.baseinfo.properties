CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_la_agg_business_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `business_uid` BIGINT,
    `reg_leads_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_la_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
 
CREATE TABLE dataware_adl_lpc_business_lesson_data_v1 (
    course_id BIGINT,
    lesson_id BIGINT,
    business_uid BIGINT,
    reg_leads_num BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (course_id, lesson_id, business_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_business_lesson_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
 
INSERT INTO
    dataware_adl_lpc_business_lesson_data_v1
SELECT
    course_id,
    lesson_id,
    business_uid,
    reg_leads_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_la_agg_business_lpc
WHERE hg_binlog_event_type IN (5, 7);