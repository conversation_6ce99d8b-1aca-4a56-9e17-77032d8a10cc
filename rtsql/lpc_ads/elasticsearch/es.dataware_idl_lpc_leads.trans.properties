CREATE TABLE zyb_zbk_lpc_ads_zbk_lpc_trans_leads_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `is_trans` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads_base.ads_zbk_lpc_trans_leads_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_leads (
    `leads_id` BIGINT,
    `is_trans` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_leads_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_leads
SELECT
    `leads_id`,
    `is_trans`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_ads_zbk_lpc_trans_leads_lpc
WHERE
    hg_binlog_event_type IN(5, 7);