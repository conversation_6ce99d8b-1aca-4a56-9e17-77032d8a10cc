CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_interact_cu_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `course_id` BIGINT,
    `leader_course_id` BIGINT,
    `student_uid` BIGINT,
    `inclass_video_link_cnt` BIGINT,
    `inclass_net_delay_cnt` BIGINT,
    `inclass_quick_red_packet_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_interact_cu_sub_course_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_sub_course_v1 (
    leads_id BIGINT,
    course_id BIGINT,
    leader_course_id BIGINT,
    student_uid BIGINT,
    alllesson_video_link_num BIGINT,
    alllesson_block_num BIGINT,
    alllesson_redPacket_num BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id, course_id, leader_course_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_sub_course_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_sub_course_v1
SELECT
    leads_id,
    course_id,
    leader_course_id,
    student_uid,
    inclass_video_link_cnt AS alllesson_video_link_num,
    inclass_net_delay_cnt AS alllesson_block_num,
    inclass_quick_red_packet_cnt AS alllesson_redPacket_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_interact_cu_sub_course_lpc
WHERE
    hg_binlog_event_type IN(5, 7);