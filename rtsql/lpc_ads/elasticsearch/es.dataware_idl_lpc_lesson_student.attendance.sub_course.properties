CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_lu_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `ai_last_leave_time` BIGINT,
    `ai_view_duration` BIGINT,
    `is_inclass_teacher_room_attend_5minute` BIGINT,
    `is_inclass_teacher_room_attend_30minute` BIGINT,
    `inclass_teacher_room_attend_duration` BIGINT,
    `is_attend_ai` BIGINT,
    `is_attend_finish_ai` BIGINT,
    `is_ai_over` BIGINT,
    `is_ai_room_attend` BIGINT,
    `is_inclass_teacher_room_attend_finish` BIGINT,
    `is_lbp_attend_5minute` BIGINT,
    `is_lbp_attend_finish` BIGINT,
    `is_participate_class` BIGINT,
    `is_inclass_teacher_room_total_playback_5minute` BIGINT,
    `is_inclass_teacher_room_total_playback_three_four` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_finish` BIGINT,
    `is_inclass_teacher_room_total_playback_30minute` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_5minute` BIGINT,
    `last_playback_time` BIGINT,
    `lbp_attend_duration` BIGINT,
    `lbp_last_playtime` BIGINT,
    `user_view_detail_json` VARCHAR,
    `inclass_teacher_room_total_playback_time` BIGINT,
    `inclass_teacher_room_day7_playback_time` BIGINT,
    `total_ai_view_duration` BIGINT,
    `inclass_teacher_room_unlock_total_playback_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_lu_sub_course_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_lesson_student_v1 (
    lesson_id BIGINT,
    student_uid BIGINT,
    ai_last_leave_time BIGINT,
    ai_view_duration BIGINT,
    attend BIGINT,
    attend_duration BIGINT,
    attend_long BIGINT,
    is_ai_attend BIGINT,
    is_ai_finish BIGINT,
    is_ai_over BIGINT,
    is_ai_room_attend BIGINT,
    is_attend_finish BIGINT,
    is_lbp_attend BIGINT,
    is_lbp_attend_finish BIGINT,
    is_participate_class BIGINT,
    is_playback BIGINT,
    is_playback_Finish BIGINT,
    is_playback_long BIGINT,
    is_unlock_playback_attend BIGINT,
    is_unlock_playback_finish BIGINT,
    last_playback_time BIGINT,
    lbp_attend_duration BIGINT,
    lbp_last_playtime BIGINT,
    lbp_view_detail VARCHAR,
    playback_time BIGINT,
    playback_time_in_7d BIGINT,
    total_ai_view_duration BIGINT,
    unlock_playback_duartion BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (lesson_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_lesson_student_v3',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_lesson_student_v1
SELECT
    lesson_id,
    student_uid,
    ai_last_leave_time,
    ai_view_duration,
    is_inclass_teacher_room_attend_5minute AS attend,
    inclass_teacher_room_attend_duration AS attend_duration,
    is_inclass_teacher_room_attend_30minute AS attend_long,
    is_attend_ai AS is_ai_attend,
    is_attend_finish_ai AS is_ai_finish,
    is_ai_over,
    is_ai_room_attend,
    is_inclass_teacher_room_attend_finish AS is_attend_finish,
    is_lbp_attend_5minute AS is_lbp_attend,
    is_lbp_attend_finish,
    is_participate_class,
    is_inclass_teacher_room_total_playback_5minute AS is_playback,
    is_inclass_teacher_room_total_playback_three_four AS is_playback_Finish,
    is_inclass_teacher_room_total_playback_30minute AS is_playback_long,
    is_inclass_teacher_room_unlock_total_playback_5minute AS is_unlock_playback_attend,
    is_inclass_teacher_room_unlock_total_playback_finish AS is_unlock_playback_finish,
    last_playback_time,
    lbp_attend_duration,
    lbp_last_playtime,
    user_view_detail_json AS lbp_view_detail,
    inclass_teacher_room_total_playback_time AS playback_time,
    inclass_teacher_room_day7_playback_time AS playback_time_in_7d,
    total_ai_view_duration,
    inclass_teacher_room_unlock_total_playback_time AS unlock_playback_duartion,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_lu_sub_course_lpc
WHERE
    hg_binlog_event_type IN (5, 7);