CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_sop_leads_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `intention` BIGINT,
    `system_intention` BIGINT,
    `system_intention_score` BIGINT,
    `wechat_add_status` BIGINT,
    `wechat_add_time` BIGINT,
    `wechat_sub_status` BIGINT,
    `wechat_sub_time` BIGINT,
    `appointment_submit_status` BIGINT,
    `digneeds_submit_status` BIGINT,
    `wechat_add_type` BIGINT,
    `is_call` BIGINT,
    `callout_status` BIGINT,
    `callout_time` BIGINT,
    `is_deleted` BIGINT,
    `fn_submit_status` BIGINT,
    `curr_svc_num` BIGINT,
    `is_have_coupon` BIGINT,
    `cube_tag_id_list` STRING,
    `is_submit_fn_survey` BIGINT,
    `out_cube_questionnaire_answer_detail` STRING,
    `out_cube_tag_detail_v2` STRING
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_sop_leads_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dataware_idl_lpc_leads_v1 (
    leads_id BIGINT,
    is_preview_leads BIGINT,
    is_call BIGINT,
    call_time BIGINT,
    intention BIGINT,
    has_coupon BIGINT,
    call_status BIGINT,
    wechat_add_time BIGINT,
    wechat_sub_time BIGINT,
    system_intention BIGINT,
    wechat_sub_status BIGINT,
    wechat_add_status BIGINT,
    system_intention_score BIGINT,
    digneeds_submit_status BIGINT,
    appointment_submit_status BIGINT,
    current_service_lpc_num BIGINT,
    `cube_tag_id_list` STRING,
    `is_submit_fn_survey` BIGINT,
    `out_cube_questionnaire_answer_detail` STRING,
    `out_cube_tag_detail_v2` STRING,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_leads_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_leads_v1
SELECT
    leads_id,
    fn_submit_status AS is_preview_leads,
    is_call,
    callout_time AS call_time,
    intention,
    is_have_coupon AS has_coupon,
    callout_status AS call_status,
    wechat_add_time,
    wechat_sub_time,
    system_intention,
    wechat_sub_status,
    wechat_add_status,
    system_intention_score,
    digneeds_submit_status,
    appointment_submit_status,
    curr_svc_num AS current_service_lpc_num,
    `cube_tag_id_list`,
    `is_submit_fn_survey`,
    `out_cube_questionnaire_answer_detail`,
    `out_cube_tag_detail_v2`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_sop_leads_lpc
WHERE
    hg_binlog_event_type IN(5, 7);