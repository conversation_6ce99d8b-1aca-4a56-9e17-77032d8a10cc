CREATE FUNCTION STR_TO_ARRAY as 'com.streaming.flink.udf.StringToArray' LANGUAGE JAVA;
CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_exam_leads_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `cu_is_preview` BIGINT,
    `homework_submit_lesson_cnt` BIGINT,
    `beforeclass_modi_test_submit_status` BIGINT,
    `modi_test_submit_detail` VARCHAR,
    `preview_lessonid_array` VARCHAR,
    `preview_finish_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_exam_leads_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_leads_v1 (
    leads_id BIGINT,
    is_preview BIGINT,
    homework_submit_lesson_num BIGINT,
    is_submit_placement_test_before_course BIGINT,
    placement_test_submit_detail VARCHAR,
    preview_lessonid_array ARRAY <VARCHAR>,
    preview_num BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_leads_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_leads_v1
SELECT
    leads_id,
    cu_is_preview AS is_preview,
    homework_submit_lesson_cnt AS homework_submit_lesson_num,
    beforeclass_modi_test_submit_status AS is_submit_placement_test_before_course,
    modi_test_submit_detail AS placement_test_submit_detail,
    STR_TO_ARRAY(preview_lessonid_array) AS preview_lessonid_array,
    preview_finish_cnt AS preview_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_exam_leads_lpc
WHERE
    hg_binlog_event_type IN (5, 7);