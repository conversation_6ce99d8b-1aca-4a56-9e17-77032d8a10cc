CREATE TABLE source_zyb_zbk_lpc_dws_dws_zbk_exam_la_agg_business_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `business_uid` BIGINT,
    `preview_finish_leads_num` BIGINT,
    `homework_submit_leads_num` BIGINT,
    `niudao_finish_leads_num` BIGINT,
    `lesson_finish_exampaper_num` BIGINT,
    `challenge_finish_leads_num` BIGINT,
    `xiuchang_finish_leads_num` BIGINT,
    `exam9_submit_leads_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_exam_la_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dest_zyb_zbk_lpc_dws_dws_zbk_exam_la_agg_business_lpc (
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `business_uid` BIGINT,
    `preview_finish_num` BIGINT,
    `homework_submit_num` BIGINT,
    `niudao_finish_num` BIGINT,
    `lesson_finish_num` BIGINT,
    `challenge_finish_num` BIGINT,
    `xiuchang_finish_num` BIGINT,
    `exam9_submit_num` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id, lesson_id, business_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_business_lesson_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_lpc_dws_dws_zbk_exam_la_agg_business_lpc
SELECT
    `course_id`,
    `lesson_id`,
    `business_uid`,
    `preview_finish_leads_num` AS preview_finish_num,
    `homework_submit_leads_num` AS homework_submit_num,
    `niudao_finish_leads_num` AS niudao_finish_num,
    `lesson_finish_exampaper_num` AS lesson_finish_num,
    `challenge_finish_leads_num` AS challenge_finish_num,
    `xiuchang_finish_leads_num` AS xiuchang_finish_num,
    `exam9_submit_leads_num` AS exam9_submit_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_lpc_dws_dws_zbk_exam_la_agg_business_lpc
WHERE
    hg_binlog_event_type IN (5, 7);