CREATE TABLE source_zyb_zbk_lpc_dws_dws_zbk_sop_la_agg_business_lpc (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `business_uid` BIGINT,
    `bluetooth_before_class_connect_suc_cnt` BIGINT,
    `bluetooth_during_class_connect_suc_cnt` BIGINT,
    `sensor_before_class_check_suc_cnt` BIGINT,
    `sensor_during_class_check_suc_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_sop_la_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dest_zyb_zbk_lpc_dws_dws_zbk_sop_la_agg_business_lpc (
    lesson_id BIGINT,
    business_uid BIGINT,
    bluetooth_before_class_connect_suc_cnt BIGINT,
    bluetooth_during_class_connect_suc_cnt BIGINT,
    sensor_before_class_check_suc_cnt BIGINT,
    sensor_during_class_check_suc_cnt BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (lesson_id, business_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_business_lesson_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_lpc_dws_dws_zbk_sop_la_agg_business_lpc
SELECT
    lesson_id,
    business_uid,
    bluetooth_before_class_connect_suc_cnt,
    bluetooth_during_class_connect_suc_cnt,
    sensor_before_class_check_suc_cnt,
    sensor_during_class_check_suc_cnt,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_lpc_dws_dws_zbk_sop_la_agg_business_lpc
WHERE hg_binlog_event_type IN (5, 7);