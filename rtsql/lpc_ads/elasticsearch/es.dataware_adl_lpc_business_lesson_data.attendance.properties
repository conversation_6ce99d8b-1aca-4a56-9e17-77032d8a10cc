CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_la_agg_business_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `business_uid` BIGINT,
    `content_view_finish_three_four_student_num` BIGINT,
    `content_view_5minute_student_num` BIGINT,
    `content_view_10s_student_num` BIGINT,
    `content_view_finish_85percent_student_num` BIGINT,
    `overall_finish_student_num` BIGINT,
    `attend_leads_num` BIGINT,
    `attend_finish_leads_num` BIGINT,
    `submit_lesson_work_student_num` BIGINT,
    `generate_lesson_report_student_num` BIGINT,
    `all_attend_leads_num` BIGINT,
    `all_attend_finish_leads_num` BIGINT,
    `playback_leads_num` BIGINT,
    `playback_finish_leads_num` BIGINT,
    `ai_attend_leads_num` BIGINT,
    `ai_attend_finish_leads_num` BIGINT,
    `lbp_attend_leads_num` BIGINT,
    `lbp_finish_leads_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_la_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
 
CREATE TABLE dataware_adl_lpc_business_lesson_data_v1 (
    course_id BIGINT,
    lesson_id BIGINT,
    business_uid BIGINT,
    content_view_finish_three_four_student_num BIGINT,
    content_view_5minute_student_num BIGINT,
    content_view_10s_student_num BIGINT,
    content_view_finish_85percent_student_num BIGINT,
    overall_finish_student_num BIGINT,
    attend_leads_num BIGINT,
    attend_finish_leads_num BIGINT,
    submit_lesson_work_student_num BIGINT,
    generate_lesson_report_student_num BIGINT,
    `attend_num` BIGINT,
    `attend_finish_num` BIGINT,
    `all_attend_num` BIGINT,
    `all_attend_finish_num` BIGINT,
    `playback_num` BIGINT,
    `playback_finish_num` BIGINT,
    `ai_attend_num` BIGINT,
    `ai_attend_finish_num` BIGINT,
    `lbp_attend_num` BIGINT,
    `lbp_finish_num` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (course_id, lesson_id, business_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_business_lesson_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
 
INSERT INTO
    dataware_adl_lpc_business_lesson_data_v1
SELECT
    course_id,
    lesson_id,
    business_uid,
    content_view_finish_three_four_student_num,
    content_view_5minute_student_num,
    content_view_10s_student_num,
    content_view_finish_85percent_student_num,
    overall_finish_student_num,
    attend_leads_num,
    attend_finish_leads_num,
    submit_lesson_work_student_num,
    generate_lesson_report_student_num,
    attend_leads_num AS attend_num,
    attend_finish_leads_num AS attend_finish_num,
    all_attend_leads_num AS all_attend_num,
    all_attend_finish_leads_num AS all_attend_finish_num,
    playback_leads_num AS playback_num,
    playback_finish_leads_num AS playback_finish_num,
    ai_attend_leads_num AS ai_attend_num,
    ai_attend_finish_leads_num AS ai_attend_finish_num,
    lbp_attend_leads_num AS lbp_attend_num,
    lbp_finish_leads_num AS lbp_finish_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_la_agg_business_lpc
WHERE hg_binlog_event_type IN (5, 7);