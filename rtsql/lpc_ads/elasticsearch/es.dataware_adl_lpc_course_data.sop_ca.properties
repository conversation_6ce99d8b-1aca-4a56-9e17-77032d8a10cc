CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_sop_ca_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `person_uid` BIGINT,
    `first_channel_id` BIGINT,
    `add_wechat_num` BIGINT,
    `add_manual_wechat_num` BIGINT,
    `all_wechat_add_num` BIGINT,
    `student_digneeds_submit_num` BIGINT,
    `digneeds_submit_num` BIGINT,
    `appointment_submit_num` BIGINT,
    `fn_appointment_submit_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_sop_ca_agg_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dataware_adl_lpc_course_data_v1 (
    `course_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` BIGINT,
    `add_wechat_num` BIGINT,
    `add_manual_wechat_num` BIGINT,
    `all_add_wechat_num` BIGINT,
    `student_digneeds_submit_num` BIGINT,
    `digneeds_submit_num` BIGINT,
    `appointment_submit_num` BIGINT,
    `fn_preview_leads_num` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (course_id, lpc_uid, first_channel_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_course_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dataware_adl_lpc_course_data_v1
SELECT
    course_id,
    person_uid AS lpc_uid,
    first_channel_id,
    add_wechat_num,
    add_manual_wechat_num,
    all_wechat_add_num AS all_add_wechat_num,
    student_digneeds_submit_num,
    digneeds_submit_num,
    appointment_submit_num,
    fn_appointment_submit_num AS fn_preview_leads_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_sop_ca_agg_lpc
WHERE
    hg_binlog_event_type IN(5, 7);