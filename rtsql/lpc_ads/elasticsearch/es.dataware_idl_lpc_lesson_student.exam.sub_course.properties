-- 声明用到的所有表
CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_exam_lu_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `exam1` VARCHAR,
    `exam10` VARCHAR,
    `exam13` VARCHAR,
    `exam5` VARCHAR,
    `exam7` VARCHAR,
    `exam9` VARCHAR
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_exam_lu_sub_course_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

-- row https://nightlies.apache.org/flink/flink-docs-master/docs/dev/table/types/
CREATE TABLE dataware_idl_lpc_lesson_student_v1 (
    lesson_id BIGINT,
    student_uid BIGINT,
    exam1 ROW<participate_num BIGINT, right_num BIGINT, total_num BIGINT>,
    exam10 ROW<is_submit STRING, participate_num BIGINT, right_num BIGINT, submit_time BIGINT>,
    exam13 ROW<finish_time BIGINT, is_submit STRING, participate_num BIGINT, right_num BIGINT, source BIGINT, status BIGINT, submit_time BIGINT, total_num BIGINT>,
    exam5 ROW<is_submit STRING, participate_num BIGINT, right_num BIGINT, submit_time BIGINT, total_num BIGINT>,
    exam7 ROW<correct_level STRING, correct_status BIGINT, correct_time BIGINT, exam_id STRING, is_amend STRING, is_correct STRING, is_have STRING, is_submit STRING, is_view_wrong_expound_video STRING, last_correct_time BIGINT, last_submit_time BIGINT, participate_num STRING, right_num STRING, submit_num STRING, submit_time BIGINT, total_num STRING, wrong_expound_video_view_num STRING, wrong_num STRING>,
    exam9 ROW<answer_score BIGINT, is_submit STRING, participate_num BIGINT, right_num BIGINT, submit_time BIGINT, total_num BIGINT>,
    zbk_update_time BIGINT,
    PRIMARY KEY (lesson_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_lesson_student_v3',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

CREATE TEMPORARY VIEW temp AS
SELECT
    lesson_id,
    student_uid,
    CAST(z_json_extract(exam1, '$.participate_num') AS BIGINT) AS exam1_participate_num,
    CAST(z_json_extract(exam1, '$.right_num') AS BIGINT) AS exam1_right_num,
    CAST(z_json_extract(exam1, '$.total_num') AS BIGINT) AS exam1_total_num,
    CAST(z_json_extract(exam10, '$.is_submit') AS STRING) AS exam10_is_submit,
    CAST(z_json_extract(exam10, '$.participate_num') AS BIGINT) AS exam10_participate_num,
    CAST(z_json_extract(exam10, '$.right_num') AS BIGINT) AS exam10_right_num,
    CAST(z_json_extract(exam10, '$.submit_time') AS BIGINT) AS exam10_submit_time,
    CAST(z_json_extract(exam13, '$.finish_time') AS BIGINT) AS exam13_finish_time,
    CAST(z_json_extract(exam13, '$.is_submit') AS STRING) AS exam13_is_submit,
    CAST(z_json_extract(exam13, '$.participate_num') AS BIGINT) AS exam13_participate_num,
    CAST(z_json_extract(exam13, '$.right_num') AS BIGINT) AS exam13_right_num,
    CAST(z_json_extract(exam13, '$.preview_channel') AS BIGINT) AS exam13_source,
    CAST(z_json_extract(exam13, '$.is_finish') AS BIGINT) AS exam13_status,
    CAST(z_json_extract(exam13, '$.submit_time') AS BIGINT) AS exam13_submit_time,
    CAST(z_json_extract(exam13, '$.total_num') AS BIGINT) AS exam13_total_num,
    CAST(z_json_extract(exam5, '$.is_submit') AS STRING) AS exam5_is_submit,
    CAST(z_json_extract(exam5, '$.participate_num') AS BIGINT) AS exam5_participate_num,
    CAST(z_json_extract(exam5, '$.right_num') AS BIGINT) AS exam5_right_num,
    CAST(z_json_extract(exam5, '$.submit_time') AS BIGINT) AS exam5_submit_time,
    CAST(z_json_extract(exam5, '$.total_num') AS BIGINT) AS exam5_total_num,
    CAST(z_json_extract(exam7, '$.correct_level') AS STRING) AS exam7_correct_level,
    CAST(z_json_extract(exam7, '$.correct_status') AS BIGINT) AS exam7_correct_status,
    CAST(z_json_extract(exam7, '$.correct_time') AS BIGINT) AS exam7_correct_time,
    CAST(z_json_extract(exam7, '$.exam_id') AS STRING) AS exam7_exam_id,
    CAST(z_json_extract(exam7, '$.is_amend') AS STRING) AS exam7_is_amend,
    CAST(z_json_extract(exam7, '$.is_correct') AS STRING) AS exam7_is_correct,
    CAST(z_json_extract(exam7, '$.is_have') AS STRING) AS exam7_is_have,
    CAST(z_json_extract(exam7, '$.is_submit') AS STRING) AS exam7_is_submit,
    CAST(z_json_extract(exam7, '$.is_view_wrong_expound_video') AS STRING) AS exam7_is_view_wrong_expound_video,
    CAST(z_json_extract(exam7, '$.last_correct_time') AS BIGINT) AS exam7_last_correct_time,
    CAST(z_json_extract(exam7, '$.last_submit_time') AS BIGINT) AS exam7_last_submit_time,
    CAST(z_json_extract(exam7, '$.participate_num') AS STRING) AS exam7_participate_num,
    CAST(z_json_extract(exam7, '$.right_num') AS STRING) AS exam7_right_num,
    CAST(z_json_extract(exam7, '$.submit_num') AS STRING) AS exam7_submit_num,
    CAST(z_json_extract(exam7, '$.submit_time') AS BIGINT) AS exam7_submit_time,
    CAST(z_json_extract(exam7, '$.total_num') AS STRING) AS exam7_total_num,
    CAST(z_json_extract(exam7, '$.wrong_expound_video_view_num') AS STRING) AS exam7_wrong_expound_video_view_num,
    CAST(z_json_extract(exam7, '$.wrong_num') AS STRING) AS exam7_wrong_num,
    CAST(z_json_extract(exam9, '$.answer_score') AS BIGINT) AS exam9_answer_score,
    CAST(z_json_extract(exam9, '$.is_submit') AS STRING) AS exam9_is_submit,
    CAST(z_json_extract(exam9, '$.participate_num') AS BIGINT) AS exam9_participate_num,
    CAST(z_json_extract(exam9, '$.right_num') AS BIGINT) AS exam9_right_num,
    CAST(z_json_extract(exam9, '$.submit_time') AS BIGINT) AS exam9_submit_time,
    CAST(z_json_extract(exam9, '$.total_num') AS BIGINT) AS exam9_total_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_exam_lu_sub_course_lpc
WHERE
    hg_binlog_event_type IN (5, 7);

INSERT INTO
    dataware_idl_lpc_lesson_student_v1
SELECT
    lesson_id,
    student_uid,
    ROW(exam1_participate_num, exam1_right_num, exam1_total_num) AS exam1,
    ROW(exam10_is_submit, exam10_participate_num, exam10_right_num, exam10_submit_time) AS exam10,
    ROW(exam13_finish_time, exam13_is_submit, exam13_participate_num, exam13_right_num, exam13_source, exam13_status, exam13_submit_time, exam13_total_num) AS exam13,
    ROW(exam5_is_submit, exam5_participate_num, exam5_right_num, exam5_submit_time, exam5_total_num) AS exam5,
    ROW(exam7_correct_level, exam7_correct_status, exam7_correct_time, exam7_exam_id, exam7_is_amend, exam7_is_correct, exam7_is_have, exam7_is_submit, exam7_is_view_wrong_expound_video, exam7_last_correct_time, exam7_last_submit_time, exam7_participate_num, exam7_right_num, exam7_submit_num, exam7_submit_time, exam7_total_num, exam7_wrong_expound_video_view_num, exam7_wrong_num) AS exam7,
    ROW(exam9_answer_score, exam9_is_submit, exam9_participate_num, exam9_right_num, exam9_submit_time, exam9_total_num) AS exam9,
    zbk_update_time
FROM
    temp;