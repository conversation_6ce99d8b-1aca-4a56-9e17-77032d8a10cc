-- 声明用到的所有表
CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_interact_lu_lpc (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `beforeclass_hands_up_cnt` BIGINT,
    `inclass_hands_up_cnt` BIGINT,
    `afterclass_hands_up_cnt` BIGIN<PERSON>,
    `inclass_assistant_hands_up_cnt` BIGINT,
    `beforeclass_net_delay_cnt` BIGINT,
    `inclass_net_delay_cnt` BIGINT,
    `afterclass_net_delay_cnt` BIGINT,
    `inclass_assistant_net_delay_cnt` BIGINT,
    `beforeclass_red_packet_cnt` BIGINT,
    `inclass_red_packet_cnt` BIGINT,
    `afterclass_red_packet_cnt` BIGINT,
    `inclass_assistant_red_packet_cnt` BIGINT,
    `beforeclass_video_link_cnt` BIGINT,
    `inclass_video_link_cnt` BIGINT,
    `afterclass_video_link_cnt` BIGINT,
    `inclass_assistant_video_link_cnt` BIGINT,
    `beforeclass_quick_red_packet_cnt` BIGINT,
    `inclass_quick_red_packet_cnt` BIGINT,
    `afterclass_quick_red_packet_cnt` BIGINT,
    `inclass_assistant_quick_red_packet_cnt` BIGINT,
    `mix_live_interaction_right_num` BIGINT,
    `mix_live_interaction_submit_num` BIGINT,
    `mix_playback_interaction_right_num` BIGINT,
    `mix_playback_interaction_submit_num` BIGINT,
    `open_mouth_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_interact_lu_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_lesson_student_v1 (
    lesson_id BIGINT,
    student_uid BIGINT,
    after_class_hands_up_num BIGINT,
    after_class_net_delay_num BIGINT,
    after_class_quick_red_packet_num BIGINT,
    after_class_red_packet_num BIGINT,
    after_class_video_link_num BIGINT,
    in_class_assistant_hands_up_num BIGINT,
    in_class_assistant_net_delay_num BIGINT,
    in_class_assistant_quick_red_packet_num BIGINT,
    in_class_assistant_red_packet_num BIGINT,
    in_class_assistant_video_link_num BIGINT,
    in_class_hands_up_num BIGINT,
    in_class_net_delay_num BIGINT,
    in_class_quick_red_packet_num BIGINT,
    in_class_red_packet_num BIGINT,
    in_class_video_link_num BIGINT,
    mix_live_interaction_right_num BIGINT,
    mix_live_interaction_submit_num BIGINT,
    mix_playback_interaction_right_num BIGINT,
    mix_playback_interaction_submit_num BIGINT,
    pre_class_hands_up_num BIGINT,
    pre_class_net_delay_num BIGINT,
    pre_class_quick_red_packet_num BIGINT,
    pre_class_red_packet_num BIGINT,
    pre_class_video_link_num BIGINT,
    open_mouth_cnt BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (lesson_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_lesson_student_v3',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
-- sink
INSERT INTO
    dataware_idl_lpc_lesson_student_v1
SELECT
    lesson_id,
    student_uid,
    afterclass_hands_up_cnt AS after_class_hands_up_num,
    afterclass_net_delay_cnt AS after_class_net_delay_num,
    afterclass_quick_red_packet_cnt AS after_class_quick_red_packet_num,
    afterclass_red_packet_cnt AS after_class_red_packet_num,
    afterclass_video_link_cnt AS after_class_video_link_num,
    inclass_assistant_hands_up_cnt AS in_class_assistant_hands_up_num,
    inclass_assistant_net_delay_cnt AS in_class_assistant_net_delay_num,
    inclass_assistant_quick_red_packet_cnt AS in_class_assistant_quick_red_packet_num,
    inclass_assistant_red_packet_cnt AS in_class_assistant_red_packet_num,
    inclass_assistant_video_link_cnt AS in_class_assistant_video_link_num,
    inclass_hands_up_cnt AS in_class_hands_up_num,
    inclass_net_delay_cnt AS in_class_net_delay_num,
    inclass_quick_red_packet_cnt AS in_class_quick_red_packet_num,
    inclass_red_packet_cnt AS in_class_red_packet_num,
    inclass_video_link_cnt AS in_class_video_link_num,
    mix_live_interaction_right_num,
    mix_live_interaction_submit_num,
    mix_playback_interaction_right_num,
    mix_playback_interaction_submit_num,
    beforeclass_hands_up_cnt AS pre_class_hands_up_num,
    beforeclass_net_delay_cnt AS pre_class_net_delay_num,
    beforeclass_quick_red_packet_cnt AS pre_class_quick_red_packet_num,
    beforeclass_red_packet_cnt AS pre_class_red_packet_num,
    beforeclass_video_link_cnt AS pre_class_video_link_num,
    open_mouth_cnt,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_interact_lu_lpc
WHERE
    hg_binlog_event_type IN (5, 7);