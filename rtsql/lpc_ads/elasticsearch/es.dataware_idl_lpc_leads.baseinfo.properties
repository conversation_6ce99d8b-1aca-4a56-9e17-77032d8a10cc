CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_leads_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `person_uid` BIGINT,
    `class_id` BIGINT,
    `student_type` BIGINT,
    `transfer_type` BIGINT,
    `is_original` BIGINT,
    `leads_status` BIGINT,
    `trade_time` BIGINT,
    `refund_time` BIGINT,
    `student_area` BIGINT,
    `assistant_area` BIGINT,
    `inviter_uid` BIGINT,
    `business_uid` BIGINT,
    `wx_map_id` BIGINT,
    `sell_cannel_id` BIGINT,
    `buy_type` BIGINT,
    `first_channel_id` BIGINT,
    `course_mode` BIGINT,
    `sale_mode` BIGINT,
    `alloc_time` BIGINT,
    `expire_time_start` BIGINT,
    `expire_time_stop` BIGINT,
    `is_invalid_leads` BIGINT,
    `is_real_alloc` BIGINT,
    `is_deleted` BIGINT,
    `student_phone` BIGINT,
    `student_last_phone_no` BIGINT,
    `activity_id` BIGINT,
    `source` BIGINT,
    `leads_origin` BIGINT,
    `leads_level` BIGINT,
    `leads_score` BIGINT,
    `leads_deploy_flag` BIGINT,
    `is_goal` BIGINT,
    `leads_stage` BIGINT,
    `course_price_tag` BIGINT,
    `trans_time_start` BIGINT,
    `sub_trade_id` BIGINT,
    `transfer_time` BIGINT,
    `from_person_uid` BIGINT,
    `from_business_uid` BIGINT,
    `from_wx_map_id` BIGINT,
    `active_status` BIGINT,
    `active_time` BIGINT,
    `create_time` BIGINT,
    `update_time` BIGINT,
    `last_from` VARCHAR,
    `lpc_user_type` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_leads_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_leads_v1 (
    leads_id BIGINT,
    course_id BIGINT,
    level BIGINT,
    phone BIGINT,
    stage BIGINT,
    is_goal BIGINT,
    lpc_wx_id BIGINT,
    is_deleted BIGINT,
    lpc_uid BIGINT,
    active_time BIGINT,
    activity_id BIGINT,
    inviter_uid BIGINT,
    leads_origin BIGINT,
    leads_source BIGINT,
    sub_trade_id BIGINT,
    active_status BIGINT,
    is_real_alloc BIGINT,
    last_phone_no BIGINT,
    has_course BIGINT,
    lpc_alloc_time BIGINT,
    leads_last_from VARCHAR,
    leads_sale_mode BIGINT,
    leads_status BIGINT,
    is_invalid_leads BIGINT,
    trans_time_start BIGINT,
    leads_create_time BIGINT,
    leads_deploy_wx_id BIGINT,
    course_price_tag BIGINT,
    first_channel_id BIGINT,
    leads_deploy_flag BIGINT,
    leads_expire_time BIGINT,
    leads_start_expire_time BIGINT,
    leads_deploy_lpc_uid BIGINT,
    lpc_user_type BIGINT,
    student_uid BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_leads_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_leads_v1
SELECT
    leads_id,
    course_id,
    leads_level AS level,
    student_phone AS phone,
    leads_stage AS stage,
    is_goal,
    wx_map_id AS lpc_wx_id,
    is_deleted,
    person_uid AS lpc_uid,
    active_time,
    activity_id,
    inviter_uid,
    leads_origin,
    source AS leads_source,
    sub_trade_id,
    active_status,
    is_real_alloc,
    student_last_phone_no AS last_phone_no,
    course_mode AS has_course,
    alloc_time AS lpc_alloc_time,
    last_from AS leads_last_from,
    sale_mode AS leads_sale_mode,
    leads_status,
    is_invalid_leads,
    trans_time_start,
    create_time AS leads_create_time,
    from_wx_map_id AS leads_deploy_wx_id,
    course_price_tag,
    first_channel_id,
    leads_deploy_flag,
    expire_time_stop AS leads_expire_time,
    expire_time_start AS leads_start_expire_time,
    from_person_uid AS leads_deploy_lpc_uid,
    lpc_user_type,
    student_uid,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_leads_lpc
WHERE
    hg_binlog_event_type IN(5, 7);