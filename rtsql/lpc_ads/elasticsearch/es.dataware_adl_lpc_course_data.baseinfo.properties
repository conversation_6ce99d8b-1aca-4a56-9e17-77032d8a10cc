CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_ca_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `person_uid` BIGINT,
    `first_channel_id` BIGINT,
    `first_lesson_teacher` BIGINT,
    all_leads_cnt BIGINT,
    invalid_leads_cnt BIGINT,
    leads_cnt BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_ca_agg_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_adl_lpc_course_data_v1 (
    `course_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` BIGINT,
    `first_lesson_teacher` BIGINT,
    all_leads_num BIGINT,
    invalid_leads_num BIGINT,
    leads_num BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (course_id, lpc_uid, first_channel_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_course_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dataware_adl_lpc_course_data_v1
SELECT
    course_id,
    person_uid AS lpc_uid,
    first_channel_id,
    first_lesson_teacher,
    all_leads_cnt AS all_leads_num,
    invalid_leads_cnt AS invalid_leads_num,
    leads_cnt AS leads_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_ca_agg_lpc
WHERE
    hg_binlog_event_type IN(5, 7);