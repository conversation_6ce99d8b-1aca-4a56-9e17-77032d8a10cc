CREATE FUNCTION STR_TO_ARRAY as 'com.streaming.flink.udf.StringToArray' LANGUAGE JAVA;
CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_leads_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `attend_5minute_lesson_id_detail` VARCHA<PERSON>,
    `attend_5minute_lesson_num` BIGINT,
    `attend_finish_lesson_id_detail` VARCHA<PERSON>,
    `attend_finish_lesson_num` BIGINT,
    `cu_is_ai_attend` BIGINT,
    `cu_is_ai_finish` BIGINT,
    `cu_is_all_attend` BIGINT,
    `cu_is_all_finish` BIGINT,
    `cu_is_attend` BIGINT,
    `cu_is_chujing_attend` BIGINT,
    `cu_is_finish` BIGINT,
    `cu_is_finish_playback` BIGINT,
    `cu_is_lbp_attend` BIGINT,
    `cu_is_lbp_attend_finish` BIGINT,
    `cu_is_playback` BIGINT,
    `cu_is_unlock_playback_attend` BIGINT,
    `cu_is_unlock_playback_finish` BIGINT,
    `last_playback_time` B<PERSON>IN<PERSON>,
    `lbp_attend_lessonid_array` <PERSON>RC<PERSON>R,
    `lbp_attend_count` BIGINT,
    `lbp_finish_lessonid_array` VARCHAR,
    `lbp_finish_count` BIGINT,
    `no_attend_5minute_total_playback_5minute_lesson_num` BIGINT,
    `no_attend_5minute_total_playback_5minute_lesson_id_detail` VARCHAR,
    `total_playback_5minute_lesson_id_detail` VARCHAR,
    `total_playback_5minute_lesson_num` BIGINT,
    `total_unlock_playback_5minute_lesson_id_detail` VARCHAR,
    `total_unlock_playback_5minute_lesson_num` BIGINT,
    `all_lesson_total_unlock_playback_time_sum` BIGINT,
    `total_unlock_playback_three_four_lesson_id_detail` VARCHAR,
    `total_unlock_playback_three_four_lesson_num` bigint
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_leads_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_leads_v1 (
    leads_id BIGINT,
    attend_lessonid_array ARRAY<VARCHAR>,
    attend_num BIGINT,
    finish_lessonid_array ARRAY<VARCHAR>,
    finish_num BIGINT,
    is_ai_attend BIGINT,
    is_ai_finish BIGINT,
    is_all_attend BIGINT,
    is_all_finish BIGINT,
    is_attend BIGINT,
    is_chujing_attend BIGINT,
    is_finish BIGINT,
    is_finish_playback BIGINT,
    is_lbp_attend BIGINT,
    is_lbp_attend_finish BIGINT,
    is_playback BIGINT,
    is_unlock_playback_attend BIGINT,
    is_unlock_playback_finish BIGINT,
    last_playback_time BIGINT,
    lbp_attend_lessonid_array ARRAY<VARCHAR>,
    lbp_attend_num BIGINT,
    lbp_finish_lessonid_array ARRAY<VARCHAR>,
    lbp_finish_num BIGINT,
    no_attend_playback_lesson_num BIGINT,
    no_attend_playback_lessonid_array ARRAY<VARCHAR>,
    playback_lessonid_array ARRAY<VARCHAR>,
    playback_num BIGINT,
    unlock_playback_attend_detail ARRAY<VARCHAR>,
    unlock_playback_attend_num BIGINT,
    unlock_playback_duartion BIGINT,
    unlock_playback_finish_detail ARRAY<VARCHAR>,
    unlock_playback_finish_num BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_leads_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_leads_v1
SELECT
    leads_id,
    STR_TO_ARRAY(attend_5minute_lesson_id_detail) AS attend_lessonid_array,
    attend_5minute_lesson_num as attend_num,
    STR_TO_ARRAY(attend_finish_lesson_id_detail) AS finish_lessonid_array,
    attend_finish_lesson_num AS finish_num,
    cu_is_ai_attend AS is_ai_attend,
    cu_is_ai_finish AS is_ai_finish,
    cu_is_all_attend AS is_all_attend,
    cu_is_all_finish AS is_all_finish,
    cu_is_attend AS is_attend,
    cu_is_chujing_attend AS is_chujing_attend,
    cu_is_finish AS is_finish,
    cu_is_finish_playback AS is_finish_playback,
    cu_is_lbp_attend AS is_lbp_attend,
    cu_is_lbp_attend_finish AS is_lbp_attend_finish,
    cu_is_playback AS is_playback,
    cu_is_unlock_playback_attend AS is_unlock_playback_attend,
    cu_is_unlock_playback_finish AS is_unlock_playback_finish,
    last_playback_time,
    STR_TO_ARRAY(lbp_attend_lessonid_array) AS lbp_attend_lessonid_array,
    lbp_attend_count AS lbp_attend_num,
    STR_TO_ARRAY(lbp_finish_lessonid_array) AS lbp_finish_lessonid_array,
    lbp_finish_count AS lbp_finish_num,
    no_attend_5minute_total_playback_5minute_lesson_num AS no_attend_playback_lesson_num,
    STR_TO_ARRAY(
        no_attend_5minute_total_playback_5minute_lesson_id_detail
    ) AS no_attend_playback_lessonid_array,
    STR_TO_ARRAY(total_playback_5minute_lesson_id_detail) AS playback_lessonid_array,
    total_playback_5minute_lesson_num AS playback_num,
    STR_TO_ARRAY(total_unlock_playback_5minute_lesson_id_detail) AS unlock_playback_attend_detail,
    total_unlock_playback_5minute_lesson_num AS unlock_playback_attend_num,
    all_lesson_total_unlock_playback_time_sum AS unlock_playback_duartion,
    STR_TO_ARRAY(
        total_unlock_playback_three_four_lesson_id_detail
    ) AS unlock_playback_finish_detail,
    total_unlock_playback_three_four_lesson_num AS unlock_playback_finish_num,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_leads_lpc
WHERE
    hg_binlog_event_type IN (5, 7);