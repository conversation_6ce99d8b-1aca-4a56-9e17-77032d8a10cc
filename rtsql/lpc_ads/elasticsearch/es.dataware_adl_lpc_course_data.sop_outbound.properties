CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_sop_outbound_agg_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `person_uid` BIGINT,
    `first_channel_id` BIGINT,
    `call_access_num` BIGINT,
    `call_duration` BIGINT,
    `call_num` BIGINT,
    `call_valid_num` BIGINT,
    `lpc_call_num` BIGINT,
    `lpc_call_valid_num` BIGINT,
    `lpc_phone_across_gt_5min_lead_num` BIGINT,
    `lpc_phone_across_gt_5min_num` BIGINT,
    `lpc_phone_across_gt_8min_lead_num` BIGINT,
    `lpc_phone_across_gt_8min_num` BIGINT,
    `not_add_wechat_call_num` BIGINT,
    `whole_call_access_num` BIGINT,
    `whole_call_duration` BIGINT,
    `whole_call_num` BIGINT,
    `whole_call_valid_num` BIGINT,
    `whole_lpc_call_num` BIGINT,
    `whole_lpc_valid_call_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_sop_outbound_agg_lpc',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dataware_adl_lpc_course_data_v1 (
    `course_id` BIGINT,
    `lpc_uid` BIGINT,
    `first_channel_id` BIGINT,
    `call_access_num` BIGINT,
    `call_duration` BIGINT,
    `call_num` BIGINT,
    `call_valid_num` BIGINT,
    `lpc_call_num` BIGINT,
    `lpc_call_valid_num` BIGINT,
    `lpc_phone_across_gt_5min_lead_num` BIGINT,
    `lpc_phone_across_gt_5min_num` BIGINT,
    `lpc_phone_across_gt_8min_lead_num` BIGINT,
    `lpc_phone_across_gt_8min_num` BIGINT,
    `not_add_wechat_call_num` BIGINT,
    `lpc_whole_all_across_num` BIGINT,
    `whole_call_duration` BIGINT,
    `whole_call_num` BIGINT,
    `whole_call_valid_num` BIGINT,
    `whole_lpc_call_num` BIGINT,
    `whole_lpc_call_valid_num` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (course_id, lpc_uid, first_channel_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_course_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dataware_adl_lpc_course_data_v1
SELECT
    `course_id`,
    `person_uid` AS `lpc_uid`,
    `first_channel_id`,
    `call_access_num`,
    `call_duration`,
    `call_num`,
    `call_valid_num`,
    `lpc_call_num`,
    `lpc_call_valid_num`,
    `lpc_phone_across_gt_5min_lead_num`,
    `lpc_phone_across_gt_5min_num`,
    `lpc_phone_across_gt_8min_lead_num`,
    `lpc_phone_across_gt_8min_num`,
    `not_add_wechat_call_num`,
    `whole_call_access_num` AS `lpc_whole_all_across_num`,
    `whole_call_duration`,
    `whole_call_num`,
    `whole_call_valid_num`,
    `whole_lpc_call_num`,
    `whole_lpc_valid_call_num` AS `whole_lpc_call_valid_num`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    zyb_zbk_lpc_dws_dws_zbk_sop_outbound_agg_lpc
WHERE
    `hg_binlog_event_type` IN (5, 7);