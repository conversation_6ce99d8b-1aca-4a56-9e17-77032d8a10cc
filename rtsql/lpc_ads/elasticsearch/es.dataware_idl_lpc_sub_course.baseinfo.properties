CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_leads_sub_course_lpc (
    `hg_binlog_event_type` BIGINT,
    `leads_id` BIGINT,
    `course_id` BIGINT,
    `leader_course_id` BIGINT,
    `student_uid` BIGINT,
    `is_deleted` BIGINT,
    `course_source` BIGINT,
    `is_need_attend` BIGINT,
    `activity_id` BIGINT,
    `active_status` BIGINT,
    `course_price_tag` BIGINT,
    `first_channel_id` BIGINT,
    `course_mode` BIGINT,
    `is_goal` BIGINT,
    `is_invalid_leads` BIGINT,
    `is_real_alloc` BIGINT,
    `leads_deploy_flag` BIGINT,
    `transfer_time` BIGINT,
    `from_person_uid` BIGINT,
    `from_business_uid` BIGINT,
    `expire_time_start` BIGINT,
    `expire_time_stop` BIGINT,
    `source` BIGINT,
    `leads_status` BIGINT,
    `leads_level` BIGINT,
    `leads_stage` BIGINT,
    `person_uid` BIGINT,
    `business_uid` BIGINT,
    `wx_map_id` BIGINT,
    `leads_create_time` BIGINT,
    `sale_mode` BIGINT,
    `alloc_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_leads_sub_course_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_idl_lpc_sub_course_v1 (
    leads_id BIGINT,
    course_id BIGINT,
    leader_course_id BIGINT,
    student_uid BIGINT,
    level BIGINT,
    is_goal BIGINT,
    lpc_uid BIGINT,
    lpc_wx_id BIGINT,
    has_course BIGINT,
    is_deleted BIGINT,
    sub_status BIGINT,
    activity_id BIGINT,
    leads_source BIGINT,
    leads_status BIGINT,
    active_status BIGINT,
    is_real_alloc BIGINT,
    course_price_tag BIGINT,
    first_channel_id BIGINT,
    is_invalid_leads BIGINT,
    leads_create_time BIGINT,
    leads_deploy_flag BIGINT,
    leads_expire_time BIGINT,
    leads_deploy_lpc_uid BIGINT,
    leads_start_expire_time BIGINT,
    is_should_attend BIGINT,
    has_buy_sub_course BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (leads_id, course_id, leader_course_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_sub_course_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_lpc_sub_course_v1
SELECT
    leads_id,
    course_id,
    leader_course_id,
    student_uid,
    leads_level AS level,
    is_goal,
    person_uid AS lpc_uid,
    wx_map_id AS lpc_wx_id,
    course_mode AS has_course,
    is_deleted,
    is_deleted AS sub_status,
    activity_id,
    source AS leads_source,
    leads_status,
    active_status,
    is_real_alloc,
    course_price_tag,
    first_channel_id,
    is_invalid_leads,
    leads_create_time,
    leads_deploy_flag,
    expire_time_stop AS leads_expire_time,
    from_person_uid AS leads_deploy_lpc_uid,
    expire_time_start AS leads_start_expire_time,
    is_need_attend AS is_should_attend,
    CAST(
        (
            CASE
                WHEN course_source = 2 THEN 1
                ELSE 0
            END
        ) AS BIGINT
    ) AS has_buy_sub_course,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_leads_sub_course_lpc
WHERE
    hg_binlog_event_type IN(5, 7);