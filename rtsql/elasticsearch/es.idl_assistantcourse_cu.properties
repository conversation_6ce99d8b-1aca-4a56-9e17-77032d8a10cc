-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_cu (
    `asscourse_attend_count` BIGINT,
    `asscourse_finish_count` BIGINT,
    `course_id` BIGINT,
    `is_refund` BIGINT,
    `student_uid` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_assistantcourse_cu',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_cu (
    `asscourse_attend_count` BIGINT,
    `asscourse_finish_count` BIGINT,
    `course_id` BIGINT,
    `is_refund` BIGINT,
    `student_uid` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_assistantcourse_cu_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistantcourse_cu
SELECT
    `asscourse_attend_count`,
    `asscourse_finish_count`,
    `course_id`,
    `is_refund`,
    `student_uid`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistantcourse_cu;