CREATE TABLE zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_student_detail (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `assistant_uid` BIGINT,
    `total_question_cnt` BIGINT,
    `total_correct_cnt` BIGINT,
    `question_status` BIGINT,
    `unlock_lesson_id` BIGINT,
    `answer_times` BIGINT,
    `exam_id` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_question_course_task_student_detail',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_adl_exercise_note_l1_course_task_student_detail (
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `assistant_uid` BIGINT,
    `total_question_cnt` BIGINT,
    `total_correct_cnt` BIGINT,
    `question_status` BIGINT,
    `unlock_lesson_id` BIGINT,
    `answer_times` BIGINT,
    `exam_id` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (assistant_uid, course_id, task_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_exercise_note_l1_course_task_student_detail_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_adl_exercise_note_l1_course_task_student_detail
SELECT
    `student_uid`,
    `course_id`,
    `task_id`,
    `assistant_uid`,
    `total_question_cnt`,
    `total_correct_cnt`,
    `question_status`,
    `unlock_lesson_id`,
    `answer_times`,
    `exam_id`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_student_detail
WHERE
    hg_binlog_event_type IN(5, 7);
