-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_assistant_v1 (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `assistant_uid` BIGINT,
    `is_l1r` BIGINT,
    `is_l2_once` BIGINT,
    `is_l2r` BIGINT,
    `new_user_type` STRING,
    `status` BIGINT,
    `trade_id` BIGINT,
    `year` BIGINT,
    `is_l2r_space_season` BIGINT,
    `leap_user_type` STRING
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_assistant_v1',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_assistant_v1 (
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `assistant_uid` BIGINT,
    `is_l1r` BIGINT,
    `is_l2_once` BIGINT,
    `is_l2r` BIGINT,
    `new_user_type` STRING,
    `status` BIGINT,
    `trade_id` BIGINT,
    `year` BIGINT,
    `is_l2r_space_season` BIGINT,
    `leap_user_type` STRING,
    `update_time` BIGINT,
    PRIMARY KEY (course_id, student_uid, sub_trade_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_trade_order_assistant_genke_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_assistant_v1
SELECT
    `course_id`,
    `student_uid`,
    `sub_trade_id`,
    `assistant_uid`,
    `is_l1r`,
    `is_l2_once`,
    `is_l2r`,
    `new_user_type`,
    `status`,
    `trade_id`,
    `year`,
    `is_l2r_space_season`,
    `leap_user_type`,
    UNIX_TIMESTAMP() AS `update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_assistant_v1
WHERE
    `hg_binlog_event_type` <> 3
    -- 灌班之后才跟课
    AND `assistant_uid` IS NOT NULL;