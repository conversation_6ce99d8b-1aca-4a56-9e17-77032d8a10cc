-- TP数据引擎 A/B课关系表
CREATE TABLE binlog_homework_zhibo_exam_tbl_course_buy_gift_relation (
    BINLOG_NAME VARCHAR,
    BINLOG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `GLOBAL_ID` VARCHAR,
    `NEW_VALUES` ROW<`id` STRING, `source_course_id` STRING, `target_course_id` STRING, `deleted` STRING>,
    `OLD_VALUES` ROW<`id` STRING, `source_course_id` STRING, `target_course_id` STRING, `deleted` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = '505_homework_zhibo_miscourse_tblCourseBuyGiftRelation',
    'properties.bootstrap.servers' = '${kafka.bootstrap-servers.binlog.ali}',
    'properties.group.id' = '${kafka.consumer.groupid}',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_course_buy_gift_relation (
    `id` BIGINT, -- 自增主键ID
    `source_course_id` BIGINT, -- 源课程id-买A
    `target_course_id` BIGINT, -- 目标课程id-赠B
    `deleted` BIGINT, -- 是否有效 0有效 1已删除
    `zbk_delete_flag` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.dataengine.hosts}',
    'index' = 'dataengine_dim_miscourse_gift_course_relation_v1',
    'username' = '${es.dataengine.username}',
    'password' = '${es.dataengine.password}'
);

CREATE TEMPORARY VIEW view_binlog_homework_zhibo_exam_tbl_course_buy_gift_relation AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_homework_zhibo_exam_tbl_course_buy_gift_relation;

CREATE TEMPORARY VIEW view_source_homework_zhibo_exam_tbl_course_buy_gift_relation AS
SELECT
    CAST(binlog_data.`id` AS BIGINT) AS `id`,
    CAST(binlog_data.`source_course_id` AS BIGINT) AS `source_course_id`,
    CAST(binlog_data.`target_course_id` AS BIGINT) AS `target_course_id`,
    CAST(binlog_data.`deleted` AS BIGINT) AS `deleted`,
    zbk_delete_flag
FROM
    view_binlog_homework_zhibo_exam_tbl_course_buy_gift_relation;

INSERT INTO
    es_tbl_course_buy_gift_relation
SELECT
    `id`,
    `source_course_id`,
    `target_course_id`,
    `deleted`,
    `zbk_delete_flag`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    view_source_homework_zhibo_exam_tbl_course_buy_gift_relation;