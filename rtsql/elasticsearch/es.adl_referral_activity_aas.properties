-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_aas (
    `assistant_uid` BIGINT,
    `bind_status` BIGINT,
    `config_id` BIGINT,
    `is_poster` BIGINT,
    `new_user_pv` BIGINT,
    `new_user_uv` BIGINT,
    `student_uid` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_aas',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_aas (
    `assistant_uid` BIGINT,
    `bind_status` BIGINT,
    `config_id` BIGINT,
    `is_poster` BIGINT,
    `new_user_pv` BIGINT,
    `new_user_uv` BIGINT,
    `student_uid` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (assistant_uid, config_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_referral_activity_aas_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_aas
SELECT
    `assistant_uid`,
    `bind_status`,
    `config_id`,
    `is_poster`,
    `new_user_pv`,
    `new_user_uv`,
    `student_uid`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_aas;