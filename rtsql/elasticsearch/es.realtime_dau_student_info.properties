CREATE TABLE binlog_homework_zhibo_dau_student_0 (
    BINLOG_NAME VARCHAR,
    B<PERSON><PERSON>OG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `GL<PERSON>BAL_ID` VARCHAR,
    `NEW_VALUES` ROW<`student_uid` STRING, `student_name` STRING,  `phone_sign` STRING, `guardian` STRING,  `guardian_phone_sign` STRING, `deleted` STRING, `create_time` STRING, `update_time` STRING>,
    `OLD_VALUES` ROW<`student_uid` STRING, `student_name` STRING,  `phone_sign` STRING, `guardian` STRING,  `guardian_phone_sign` STRING, `deleted` STRING, `create_time` STRING, `update_time` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = '787_homework_zhibo_dau_tblStudent',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = 'zlink.zb_student_dal.online',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_zhibo_dau_student_0(
    `student_uid` BIGINT,
    `student_name` STRING,
    `phone_sign` STRING,
    `guardian` STRING,
    `guardian_phone_sign` STRING,
    `deleted` BIGINT,
    `create_time` BIGINT,
    `update_time` BIGINT,
      `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '************:9200',
    'index' = 'external_tbl_dau_student_info_v1',
    'username' = 'fywwbaseservice_app',
    'password' = 'fwRBIYocjsruKDhnJVy_v'
);

CREATE TEMPORARY VIEW view_binlog_homework_zhibo_dau_student_0 AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_homework_zhibo_dau_student_0;

CREATE TEMPORARY VIEW view_source_homework_zhibo_exam_tbl_answer_detail_0 AS
SELECT
    CAST(binlog_data.`student_uid` AS BIGINT) AS `student_uid`,
    CAST(binlog_data.`student_name` AS STRING) AS `student_name`,
    CAST(binlog_data.`phone_sign` AS STRING) AS `phone_sign`,
    CAST(binlog_data.`guardian` AS STRING) AS `guardian`,
    CAST(binlog_data.`guardian_phone_sign` AS STRING) AS `guardian_phone_sign`,
    CAST(binlog_data.`deleted` AS BIGINT) AS `deleted`,
    CAST(binlog_data.`create_time` AS BIGINT) AS `create_time`,
    CAST(binlog_data.`update_time` AS BIGINT) AS `update_time`
FROM
    view_binlog_homework_zhibo_dau_student_0;

INSERT INTO
    es_zhibo_dau_student_0
SELECT
    `student_uid`,
    `student_name`,
    `phone_sign`,
    `guardian`,
    `guardian_phone_sign`,
    `deleted`,
    `create_time`,
    `update_time` ,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    view_source_homework_zhibo_exam_tbl_answer_detail_0;