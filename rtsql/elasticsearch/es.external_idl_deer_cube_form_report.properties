CREATE TABLE binlog_homework_zhibo_exam_tbl_dyxz_form_report (
    BINLOG_NAME VARCHAR,
    BINLOG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `GLOBAL_ID` VARCHAR,
    `NEW_VALUES` ROW<`id` STRING, `uid` STRING, `openid` STRING, `unionid` STRING, `order_id` STRING, `province` STRING, `province_code` STRING, `city` STRING, `city_code` STRING, `county` STRING, `county_code` STRING, `school_name` STRING, `grade_id` STRING, `class_num` STRING, `student_name` STRING, `student_age` STRING, `form_done` STRING, `create_time` STRING, `update_time` STRING, `deleted` STRING, `business_type` STRING>,
    `OLD_VALUES` ROW<`id` STRING, `uid` STRING, `openid` STRING, `unionid` STRING, `order_id` STRING, `province` STRING, `province_code` STRING, `city` STRING, `city_code` STRING, `county` STRING, `county_code` STRING, `school_name` STRING, `grade_id` STRING, `class_num` STRING, `student_name` STRING, `student_age` STRING, `form_done` STRING, `create_time` STRING, `update_time` STRING, `deleted` STRING, `business_type` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = 'binlog-homework_zhibo_qudao-tblDyxzFormReport-orderby-pk',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = 'zlink.fwyy-dataengine',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_dyxz_form_report (
    `id` BIGINT, -- 主键id
    `student_uid` BIGINT, -- 作业帮uid
    `openid` STRING, -- 微信openid
    `unionid` STRING, -- 微信unionid
    `order_id` BIGINT, -- 订单id
    `province` STRING, -- 省
    `province_code` STRING, -- 省code
    `city` STRING, -- 市
    `city_code` STRING, -- 市code
    `county` STRING, -- 区
    `county_code` STRING, -- 区code
    `school_name` STRING, -- 学校名称
    `grade_id` INT, -- 学生的年级id
    `class_num` INT, -- 班级号
    `student_name` STRING, -- 学生姓名
    `student_age` INT, -- 学生年龄
    `create_time` INT, -- 创建时间
    `update_time` INT, -- 修改时间
    `deleted` TINYINT, -- 是否删除
    `business_type` TINYINT, -- 1小鹿编程，2小鹿写字
    `zbk_delete_flag` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'external_idl_deer_cube_form_report_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

CREATE TEMPORARY VIEW view_binlog_homework_zhibo_exam_tbl_dyxz_form_report AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_homework_zhibo_exam_tbl_dyxz_form_report;

CREATE TEMPORARY VIEW view_source_homework_zhibo_exam_tbl_dyxz_form_report AS
SELECT
    CAST(binlog_data.`id` AS BIGINT) AS `id`,
    CAST(binlog_data.`uid` AS BIGINT) AS `uid`,
    binlog_data.`openid` AS `openid`,
    binlog_data.`unionid` AS `unionid`,
    CAST(binlog_data.`order_id` AS BIGINT) AS `order_id`,
    binlog_data.`province` AS `province`,
    binlog_data.`province_code` AS `province_code`,
    binlog_data.`city` AS `city`,
    binlog_data.`city_code` AS `city_code`,
    binlog_data.`county` AS `county`,
    binlog_data.`county_code` AS `county_code`,
    binlog_data.`school_name` AS `school_name`,
    CAST(binlog_data.`grade_id` AS INT) AS `grade_id`,
    CAST(binlog_data.`class_num` AS INT) AS `class_num`,
    binlog_data.`student_name` AS `student_name`,
    CAST(binlog_data.`student_age` AS INT) AS `student_age`,
    CAST(binlog_data.`form_done` AS TINYINT) AS `form_done`,
    CAST(binlog_data.`create_time` AS INT) AS `create_time`,
    CAST(binlog_data.`update_time` AS INT) AS `update_time`,
    CAST(binlog_data.`deleted` AS TINYINT) AS `deleted`,
    CAST(binlog_data.`business_type` AS TINYINT) AS `business_type`,
    zbk_delete_flag
FROM
    view_binlog_homework_zhibo_exam_tbl_dyxz_form_report;

INSERT INTO
    es_tbl_dyxz_form_report
SELECT
    `id`,
    `uid`,
    `openid`,
    `unionid`,
    `order_id`,
    `province`,
    `province_code`,
    `city`,
    `city_code`,
    `county`,
    `county_code`,
    `school_name`,
    `grade_id`,
    `class_num`,
    `student_name`,
    `student_age`,
    `create_time`,
    `update_time`,
    `deleted`,
    `business_type`,
    `zbk_delete_flag`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    view_source_homework_zhibo_exam_tbl_dyxz_form_report
WHERE `form_done` = 1 -- 仅同步已填写的表单
    AND `uid` > 0;