CREATE TABLE zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_agg (
    `hg_binlog_event_type` BIGINT,
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `unlock_lesson_id` BIGINT,
    `correct_qualify_stu_cnt` BIGINT,
    `wrong_question_stu_cnt` BIGINT,
    `task_execute_stu_cnt` BIGINT,
    `total_correct_cnt` BIGINT,
    `total_question_cnt` BIGINT,
    `transfer_type` BIGINT,
    `new_user_type` STRING,
    `save_time` STRING
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_question_course_task_agg',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_adl_exercise_note_l1_course_task_agg (
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `unlock_lesson_id` BIGINT,
    `correct_qualify_stu_cnt` BIGINT,
    `wrong_question_stu_cnt` BIGINT,
    `task_execute_stu_cnt` BIGINT,
    `total_correct_cnt` BIGINT,
    `total_question_cnt` BIGINT,
    `transfer_type` BIGINT,
    `new_user_type` STRING,
    `save_time` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (assistant_uid, course_id, task_id, transfer_type, new_user_type) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_exercise_note_l1_course_task_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_adl_exercise_note_l1_course_task_agg
SELECT
    `assistant_uid`,
    `course_id`,
    `task_id`,
    `unlock_lesson_id`,
    `correct_qualify_stu_cnt`,
    `wrong_question_stu_cnt`,
    `task_execute_stu_cnt`,
    `total_correct_cnt`,
    `total_question_cnt`,
    `transfer_type`,
    `new_user_type`,
    `save_time`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_agg
WHERE
    hg_binlog_event_type IN(5, 7);
