-- u维度明细数据 学生ycl报名信息
CREATE TABLE source_zyb_zbk_deer_dws_dws_deer_exam_ycl_register_info_u (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `status` BIGINT,
    `review_time` BIGINT,
    `review_msg` STRING,
    `create_time` BIGINT,
    `update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_deer_dws.dws_deer_exam_ycl_register_info_u',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_deer_dws_dws_deer_exam_ycl_register_info_u (
    `student_uid` BIGINT,
    `ycl_status` BIGINT,
    `ycl_review_time` BIGINT,
    `ycl_review_msg` STRING,
    `ycl_create_time` BIGINT,
    `ycl_update_time` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_u_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_deer_dws_dws_deer_exam_ycl_register_info_u
SELECT
    `student_uid`,
    `status` AS `ycl_status`,
    `review_time` AS `ycl_review_time`,
    `review_msg` AS `ycl_review_msg`,
    `create_time` AS `ycl_create_time`,
    `update_time` AS `ycl_update_time`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_deer_dws_dws_deer_exam_ycl_register_info_u
WHERE
    `hg_binlog_event_type` IN (5, 7);