CREATE TABLE binlog_homework_deer_ty_tblweekreport_0 (
    BINLOG_NAME VARCHAR,
    BINLOG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `GLOBAL_ID` VARCHAR,
    `NEW_VALUES` ROW<`id` STRING, `report_id` STRING, `user_id` STRING, `course_id` STRING, `lesson_ids` STRING, `week` STRING, `status` STRING, `open_status` STRING, `rating` STRING, `rank_detail` STRING, `dan_detail` STRING, `video_url` STRING, `duration_detail` STRING, `finish_detail` STRING, `kcal_detail` STRING, `heart_rate_detail` STRING, `start_time` STRING, `create_time` STRING, `update_time` STRING>,
    `OLD_VALUES` ROW<`id` STRING, `report_id` STRING, `user_id` STRING, `course_id` STRING, `lesson_ids` STRING, `week` STRING, `status` STRING, `open_status` STRING, `rating` STRING, `rank_detail` STRING, `dan_detail` STRING, `video_url` STRING, `duration_detail` STRING, `finish_detail` STRING, `kcal_detail` STRING, `heart_rate_detail` STRING, `start_time` STRING, `create_time` STRING, `update_time` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = '757_deer_ty_tblWeekReport',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = '${kafka.consumer.groupid}',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_deer_ty_tblweekreport_0 (
    id BIGINT COMMENT 'id',
    report_id BIGINT COMMENT '报告id',
    user_id BIGINT COMMENT 'userID',
    course_id BIGINT COMMENT '课程id',
    lesson_ids STRING comment '章节id列表',
    week BIGINT COMMENT '周',
    status BIGINT COMMENT '生成状态 1已生成 2未生成',
    open_status BIGINT COMMENT '查看状态 1已查看 2未查看',
    rating BIGINT COMMENT '报告评级 1卓越 2优秀 3良好',
    rank_detail STRING COMMENT '每章节小组排名',
    dan_detail STRING comment '每章节段位id',
    duration_detail STRING COMMENT '每章节运动时长',
    finish_detail STRING comment '每章节是否完课',
    kcal_detail STRING comment '每章节运动消耗千卡',
    heart_rate_detail STRING comment '每章节心率',
    start_time BIGINT COMMENT '	周报开始时间',
    create_time BIGINT COMMENT '创建时间',
    update_time BIGINT COMMENT '更新时间',
    zbk_update_time BIGINT COMMENT '时间',
    PRIMARY KEY (
        `id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = 'http://************:9200',
    'index' = 'deer_ty_week_report_v1',
    'username' = 'workbench_app',
    'password' = 'RI_WLDQktvrViCzGsopAF'
);

CREATE TEMPORARY VIEW view_binlog_homework_deer_ty_tblweekreport_0 AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_homework_deer_ty_tblweekreport_0;

CREATE TEMPORARY VIEW view_source_homework_deer_ty_tblweekreport_0 AS
SELECT
    CAST(binlog_data.`id` AS BIGINT) AS `id`,
    CAST(binlog_data.`report_id` AS BIGINT) AS `report_id`,
    CAST(binlog_data.`user_id` AS BIGINT) AS `user_id`,
    CAST(binlog_data.`course_id` AS BIGINT) AS `course_id`,
    binlog_data.`lesson_ids` AS `lesson_ids`,
    CAST(binlog_data.`week` AS BIGINT) AS `week`,
    CAST(binlog_data.`status` AS BIGINT) AS `status`,
    CAST(binlog_data.`open_status` AS INT) AS `open_status`,
    CAST(binlog_data.`rating` AS INT) AS `rating`,
    binlog_data.`rank_detail` AS `rank_detail`,
    binlog_data.`dan_detail` AS `dan_detail`,
    binlog_data.`duration_detail` AS `duration_detail`,
    binlog_data.`finish_detail` AS `finish_detail`,
    binlog_data.`kcal_detail` AS `kcal_detail`,
    binlog_data.`heart_rate_detail` AS `heart_rate_detail`,
    CAST(binlog_data.`start_time` AS INT) AS `start_time`,
    CAST(binlog_data.`create_time` AS INT) AS `create_time`,
    CAST(binlog_data.`update_time` AS INT) AS `update_time`,
    zbk_delete_flag
FROM
    view_binlog_homework_deer_ty_tblweekreport_0;

INSERT INTO
    es_tbl_deer_ty_tblweekreport_0
SELECT
   id,
    report_id,
    user_id,
    course_id,
    lesson_ids,
    week,
    status,
    open_status,
    rating,
    rank_detail,
    dan_detail,
    duration_detail,
    finish_detail,
    kcal_detail,
    heart_rate_detail,
    start_time,
    create_time,
    update_time,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    view_source_homework_deer_ty_tblweekreport_0;