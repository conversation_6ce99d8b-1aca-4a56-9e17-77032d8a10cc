-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_interact_lu_common (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `afterclass_chat_cnt` BIGINT,
    `afterclass_hands_up_cnt` BIGINT,
    `afterclass_net_delay_cnt` BIGINT,
    `afterclass_participate_cnt` BIGINT,
    `afterclass_quick_red_packet_cnt` BIGINT,
    `afterclass_red_packet_cnt` BIGINT,
    `afterclass_right_cnt` BIGINT,
    `afterclass_video_link_cnt` BIGINT,
    `beforeclass_chat_cnt` BIGINT,
    `beforeclass_hands_up_cnt` BIGINT,
    `beforeclass_net_delay_cnt` BIGINT,
    `beforeclass_participate_cnt` BIGINT,
    `beforeclass_quick_red_packet_cnt` BIGINT,
    `beforeclass_red_packet_cnt` BIGINT,
    `beforeclass_right_cnt` BIGINT,
    `beforeclass_video_link_cnt` B<PERSON>IN<PERSON>,
    `inclass_assistant_hands_up_cnt` <PERSON>IGIN<PERSON>,
    `inclass_assistant_net_delay_cnt` BIGINT,
    `inclass_assistant_quick_red_packet_cnt` BIGINT,
    `inclass_assistant_red_packet_cnt` BIGINT,
    `inclass_assistant_video_link_cnt` BIGINT,
    `inclass_chat_cnt` BIGINT,
    `inclass_hands_up_cnt` BIGINT,
    `inclass_net_delay_cnt` BIGINT,
    `inclass_participate_cnt` BIGINT,
    `inclass_question_cnt` BIGINT,
    `inclass_quick_red_packet_cnt` BIGINT,
    `inclass_red_packet_cnt` BIGINT,
    `inclass_right_cnt` BIGINT,
    `inclass_video_link_cnt` BIGINT,
    `interact_answer_detail` STRING,
    `interact_answer_label` STRING,
    `lbp_interaction_right_num` BIGINT,
    `lbp_interaction_submit_num` BIGINT,
    `mix_live_interaction_right_num` BIGINT,
    `mix_live_interaction_submit_num` BIGINT,
    `mix_playback_interaction_right_num` BIGINT,
    `mix_playback_interaction_submit_num` BIGINT,
    `new_course_type` BIGINT,
    `playback_interact_last_submit_time` BIGINT,
    `playback_interactive_detail` STRING,
    `playback_participate_cnt` BIGINT,
    `playback_right_cnt` BIGINT,
    `year_season` STRING,
    `inclass_answer_status` BIGINT,
    `before_answer_status` BIGINT,
    `after_answer_status` BIGINT,
    `inclass_lp_open_mouth_cnt` BIGINT,
    `inclass_aigc_interact_cnt` BIGINT,
    `inclass_aigc_interact_ti_cnt` BIGINT,
    `inclass_aigc_question_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_interact_lu_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_interact_lu_common (
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `afterclass_chat_cnt` BIGINT,
    `afterclass_hands_up_cnt` BIGINT,
    `afterclass_net_delay_cnt` BIGINT,
    `afterclass_participate_cnt` BIGINT,
    `afterclass_quick_red_packet_cnt` BIGINT,
    `afterclass_red_packet_cnt` BIGINT,
    `afterclass_right_cnt` BIGINT,
    `afterclass_video_link_cnt` BIGINT,
    `beforeclass_chat_cnt` BIGINT,
    `beforeclass_hands_up_cnt` BIGINT,
    `beforeclass_net_delay_cnt` BIGINT,
    `beforeclass_participate_cnt` BIGINT,
    `beforeclass_quick_red_packet_cnt` BIGINT,
    `beforeclass_red_packet_cnt` BIGINT,
    `beforeclass_right_cnt` BIGINT,
    `beforeclass_video_link_cnt` BIGINT,
    `inclass_assistant_hands_up_cnt` BIGINT,
    `inclass_assistant_net_delay_cnt` BIGINT,
    `inclass_assistant_quick_red_packet_cnt` BIGINT,
    `inclass_assistant_red_packet_cnt` BIGINT,
    `inclass_assistant_video_link_cnt` BIGINT,
    `inclass_chat_cnt` BIGINT,
    `inclass_hands_up_cnt` BIGINT,
    `inclass_net_delay_cnt` BIGINT,
    `inclass_participate_cnt` BIGINT,
    `inclass_question_cnt` BIGINT,
    `inclass_quick_red_packet_cnt` BIGINT,
    `inclass_red_packet_cnt` BIGINT,
    `inclass_right_cnt` BIGINT,
    `inclass_video_link_cnt` BIGINT,
    `interact_answer_detail` STRING,
    `interact_answer_label` STRING,
    `lbp_interaction_right_num` BIGINT,
    `lbp_interaction_submit_num` BIGINT,
    `mix_live_interaction_right_num` BIGINT,
    `mix_live_interaction_submit_num` BIGINT,
    `mix_playback_interaction_right_num` BIGINT,
    `mix_playback_interaction_submit_num` BIGINT,
    `new_course_type` BIGINT,
    `playback_interact_last_submit_time` BIGINT,
    `playback_interactive_detail` STRING,
    `playback_participate_cnt` BIGINT,
    `playback_right_cnt` BIGINT,
    `year_season` STRING,
    `inclass_answer_status` BIGINT,
    `before_answer_status` BIGINT,
    `after_answer_status` BIGINT,
    `inclass_lp_open_mouth_cnt` BIGINT,
    `inclass_aigc_interact_cnt` BIGINT,
    `inclass_aigc_interact_ti_cnt` BIGINT,
    `inclass_aigc_interact_ti_total_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `lesson_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_interact_lu_common
SELECT
    `lesson_id`,
    `student_uid`,
    `course_id`,
    `afterclass_chat_cnt`,
    `afterclass_hands_up_cnt`,
    `afterclass_net_delay_cnt`,
    `afterclass_participate_cnt`,
    `afterclass_quick_red_packet_cnt`,
    `afterclass_red_packet_cnt`,
    `afterclass_right_cnt`,
    `afterclass_video_link_cnt`,
    `beforeclass_chat_cnt`,
    `beforeclass_hands_up_cnt`,
    `beforeclass_net_delay_cnt`,
    `beforeclass_participate_cnt`,
    `beforeclass_quick_red_packet_cnt`,
    `beforeclass_red_packet_cnt`,
    `beforeclass_right_cnt`,
    `beforeclass_video_link_cnt`,
    `inclass_assistant_hands_up_cnt`,
    `inclass_assistant_net_delay_cnt`,
    `inclass_assistant_quick_red_packet_cnt`,
    `inclass_assistant_red_packet_cnt`,
    `inclass_assistant_video_link_cnt`,
    `inclass_chat_cnt`,
    `inclass_hands_up_cnt`,
    `inclass_net_delay_cnt`,
    `inclass_participate_cnt`,
    `inclass_question_cnt`,
    `inclass_quick_red_packet_cnt`,
    `inclass_red_packet_cnt`,
    `inclass_right_cnt`,
    `inclass_video_link_cnt`,
    `interact_answer_detail`,
    `interact_answer_label`,
    `lbp_interaction_right_num`,
    `lbp_interaction_submit_num`,
    `mix_live_interaction_right_num`,
    `mix_live_interaction_submit_num`,
    `mix_playback_interaction_right_num`,
    `mix_playback_interaction_submit_num`,
    `new_course_type`,
    `playback_interact_last_submit_time`,
    `playback_interactive_detail`,
    `playback_participate_cnt`,
    `playback_right_cnt`,
    `year_season`,
    `inclass_answer_status`,
    `before_answer_status`,
    `after_answer_status`,
    `inclass_lp_open_mouth_cnt`,
    `inclass_aigc_interact_cnt`,
    `inclass_aigc_interact_ti_cnt`,
    `inclass_aigc_question_cnt` AS `inclass_aigc_interact_ti_total_cnt`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_interact_lu_common
WHERE
    `hg_binlog_event_type` IN (5, 7);