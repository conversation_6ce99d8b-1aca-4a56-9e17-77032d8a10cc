CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_ca_agg_business_lpc   (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `business_uid` BIGINT,
    `enterprise_wechat_course_total_leads_num` BIGINT,
    `enterprise_wechat_course_total_appearance_duration` BIGINT,
    `enterprise_wechat_1v1_course_leads_num`   BIGINT,
    `enterprise_wechat_1v1_course_total_appearance_duration`   BIGINT,
    `enterprise_wechat_1v2_6_course_leads_num`   BIGINT,
    `enterprise_wechat_1v2_6_course_total_appearance_duration`   BIGINT,
    `enterprise_wechat_1v7_course_leads_num`   BIGINT,
    `enterprise_wechat_1v7_course_total_appearance_duration`   BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_ca_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE es_adl_lpc_qw_course_data_agg (
  `course_id` BIGINT,
  `business_uid` BIGINT,
  `enterprise_wechat_course_total_leads_num` BIGINT,
  `enterprise_wechat_course_total_appearance_duration` BIGINT,
  `enterprise_wechat_1v1_course_leads_num`   BIGINT,
  `enterprise_wechat_1v1_course_total_appearance_duration`   BIGINT,
  `enterprise_wechat_1v2_6_course_leads_num`   BIGINT,
  `enterprise_wechat_1v2_6_course_total_appearance_duration`   BIGINT,
  `enterprise_wechat_1v7_course_leads_num`   BIGINT,
  `enterprise_wechat_1v7_course_total_appearance_duration`   BIGINT,
  `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id,business_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_lpc_qw_course_data_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    es_adl_lpc_qw_course_data_agg
SELECT
  `course_id` ,
    `business_uid` ,
    `enterprise_wechat_course_total_leads_num` ,
    `enterprise_wechat_course_total_appearance_duration` ,
    `enterprise_wechat_1v1_course_leads_num`   ,
    `enterprise_wechat_1v1_course_total_appearance_duration`   ,
    `enterprise_wechat_1v2_6_course_leads_num`   ,
    `enterprise_wechat_1v2_6_course_total_appearance_duration`   ,
    `enterprise_wechat_1v7_course_leads_num`  ,
    `enterprise_wechat_1v7_course_total_appearance_duration`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_ca_agg_business_lpc
WHERE
    hg_binlog_event_type IN(5, 7);