-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_service_call_compound_cu_day(
    `hg_binlog_event_type` BIGINT,
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `save_time` BIGINT,
    `all_num` BIGINT,
    `day_wechat_num` BIGINT,
    `access_num` BIGINT,
    `day_wechat_access_num` BIGINT,
    `day_touch_num` BIGINT,
    `day_wechat_touch_num` BIGINT,
    `day_3min_num` BIGINT,
    `day_3min_wechat_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_service_call_compound_cu_day',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);
-- 数仓主键：course_id, student_uid, save_time；工作台主键：course_id, student_uid；同步维度数据量过大，工作台目前只会取当天的数据
CREATE TABLE dest_zyb_zbk_bzr_dws_dws_zbk_service_call_compound_cu_day (
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `save_time` BIGINT,
    `all_num` BIGINT,
    `day_wechat_num` BIGINT,
    `access_num` BIGINT,
    `day_wechat_access_num` BIGINT,
    `day_touch_num` BIGINT,
    `day_wechat_touch_num` BIGINT,
    `day_3min_num` BIGINT,
    `day_3min_wechat_num` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_assistant_course_student_info_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_dws_dws_zbk_service_call_compound_cu_day
SELECT
    `assistant_uid`,
    `course_id`,
    `student_uid`,
    `save_time`,
    `all_num`,
    `day_wechat_num`,
    `access_num`,
    `day_wechat_access_num`,
    `day_touch_num`,
    `day_wechat_touch_num`,
    `day_3min_num`,
    `day_3min_wechat_num`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_service_call_compound_cu_day
WHERE
    `hg_binlog_event_type` <> 3;