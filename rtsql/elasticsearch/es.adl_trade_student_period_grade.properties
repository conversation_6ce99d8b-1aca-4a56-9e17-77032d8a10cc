-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_dws_trade_student_period_grade_agg (
    `hg_binlog_event_type` BIGINT,
    `grade_id` BIGINT,
    `grade_period_id` BIGINT,
    `learn_season` BIGINT,
    `learn_year` BIGINT,
    `reserve_course` STRING,
    `reserve_subject` STRING,
    `season` BIGINT,
    `student_uid` BIGINT,
    `valid_subjects` STRING,
    `earliest_trade_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.dws_trade_student_period_grade_agg',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_trade_student_period_grade_agg (
    `grade_id` BIGINT,
    `grade_period_id` BIGINT,
    `learn_season` BIGINT,
    `learn_year` BIGINT,
    `reserve_course` STRING,
    `reserve_subject` STRING,
    `season` BIGINT,
    `student_uid` BIGINT,
    `valid_subjects` STRING,
    `earliest_trade_time` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `student_uid`,
        `learn_year`,
        `season`,
        `learn_season`,
        `grade_id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_trade_student_period_grade_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_trade_student_period_grade_agg
SELECT
    `grade_id`,
    `grade_period_id`,
    `learn_season`,
    `learn_year`,
    `reserve_course`,
    `reserve_subject`,
    `season`,
    `student_uid`,
    `valid_subjects`,
    `earliest_trade_time`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_dws_trade_student_period_grade_agg
WHERE
    `hg_binlog_event_type` <> 3;