-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_trade_student_season_department_agg (
    `hg_binlog_event_type` BIGINT,
    `earliest_reserve_time` BIGINT,
    `grade_period_id` BIGINT,
    `learn_season` BIGINT,
    `learn_year` BIGINT,
    `reserve_grade_subject` STRING,
    `season` BIGINT,
    `student_uid` BIGINT,
    `valid_grade_subject` STRING
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_trade_student_season_department_agg',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_trade_student_season_department_agg (
    `earliest_reserve_time` BIGINT,
    `grade_period_id` BIGINT,
    `learn_season` BIGINT,
    `learn_year` BIGINT,
    `reserve_grade_subject` STRING,
    `season` BIGINT,
    `student_uid` BIGINT,
    `valid_grade_subject` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
       `student_uid`,
       `learn_year`,
       `season`,
       `learn_season`,
       `grade_period_id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_trade_student_season_department_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_trade_student_season_department_agg
SELECT
    `earliest_reserve_time`,
    `grade_period_id`,
    `learn_season`,
    `learn_year`,
    `reserve_grade_subject`,
    `season`,
    `student_uid`,
    `valid_grade_subject`, -- 有效报名课程对应的年级学科编码，e.g 7-1,7-2,7-3
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_trade_student_season_department_agg
WHERE
    `hg_binlog_event_type` <> 3;