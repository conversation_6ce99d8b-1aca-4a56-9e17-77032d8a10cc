-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_lesson_common (
    `lbp_interaction_total_num` BIGINT,
    `mix_interaction_total_num` BIGINT,
    `lesson_id` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = 'hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80',
    'username' = 'LTAI5t5ta8eK5UciPg6fM45e',
    'password' = '******************************',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_lesson_common',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_lesson_common (
    `lbp_interaction_total_num` BIGINT,
    `mix_interaction_total_num` BIGINT,
    `lesson_id` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (lesson_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = 'http://************:9200',
    'index' = 'idl_lesson_common_v2',
    'username' = 'workbench_app',
    'password' = 'RI_WLDQktvrViCzGsopAF'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_lesson_common
SELECT
    `lbp_interaction_total_num`,
    `mix_interaction_total_num`,
    `lesson_id`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_lesson_common;