-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_exam_cu_common (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `challenge_finish_lesson_cnt` BIGINT,
    `exam32_total_submit_lesson_cnt` BIGINT,
    `exam53_s_amend_exam_num` BIGINT,
    `exam53_submit_exam_num` BIGINT,
    `homework_submit_lesson_cnt` BIGINT,
    `lesson1_finish_lesson_cnt` BIGINT,
    `main_always_test_exam_finish_num` BIGINT,
    `main_consolidation_exam_finish_num` BIGINT,
    `main_consolidation_exam_right_num` BIGINT,
    `main_exam32_total_submit_lesson_cnt` BIGINT,
    `main_homework_submit_lesson_cnt` BIGINT,
    `main_interactive_exam_finish_num` BIGINT,
    `main_interactive_exam_right_num` BIGINT,
    `main_interactive_exam_total_num` BIGINT,
    `main_preview_exam_finish_num` BIGINT,
    `main_preview_exam_right_num` BIGINT,
    `main_preview_finish_cnt` BIGINT,
    `main_stage_test_submit_lesson_cnt` BIGINT,
    `new_course_type` BIGINT,
    `niudao_finish_lesson_cnt` BIGINT,
    `preview_finish_cnt` BIGINT,
    `preview_lessonid_array` STRING,
    `stage_test_submit_lesson_cnt` BIGINT,
    `xiuchang_finish_lesson_cnt` BIGINT,
    `open_mouth_cnt` BIGINT,
    `exam60_total_submit_lesson_cnt` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_cu_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_exam_cu_common (
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `challenge_finish_lesson_cnt` BIGINT,
    `exam32_total_submit_lesson_cnt` BIGINT,
    `exam53_s_amend_exam_num` BIGINT,
    `exam53_submit_exam_num` BIGINT,
    `homework_submit_lesson_cnt` BIGINT,
    `lesson1_finish_lesson_cnt` BIGINT,
    `main_always_test_exam_finish_num` BIGINT,
    `main_consolidation_exam_finish_num` BIGINT,
    `main_consolidation_exam_right_num` BIGINT,
    `main_exam32_total_submit_lesson_cnt` BIGINT,
    `main_homework_submit_lesson_cnt` BIGINT,
    `main_interactive_exam_finish_num` BIGINT,
    `main_interactive_exam_right_num` BIGINT,
    `main_interactive_exam_total_num` BIGINT,
    `main_preview_exam_finish_num` BIGINT,
    `main_preview_exam_right_num` BIGINT,
    `main_preview_finish_cnt` BIGINT,
    `main_stage_test_submit_lesson_cnt` BIGINT,
    `new_course_type` BIGINT,
    `niudao_finish_lesson_cnt` BIGINT,
    `preview_finish_cnt` BIGINT,
    `preview_lessonid_array` STRING,
    `stage_test_submit_lesson_cnt` BIGINT,
    `xiuchang_finish_lesson_cnt` BIGINT,
    `open_mouth_cnt` BIGINT,
    `exam60_total_submit_lesson_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `course_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_course_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_exam_cu_common
SELECT
    `course_id`,
    `student_uid`,
    `challenge_finish_lesson_cnt`,
    `exam32_total_submit_lesson_cnt`,
    `exam53_s_amend_exam_num`,
    `exam53_submit_exam_num`,
    `homework_submit_lesson_cnt`,
    `lesson1_finish_lesson_cnt`,
    `main_always_test_exam_finish_num`,
    `main_consolidation_exam_finish_num`,
    `main_consolidation_exam_right_num`,
    `main_exam32_total_submit_lesson_cnt`,
    `main_homework_submit_lesson_cnt`,
    `main_interactive_exam_finish_num`,
    `main_interactive_exam_right_num`,
    `main_interactive_exam_total_num`,
    `main_preview_exam_finish_num`,
    `main_preview_exam_right_num`,
    `main_preview_finish_cnt`,
    `main_stage_test_submit_lesson_cnt`,
    `new_course_type`,
    `niudao_finish_lesson_cnt`,
    `preview_finish_cnt`,
    `preview_lessonid_array`,
    `stage_test_submit_lesson_cnt`,
    `xiuchang_finish_lesson_cnt`,
    `open_mouth_cnt` ,
    `exam60_total_submit_lesson_cnt`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_exam_cu_common
WHERE
    `hg_binlog_event_type` IN (5, 7);