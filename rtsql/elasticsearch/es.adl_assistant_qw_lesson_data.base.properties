CREATE TABLE zyb_zbk_bzr_dws_dws_zbk_baseinfo_la_agg_bzr  (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `assistant_uid` BIGINT,
    `transfer_type` BIGINT,
    `new_user_type` STRING,
    `save_time` STRING,
    `reg_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_baseinfo_la_agg_bzr',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE es_adl_assistant_qw_lesson_data_agg (
    `lesson_id` BIGINT,
    `assistant_uid` BIGINT,
    `transfer_type` BIGINT,
    `new_user_type` STRING,
    `save_time` STRING,
    `reg_num`   BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (lesson_id,assistant_uid,transfer_type,new_user_type,save_time) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_assistant_qw_lesson_data_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    es_adl_assistant_qw_lesson_data_agg
SELECT
    `lesson_id`,
    `assistant_uid`,
    `transfer_type`,
    `new_user_type`,
    `save_time`,
    `reg_num`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_bzr_dws_dws_zbk_baseinfo_la_agg_bzr
WHERE
    hg_binlog_event_type IN(5, 7);