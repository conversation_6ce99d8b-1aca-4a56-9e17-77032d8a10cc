-- 编程报告状态：
CREATE TABLE binlog_external_idl_lesson_student_deerbc_report (
    BINLOG_NAME VARCHAR,
    BINLOG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `<PERSON><PERSON><PERSON><PERSON>L_ID` VARCHAR,
    `NEW_VALUES` ROW<`student_uid` STRING, `lesson_id` STRING, `course_id` STRING, `status` STRING>,
    `OLD_VALUES` ROW<`student_uid` STRING, `lesson_id` STRING, `course_id` STRING, `status` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = '623_deer_bc_report_student_reports',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = '${kafka.consumer.groupid}',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_external_idl_lesson_student (
    `student_uid` BIGINT, -- 学生ID
    `lesson_id` BIGINT, -- 章节ID
    `course_id` BIGINT, -- 课程ID
    `bc_report_status` TINYINT, -- 推送状态：0未生成；1待批改；2待推送；3已推送
    `bc_report_delete_flag` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `student_uid`, `lesson_id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'external_idl_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

CREATE TEMPORARY VIEW view_binlog_external_idl_lesson_student_deerbc_report AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_external_idl_lesson_student_deerbc_report;

CREATE TEMPORARY VIEW view_source_external_idl_lesson_student_deerbc_report AS
SELECT
    CAST(binlog_data.`student_uid` AS BIGINT) AS `student_uid`,
    CAST(binlog_data.`lesson_id` AS BIGINT) AS `lesson_id`,
    CAST(binlog_data.`course_id` AS BIGINT) AS `course_id`,
    CAST(binlog_data.`status` AS TINYINT) AS `status`,
    zbk_delete_flag
FROM
    view_binlog_external_idl_lesson_student_deerbc_report;

INSERT INTO
    es_tbl_external_idl_lesson_student
SELECT
    `student_uid`,
    `lesson_id`,
    `course_id`,
    `status` AS `bc_report_status`,
    `zbk_delete_flag` AS `bc_report_delete_flag`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    view_source_external_idl_lesson_student_deerbc_report
WHERE `student_uid` > 0;

