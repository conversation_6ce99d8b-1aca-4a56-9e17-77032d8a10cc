-- 学情诊断规划报告：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=474986536
CREATE TABLE binlog_homework_zhibo_exam_tbl_edu_probe (
    BINLOG_NAME VARCHAR,
    BIN<PERSON>OG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `G<PERSON><PERSON><PERSON>L_ID` VARCHAR,
    `NEW_VALUES` ROW<`course_id` STRING, `student_uid` STRING, `pdf_stauts` STRING, `pdf_name` STRING, `pdf_obj_name` STRING, `pdf_time` STRING>,
    `OLD_VALUES` ROW<`course_id` STRING, `student_uid` STRING, `pdf_stauts` STRING, `pdf_name` STRING, `pdf_obj_name` STRING, `pdf_time` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = 'binlog-homework_fwyy_eduprobe-tblEduProbe-orderby-pk',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = '${kafka.consumer.groupid}',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_edu_probe (
    `course_id` BIGINT, -- 课程ID
    `student_uid` BIGINT, -- 学生ID
    `edu_probe_pdf_stauts` TINYINT, -- pdf状态  0未生成 1已生成 2已过期(代表对象存储已移除) 3生成失败
    `edu_probe_pdf_file_name` STRING, -- pdf文件名称
    `edu_probe_pdf_obj_name` STRING, -- 对象存储对象名称
    `edu_probe_pdf_time` BIGINT, -- pdf生成时间
    `edu_probe_delete_flag` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'external_idl_course_student_learning_report_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

CREATE TEMPORARY VIEW view_binlog_homework_zhibo_exam_tbl_edu_probe AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_homework_zhibo_exam_tbl_edu_probe;

CREATE TEMPORARY VIEW view_source_homework_zhibo_exam_tbl_edu_probe AS
SELECT
    CAST(binlog_data.`course_id` AS BIGINT) AS `course_id`,
    CAST(binlog_data.`student_uid` AS BIGINT) AS `student_uid`,
    CAST(binlog_data.`pdf_stauts` AS TINYINT) AS `pdf_stauts`,
    binlog_data.`pdf_name` AS `pdf_name`,
    binlog_data.`pdf_obj_name` AS `pdf_obj_name`,
    CAST(binlog_data.`pdf_time` AS BIGINT) AS `pdf_time`,
    zbk_delete_flag
FROM
    view_binlog_homework_zhibo_exam_tbl_edu_probe;

INSERT INTO
    es_tbl_edu_probe
SELECT
    `course_id`,
    `student_uid`,
    `pdf_stauts` AS `edu_probe_pdf_stauts`, -- cu维度的报告都放一个索引，重命名
    `pdf_name` AS `edu_probe_pdf_file_name`,
    `pdf_obj_name` AS `edu_probe_pdf_obj_name`,
    `pdf_time` AS `edu_probe_pdf_time`,
    `zbk_delete_flag` AS `edu_probe_delete_flag`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    view_source_homework_zhibo_exam_tbl_edu_probe;