CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_user_tag_lpc (
    `hg_binlog_event_type` BIGINT,
    `user_id` BIGINT ,
    `is_subject_user` BIGINT,
    `is_suyang_user` BIGINT,
    `user_label` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_user_tag',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_dim_student_user_info (
    `student_uid` BIGINT,
    `is_subject_user` BIGINT,
    `is_suyang_user` BIGINT,
    `user_label` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_dim_student_user_info_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_dim_student_user_info
SELECT
    `user_id` AS student_uid,
    `is_subject_user`,
    `is_suyang_user`,
    `user_label`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_user_tag_lpc
WHERE
    hg_binlog_event_type IN(5, 7);