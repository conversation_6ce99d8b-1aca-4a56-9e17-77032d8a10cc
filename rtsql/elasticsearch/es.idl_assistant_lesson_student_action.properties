-- 声明用到的所有表
CREATE FUNCTION decode_exam_answer as 'com.streaming.flink.udf.ExamAnswer' LANGUAGE JAVA;

--hologres源表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_student_action_v1 (
    `hg_binlog_event_type` BIGINT,
    `all_lesson_index` BIGINT,
    `assistant_uid` BIGINT,
    `attend` BIGINT,
    `attend_detail` STRING,
    `attend_duration` BIGINT,
    `attend_long` BIGINT,
    `attend_quarter` BIGINT,
    `boost_lesson_index` BIGINT,
    `chat_num` BIGINT,
    `course_id` BIGINT,
    `course_name` STRING,
    `course_type` BIGINT,
    `exam_answer` STRING,
    `hx_alltest_finishnum` BIGINT,
    `hx_pretest_finishnum` BIGINT,
    `inout_count` BIGINT,
    `interaction_answer_detail` STRING,
    `is_assistantcourse_attend` BIGINT,
    `is_assistantcourse_finish` BIGINT,
    `is_attend_finish` BIGINT,
    `is_lbp_attend` BIGINT,
    `is_lbp_attend_finish` BIGINT,
    `is_need_attend` BIGINT,
    `is_need_attend_after_unlock` BIGINT,
    `is_playback_finish_after_unlock` BIGINT,
    `is_playback_finish_after_unlock_7d` BIGINT,
    `is_playback_long_after_unlock` BIGINT,
    `is_playback_long_after_unlock_7d` BIGINT,
    `is_playback_quarter` BIGINT,
    `is_postclass_attend` BIGINT,
    `is_postclass_finish_attend` BIGINT,
    `is_preclass_attend` BIGINT,
    `is_preclass_finish_attend` BIGINT,
    `is_view_finish_in_14d` BIGINT,
    `is_view_finished` BIGINT,
    `last_playback_time` BIGINT,
    `lbp_attend_duration` BIGINT,
    `learn_season` BIGINT,
    `lesson_deleted` BIGINT,
    `lesson_id` BIGINT,
    `lesson_name` STRING,
    `lesson_start_time` BIGINT,
    `lesson_status` BIGINT,
    `lesson_stop_time` BIGINT,
    `lesson_type` BIGINT,
    `lesson_unlock_time` BIGINT,
    `main_department` BIGINT,
    `main_grade` BIGINT,
    `main_lesson_index` BIGINT,
    `main_subject` BIGINT,
    `new_user_type` STRING,
    `photo_assistant_uid` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `playback_time` BIGINT,
    `playback_time_after_unlock` BIGINT,
    `playback_time_after_unlock_7d` BIGINT,
    `playback_time_in_14d` BIGINT,
    `playback_time_in_7d` BIGINT,
    `postclass_attend_duration` BIGINT,
    `pre_attend` BIGINT,
    `preclass_attend_duration` BIGINT,
    `restart_id` BIGINT,
    `student_attend_label` STRING,
    `student_interaction_label` STRING,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `teacher_id` BIGINT,
    `trade_change_status` BIGINT,
    `trade_change_time` BIGINT,
    `trade_create_time` BIGINT,
    `trade_refund_time` BIGINT,
    `trade_status` BIGINT,
    `year` BIGINT,
    `lbp_play_detail` STRING,
    `lbp_online_duration` BIGINT,
    `lbp_last_playtime` BIGINT,
    `lbp_interaction_right_num` BIGINT,
    `lbp_interaction_submit_num` BIGINT,
    `is_inclass_teacher_room_view_attend` BIGINT,
    `is_inclass_teacher_room_view_finish_v1` BIGINT,
    `inclass_teacher_room_total_playback_time_v1` BIGINT,
    `inclass_teacher_room_total_playback_content_time` BIGINT,
    `homework_first_correct_cnt` BIGINT,
    `homework_first_right_cnt` BIGINT,
    `inclass_teacher_room_day14_playback_time_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_day14_v1` BIGINT,
    `is_inclass_teacher_room_view_attend_day14` BIGINT,
    `is_inclass_teacher_room_attend_one_third` BIGINT,
    `is_inclass_teacher_room_attend_two_third` BIGINT,
    `mix_live_interaction_right_num` BIGINT,
    `mix_live_interaction_submit_num` BIGINT,
    `mix_playback_interaction_right_num` BIGINT,
    `mix_playback_interaction_submit_num` BIGINT,
    `is_inclass_teacher_room_total_playback_three_four_v1` BIGINT,
    `is_inclass_teacher_room_day14_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_total_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_total_playback_three_five_v1` BIGINT,
    `leap_user_type` STRING,
    `exam33_first_correct_cnt` BIGINT,
    `exam33_first_right_cnt` BIGINT,
    `beforeclass_puzzle_answer_detail` STRING,
    `inclass_puzzle_answer_detail` STRING,
    `afterclass_puzzle_answer_detail` STRING,
    `beforeclass_puzzle_participate_cnt` BIGINT,
    `incalss_puzzle_participate_cnt` BIGINT,
    `after_puzzle_participate_cnt` BIGINT,
    `ide_finish_status` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_student_action_v1',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_student_action_v1 (
    `all_lesson_index` BIGINT,
    `assistant_uid` BIGINT,
    `attend` BIGINT,
    `attend_detail` STRING,
    `attend_duration` BIGINT,
    `attend_long` BIGINT,
    `attend_quarter` BIGINT,
    `boost_lesson_index` BIGINT,
    `chat_num` BIGINT,
    `course_id` BIGINT,
    `course_name` STRING,
    `course_type` BIGINT,
    `exam_answer` MAP<STRING, MAP<STRING, STRING>>,
    `hx_alltest_finishnum` BIGINT,
    `hx_pretest_finishnum` BIGINT,
    `inout_count` BIGINT,
    `interaction_answer_detail` STRING,
    `is_assistantcourse_attend` BIGINT,
    `is_assistantcourse_finish` BIGINT,
    `is_attend_finish` BIGINT,
    `is_lbp_attend` BIGINT,
    `is_lbp_attend_finish` BIGINT,
    `is_need_attend` BIGINT,
    `is_need_attend_after_unlock` BIGINT,
    `is_playback_finish_after_unlock` BIGINT,
    `is_playback_finish_after_unlock_7d` BIGINT,
    `is_playback_long_after_unlock` BIGINT,
    `is_playback_long_after_unlock_7d` BIGINT,
    `is_playback_quarter` BIGINT,
    `is_postclass_attend` BIGINT,
    `is_postclass_finish_attend` BIGINT,
    `is_preclass_attend` BIGINT,
    `is_preclass_finish_attend` BIGINT,
    `is_view_finish_in_14d` BIGINT,
    `is_view_finished` BIGINT,
    `last_playback_time` BIGINT,
    `lbp_attend_duration` BIGINT,
    `learn_season` BIGINT,
    `lesson_deleted` BIGINT,
    `lesson_id` BIGINT,
    `lesson_name` STRING,
    `lesson_start_time` BIGINT,
    `lesson_status` BIGINT,
    `lesson_stop_time` BIGINT,
    `lesson_type` BIGINT,
    `lesson_unlock_time` BIGINT,
    `main_department` BIGINT,
    `main_grade` BIGINT,
    `main_lesson_index` BIGINT,
    `main_subject` BIGINT,
    `new_user_type` STRING,
    `photo_assistant_uid` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `playback_time` BIGINT,
    `playback_time_after_unlock` BIGINT,
    `playback_time_after_unlock_7d` BIGINT,
    `playback_time_in_14d` BIGINT,
    `playback_time_in_7d` BIGINT,
    `postclass_attend_duration` BIGINT,
    `pre_attend` BIGINT,
    `preclass_attend_duration` BIGINT,
    `restart_id` BIGINT,
    `student_attend_label` STRING,
    `student_interaction_label` STRING,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `teacher_id` BIGINT,
    `trade_change_status` BIGINT,
    `trade_change_time` BIGINT,
    `trade_create_time` BIGINT,
    `trade_refund_time` BIGINT,
    `trade_status` BIGINT,
    `year` BIGINT,
    `lbp_play_detail` STRING,
    `lbp_online_duration` BIGINT,
    `lbp_last_playtime` BIGINT,
    `lbp_interaction_right_num` BIGINT,
    `lbp_interaction_submit_num` BIGINT,
    `is_inclass_teacher_room_view_attend` BIGINT,
    `is_inclass_teacher_room_view_finish_v1` BIGINT,
    `inclass_teacher_room_total_playback_time_v1` BIGINT,
    `inclass_teacher_room_total_playback_content_time` BIGINT,
    `homework_first_correct_cnt` BIGINT,
    `homework_first_right_cnt` BIGINT,
    `inclass_teacher_room_day14_playback_time_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_day14_v1` BIGINT,
    `is_inclass_teacher_room_view_attend_day14` BIGINT,
    `is_inclass_teacher_room_attend_one_third` BIGINT,
    `is_inclass_teacher_room_attend_two_third` BIGINT,
    `mix_live_interaction_right_num` BIGINT,
    `mix_live_interaction_submit_num` BIGINT,
    `mix_playback_interaction_right_num` BIGINT,
    `mix_playback_interaction_submit_num` BIGINT,
    `is_inclass_teacher_room_total_playback_three_four_v1` BIGINT,
    `is_inclass_teacher_room_day14_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_total_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_total_playback_three_five_v1` BIGINT,
    `leap_user_type` STRING,
    `exam33_first_correct_cnt` BIGINT,
    `exam33_first_right_cnt` BIGINT,
    `beforeclass_puzzle_answer_detail` STRING,
    `inclass_puzzle_answer_detail` STRING,
    `afterclass_puzzle_answer_detail` STRING,
    `beforeclass_puzzle_participate_cnt` BIGINT,
    `incalss_puzzle_participate_cnt` BIGINT,
    `after_puzzle_participate_cnt` BIGINT,
    `ide_finish_status` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id, lesson_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_assistant_lesson_student_action_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_student_action_v1
SELECT
    `all_lesson_index`,
    COALESCE(`assistant_uid`, 0) AS `assistant_uid`,
    `attend`,
    `attend_detail`,
    `attend_duration`,
    `attend_long`,
    `attend_quarter`,
    `boost_lesson_index`,
    `chat_num`,
    COALESCE(`course_id`, 0) AS `course_id`,
    `course_name`,
    `course_type`,
    decode_exam_answer(`exam_answer`),
    `hx_alltest_finishnum`,
    `hx_pretest_finishnum`,
    `inout_count`,
    `interaction_answer_detail`,
    `is_assistantcourse_attend`,
    `is_assistantcourse_finish`,
    `is_attend_finish`,
    `is_lbp_attend`,
    `is_lbp_attend_finish`,
    `is_need_attend`,
    `is_need_attend_after_unlock`,
    `is_playback_finish_after_unlock`,
    `is_playback_finish_after_unlock_7d`,
    `is_playback_long_after_unlock`,
    `is_playback_long_after_unlock_7d`,
    `is_playback_quarter`,
    `is_postclass_attend`,
    `is_postclass_finish_attend`,
    `is_preclass_attend`,
    `is_preclass_finish_attend`,
    `is_view_finish_in_14d`,
    `is_view_finished`,
    `last_playback_time`,
    `lbp_attend_duration`,
    `learn_season`,
    `lesson_deleted`,
    `lesson_id`,
    `lesson_name`,
    `lesson_start_time`,
    `lesson_status`,
    `lesson_stop_time`,
    `lesson_type`,
    `lesson_unlock_time`,
    `main_department`,
    `main_grade`,
    `main_lesson_index`,
    `main_subject`,
    `new_user_type`,
    `photo_assistant_uid`,
    `playback_participate_num`,
    `playback_right_num`,
    `playback_time`,
    `playback_time_after_unlock`,
    `playback_time_after_unlock_7d`,
    `playback_time_in_14d`,
    `playback_time_in_7d`,
    `postclass_attend_duration`,
    `pre_attend`,
    `preclass_attend_duration`,
    `restart_id`,
    `student_attend_label`,
    `student_interaction_label`,
    `student_uid`,
    `sub_trade_id`,
    `teacher_id`,
    `trade_change_status`,
    `trade_change_time`,
    `trade_create_time`,
    `trade_refund_time`,
    `trade_status`,
    `year`,
    `lbp_play_detail`,
    `lbp_online_duration`,
    `lbp_last_playtime`,
    `lbp_interaction_right_num`,
    `lbp_interaction_submit_num`,
    `is_inclass_teacher_room_view_attend`,
    `is_inclass_teacher_room_view_finish_v1`,
    `inclass_teacher_room_total_playback_time_v1`,
    `inclass_teacher_room_total_playback_content_time`,
    `homework_first_correct_cnt`,
    `homework_first_right_cnt`,
    `inclass_teacher_room_day14_playback_time_v1`,
    `is_inclass_teacher_room_view_finish_day14_v1`,
    `is_inclass_teacher_room_view_attend_day14`,
    `is_inclass_teacher_room_attend_one_third`,
    `is_inclass_teacher_room_attend_two_third`,
    `mix_live_interaction_right_num`,
    `mix_live_interaction_submit_num`,
    `mix_playback_interaction_right_num`,
    `mix_playback_interaction_submit_num`,
    `is_inclass_teacher_room_total_playback_three_four_v1`,
    `is_inclass_teacher_room_day14_playback_three_five_v1`,
    `is_inclass_teacher_room_view_finish_total_playback_three_five_v1`,
    `is_inclass_teacher_room_total_playback_three_five_v1`,
    `leap_user_type`,
    `exam33_first_correct_cnt`,
    `exam33_first_right_cnt`,
    `beforeclass_puzzle_answer_detail`,
    `inclass_puzzle_answer_detail`,
    `afterclass_puzzle_answer_detail`,
    `beforeclass_puzzle_participate_cnt`,
    `incalss_puzzle_participate_cnt`,
    `after_puzzle_participate_cnt`,
    `ide_finish_status`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_student_action_v1
WHERE
    `course_id` > 0
    AND `hg_binlog_event_type` <> 3
    AND `year` not in (2016, 2017, 2018, 2019);