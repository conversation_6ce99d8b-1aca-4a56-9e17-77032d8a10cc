-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_assistant_course_student (
    `hg_binlog_event_type` BIGINT,
    `assistant_uid` BIGINT,
    `class_id` BIGINT,
    `course_id` BIGINT,
    `reg_time` BIGINT,
    `status` BIGINT,
    `student_uid` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_bdl_assistant_course_student',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_bdl_assistant_course_student (
    `assistant_uid` BIGINT,
    `class_id` BIGINT,
    `course_id` BIGINT,
    `reg_time` BIGINT,
    `status` BIGINT,
    `student_uid` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'bdl_assistant_course_student_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_bdl_assistant_course_student
SELECT
    COALESCE(`assistant_uid`, 0) AS `assistant_uid`,
    COALESCE(`class_id`, 0) AS `class_id`,
    `course_id`,
    `reg_time`,
    COALESCE(`status`, 0) AS `status`,
    `student_uid`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_assistant_course_student
WHERE
    `hg_binlog_event_type` <> 3;