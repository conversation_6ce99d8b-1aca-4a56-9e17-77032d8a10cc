-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_season_student_info (
    `hg_binlog_event_type` BIGINT,
    `grade_id` BIGINT,
    `pre_subject` STRING,
    `pre_subject_num` STRING,
    `season_id` BIGINT,
    `student_uid` BIGINT,
    `subject` STRING,
    `subject_num` BIGINT,
    `year` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_season_student_info',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_season_student_info (
    `grade_id` BIGINT,
    `pre_subject` STRING,
    `pre_subject_num` STRING,
    `season_id` BIGINT,
    `student_uid` BIGINT,
    `subject` STRING,
    `subject_num` BIGINT,
    `year` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`season_id`, `year`, `student_uid`, `grade_id`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_season_student_info_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_season_student_info
SELECT
    `grade_id`,
    `pre_subject`,
    `pre_subject_num`,
    `season_id`,
    `student_uid`,
    `subject`,
    `subject_num`,
    `year`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_season_student_info
WHERE
    `hg_binlog_event_type` <> 3;