-- cu维度聚合表
CREATE TABLE source_zyb_zbk_lpc_dws_dws_zbk_sop_eduprobe_middle_cu_lpc (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `submission_id_lastest` BIGINT,
    `course_id_last` BIGINT,
    `submission_id_last` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_sop_eduprobe_middle_cu_lpc',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_lpc_dws_dws_zbk_sop_eduprobe_middle_cu_lpc (
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `submission_id_lastest` BIGINT,
    `course_id_last` BIGINT,
    `submission_id_last` BIGINT,
    `zbk_update_time` BIGINT,
PRIMARY KEY (student_uid, course_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'index' = 'adl_student_course_agg_v1',
    'hosts' = '${es.workbench.hosts}',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_lpc_dws_dws_zbk_sop_eduprobe_middle_cu_lpc
SELECT
    `student_uid`,
    `course_id`,
    `submission_id_lastest`,
    `course_id_last`,
    `submission_id_last`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_lpc_dws_dws_zbk_sop_eduprobe_middle_cu_lpc
WHERE
    `hg_binlog_event_type` <> 3;