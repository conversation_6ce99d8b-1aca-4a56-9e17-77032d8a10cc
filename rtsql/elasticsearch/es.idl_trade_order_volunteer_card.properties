-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_trade_order_volunteer_card (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `is_valid_volunteer_card` BIGINT,
    `product_type` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `trade_status` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_trade_order_volunteer_card',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_trade_order_volunteer_card (
    `course_id` BIGINT,
    `is_valid_volunteer_card` BIGINT,
    `product_type` BIGINT,
    `student_uid` BIGINT,
    `sub_trade_id` BIGINT,
    `trade_status` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `student_uid`, `sub_trade_id`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_trade_order_volunteer_card_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_trade_order_volunteer_card
SELECT
    `course_id`,
    `is_valid_volunteer_card`,
    `product_type`,
    `student_uid`,
    `sub_trade_id`,
    `trade_status`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_trade_order_volunteer_card
WHERE
    `hg_binlog_event_type` <> 3;