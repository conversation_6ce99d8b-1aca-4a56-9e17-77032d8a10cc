-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_attendance_lu_common (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `afterclass_assistant_room_14day_playback_time` BIGINT,
    `afterclass_assistant_room_14day_playback_time_v1` BIGINT,
    `afterclass_assistant_room_attend_duration` BIGINT,
    `afterclass_assistant_room_day7_playback_time` BIGINT,
    `afterclass_assistant_room_total_playback_time` BIGINT,
    `afterclass_assistant_room_total_playback_time_v1` BIGINT,
    `ai_last_leave_time` BIGINT,
    `ai_view_duration` BIGINT,
    `beforclass_assistant_room_14day_playback_time` BIGINT,
    `beforclass_assistant_room_14day_playback_time_v1` BIGINT,
    `beforeclass_assistant_room_attend_duration` BIGINT,
    `beforeclass_assistant_room_day7_playback_time` BIGINT,
    `beforeclass_assistant_room_total_playback_time` BIGINT,
    `beforeclass_assistant_room_total_playback_time_v1` BIGINT,
    `first_enter_time` BIGINT,
    `inclass_assistant_room_attend_duration` BIGINT,
    `inclass_teacher_room_attend_detail` STRING,
    `inclass_teacher_room_attend_duration` BIGINT,
    `inclass_teacher_room_attend_inout_cnt` BIGINT,
    `inclass_teacher_room_day14_playback_time` BIGINT,
    `inclass_teacher_room_day14_playback_time_v1` BIGINT,
    `inclass_teacher_room_day7_playback_time` BIGINT,
    `inclass_teacher_room_day7_unlock_playback_time` BIGINT,
    `inclass_teacher_room_total_playback_content_time` BIGINT,
    `inclass_teacher_room_total_playback_time` BIGINT,
    `inclass_teacher_room_total_playback_time_v1` BIGINT,
    `inclass_teacher_room_unlock_total_playback_time` BIGINT,
    `is_afterclass_assistant_room_attend` BIGINT,
    `is_afterclass_assistant_room_attend_finish` BIGINT,
    `is_ai_over` BIGINT,
    `is_ai_room_attend` BIGINT,
    `is_assistantcourse_attend` BIGINT,
    `is_assistantcourse_finish` BIGINT,
    `is_assistantcourse_need_attend` BIGINT,
    `is_attend_ai` BIGINT,
    `is_attend_finish_ai` BIGINT,
    `is_beforeclass_assistant_room_attend` BIGINT,
    `is_beforeclass_assistant_room_attend_finish` BIGINT,
    `is_inclass_teacher_room_attend_30minute` BIGINT,
    `is_inclass_teacher_room_attend_5minute` BIGINT,
    `is_inclass_teacher_room_attend_and_playback_30minute_v1` BIGINT,
    `is_inclass_teacher_room_attend_finish` BIGINT,
    `is_inclass_teacher_room_attend_one_four` BIGINT,
    `is_inclass_teacher_room_attend_one_third` BIGINT,
    `is_inclass_teacher_room_attend_two_third` BIGINT,
    `is_inclass_teacher_room_day14_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_day7_unlock_playback_30minute` BIGINT,
    `is_inclass_teacher_room_day7_unlock_playback_finish` BIGINT,
    `is_inclass_teacher_room_playback_30minute` BIGINT,
    `is_inclass_teacher_room_playback_finish` BIGINT,
    `is_inclass_teacher_room_playback_one_four` BIGINT,
    `is_inclass_teacher_room_total_playback_30minute` BIGINT,
    `is_inclass_teacher_room_total_playback_5minute` BIGINT,
    `is_inclass_teacher_room_total_playback_5minute_v1` BIGINT,
    `is_inclass_teacher_room_total_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_total_playback_three_four` BIGINT,
    `is_inclass_teacher_room_total_playback_three_four_v1` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_30minute` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_5minute` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_finish` BIGINT,
    `is_inclass_teacher_room_view_attend` BIGINT,
    `is_inclass_teacher_room_view_attend_day14` BIGINT,
    `is_inclass_teacher_room_view_finish` BIGINT,
    `is_inclass_teacher_room_view_finish_day14` BIGINT,
    `is_inclass_teacher_room_view_finish_day14_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_total_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_v1` BIGINT,
    `is_lbp_attend_5minute` BIGINT,
    `is_lbp_attend_finish` BIGINT,
    `is_need_attend_ai` BIGINT,
    `is_participate_class` BIGINT,
    `last_exit_time` BIGINT,
    `last_playback_time` BIGINT,
    `last_playback_timepoint` BIGINT,
    `lbp_attend_duration` BIGINT,
    `lbp_last_playtime` BIGINT,
    `lbp_online_duration` BIGINT,
    `lbp_play_detail` STRING,
    `new_course_type` BIGINT,
    `pre_attend` BIGINT,
    `student_attend_label` BIGINT,
    `total_ai_view_duration` BIGINT,
    `user_view_detail_json` STRING,
    `year_season` STRING,
    `inclass_teacher_room_day7_playback_time_v1` BIGINT,
    `is_inclass_teacher_room_day7_view_30minute_v1` BIGINT,
    `is_inclass_teacher_room_day7_view_finish_three_four_v1` BIGINT,
    `is_inclass_teacher_room_content_view_30minute` BIGINT,
    `is_inclass_teacher_room_content_view_finish_three_four` BIGINT,
    `is_lbp_attend_30minute` BIGINT,
    `is_inclass_teacher_room_content_view_5minute` BIGINT,
    `is_generate_lesson_report` BIGINT,
    `is_inclass_teacher_room_content_view_10s` BIGINT,
    `is_inclass_teacher_room_content_view_finish_85percent` BIGINT,
    `is_submit_lesson_work` BIGINT,
    `beforeclass_video_content_time` BIGINT,
    `afterclass_video_content_time` BIGINT,
    `is_inclass_teacher_room_total_playback_content_time_ge_3min` BIGINT,
    `is_inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration` BIGINT,
    `is_ai_beforeclass_teacher_room_content_view_finish_5percent` BIGINT,
    `is_ai_inclass_teacher_room_content_view_finish_5percent` BIGINT,
    `is_ai_afterclass_teacher_room_content_view_finish_5percent` BIGINT,
    `is_ai_beforeclass_teacher_room_content_view_finish_three_four` BIGINT,
    `is_ai_afterclass_teacher_room_content_view_finish_three_four` BIGINT,
    `is_inclass_teacher_room_zplusp_content_view_5min` BIGINT,
    `is_inclass_teacher_room_zplusp_content_view_three_four` BIGINT,
    `inclass_teacher_room_zplusp_content_view_duration` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_attendance_lu_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_attendance_lu_common (
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `afterclass_assistant_room_14day_playback_time` BIGINT,
    `afterclass_assistant_room_14day_playback_time_v1` BIGINT,
    `afterclass_assistant_room_attend_duration` BIGINT,
    `afterclass_assistant_room_day7_playback_time` BIGINT,
    `afterclass_assistant_room_total_playback_time` BIGINT,
    `afterclass_assistant_room_total_playback_time_v1` BIGINT,
    `ai_last_leave_time` BIGINT,
    `ai_view_duration` BIGINT,
    `beforclass_assistant_room_14day_playback_time` BIGINT,
    `beforclass_assistant_room_14day_playback_time_v1` BIGINT,
    `beforeclass_assistant_room_attend_duration` BIGINT,
    `beforeclass_assistant_room_day7_playback_time` BIGINT,
    `beforeclass_assistant_room_total_playback_time` BIGINT,
    `beforeclass_assistant_room_total_playback_time_v1` BIGINT,
    `first_enter_time` BIGINT,
    `inclass_assistant_room_attend_duration` BIGINT,
    `inclass_teacher_room_attend_detail` STRING,
    `inclass_teacher_room_attend_duration` BIGINT,
    `inclass_teacher_room_attend_inout_cnt` BIGINT,
    `inclass_teacher_room_day14_playback_time` BIGINT,
    `inclass_teacher_room_day14_playback_time_v1` BIGINT,
    `inclass_teacher_room_day7_playback_time` BIGINT,
    `inclass_teacher_room_day7_unlock_playback_time` BIGINT,
    `inclass_teacher_room_total_playback_content_time` BIGINT,
    `inclass_teacher_room_total_playback_time` BIGINT,
    `inclass_teacher_room_total_playback_time_v1` BIGINT,
    `inclass_teacher_room_unlock_total_playback_time` BIGINT,
    `is_afterclass_assistant_room_attend` BIGINT,
    `is_afterclass_assistant_room_attend_finish` BIGINT,
    `is_ai_over` BIGINT,
    `is_ai_room_attend` BIGINT,
    `is_assistantcourse_attend` BIGINT,
    `is_assistantcourse_finish` BIGINT,
    `is_assistantcourse_need_attend` BIGINT,
    `is_attend_ai` BIGINT,
    `is_attend_finish_ai` BIGINT,
    `is_beforeclass_assistant_room_attend` BIGINT,
    `is_beforeclass_assistant_room_attend_finish` BIGINT,
    `is_inclass_teacher_room_attend_30minute` BIGINT,
    `is_inclass_teacher_room_attend_5minute` BIGINT,
    `is_inclass_teacher_room_attend_and_playback_30minute_v1` BIGINT,
    `is_inclass_teacher_room_attend_finish` BIGINT,
    `is_inclass_teacher_room_attend_one_four` BIGINT,
    `is_inclass_teacher_room_attend_one_third` BIGINT,
    `is_inclass_teacher_room_attend_two_third` BIGINT,
    `is_inclass_teacher_room_day14_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_day7_unlock_playback_30minute` BIGINT,
    `is_inclass_teacher_room_day7_unlock_playback_finish` BIGINT,
    `is_inclass_teacher_room_playback_30minute` BIGINT,
    `is_inclass_teacher_room_playback_finish` BIGINT,
    `is_inclass_teacher_room_playback_one_four` BIGINT,
    `is_inclass_teacher_room_total_playback_30minute` BIGINT,
    `is_inclass_teacher_room_total_playback_5minute` BIGINT,
    `is_inclass_teacher_room_total_playback_5minute_v1` BIGINT,
    `is_inclass_teacher_room_total_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_total_playback_three_four` BIGINT,
    `is_inclass_teacher_room_total_playback_three_four_v1` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_30minute` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_5minute` BIGINT,
    `is_inclass_teacher_room_unlock_total_playback_finish` BIGINT,
    `is_inclass_teacher_room_view_attend` BIGINT,
    `is_inclass_teacher_room_view_attend_day14` BIGINT,
    `is_inclass_teacher_room_view_finish` BIGINT,
    `is_inclass_teacher_room_view_finish_day14` BIGINT,
    `is_inclass_teacher_room_view_finish_day14_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_total_playback_three_five_v1` BIGINT,
    `is_inclass_teacher_room_view_finish_v1` BIGINT,
    `is_lbp_attend_5minute` BIGINT,
    `is_lbp_attend_finish` BIGINT,
    `is_need_attend_ai` BIGINT,
    `is_participate_class` BIGINT,
    `last_exit_time` BIGINT,
    `last_playback_time` BIGINT,
    `last_playback_timepoint` BIGINT,
    `lbp_attend_duration` BIGINT,
    `lbp_last_playtime` BIGINT,
    `lbp_online_duration` BIGINT,
    `lbp_play_detail` STRING,
    `new_course_type` BIGINT,
    `pre_attend` BIGINT,
    `student_attend_label` BIGINT,
    `total_ai_view_duration` BIGINT,
    `user_view_detail_json` STRING,
    `year_season` STRING,
    `inclass_teacher_room_day7_playback_time_v1` BIGINT,
    `is_inclass_teacher_room_day7_view_30minute_v1` BIGINT,
    `is_inclass_teacher_room_day7_view_finish_three_four_v1` BIGINT,
    `is_inclass_teacher_room_content_view_30minute` BIGINT,
    `is_inclass_teacher_room_content_view_finish_three_four` BIGINT,
    `is_lbp_attend_30minute` BIGINT,
    `is_inclass_teacher_room_content_view_5minute` BIGINT,
    `is_generate_lesson_report` BIGINT,
    `is_inclass_teacher_room_content_view_10s` BIGINT,
    `is_inclass_teacher_room_content_view_finish_85percent` BIGINT,
    `is_submit_lesson_work` BIGINT,
    `beforeclass_video_content_time` BIGINT,
    `afterclass_video_content_time` BIGINT,
    `is_inclass_teacher_room_total_playback_content_time_ge_3min` BIGINT,
    `is_inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration` BIGINT,
    `is_ai_beforeclass_teacher_room_content_view_finish_5percent` BIGINT,
    `is_ai_inclass_teacher_room_content_view_finish_5percent` BIGINT,
    `is_ai_afterclass_teacher_room_content_view_finish_5percent` BIGINT,
    `is_ai_beforeclass_teacher_room_content_view_finish_three_four` BIGINT,
    `is_ai_afterclass_teacher_room_content_view_finish_three_four` BIGINT,
    `is_inclass_teacher_room_zplusp_content_view_5min` BIGINT,
    `is_inclass_teacher_room_zplusp_content_view_three_four` BIGINT,
    `inclass_teacher_room_zplusp_content_view_duration` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `lesson_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_attendance_lu_common
SELECT
    `lesson_id`,
    `student_uid`,
    `course_id`,
    `afterclass_assistant_room_14day_playback_time`,
    `afterclass_assistant_room_14day_playback_time_v1`,
    `afterclass_assistant_room_attend_duration`,
    `afterclass_assistant_room_day7_playback_time`,
    `afterclass_assistant_room_total_playback_time`,
    `afterclass_assistant_room_total_playback_time_v1`,
    `ai_last_leave_time`,
    `ai_view_duration`,
    `beforclass_assistant_room_14day_playback_time`,
    `beforclass_assistant_room_14day_playback_time_v1`,
    `beforeclass_assistant_room_attend_duration`,
    `beforeclass_assistant_room_day7_playback_time`,
    `beforeclass_assistant_room_total_playback_time`,
    `beforeclass_assistant_room_total_playback_time_v1`,
    `first_enter_time`,
    `inclass_assistant_room_attend_duration`,
    `inclass_teacher_room_attend_detail`,
    `inclass_teacher_room_attend_duration`,
    `inclass_teacher_room_attend_inout_cnt`,
    `inclass_teacher_room_day14_playback_time`,
    `inclass_teacher_room_day14_playback_time_v1`,
    `inclass_teacher_room_day7_playback_time`,
    `inclass_teacher_room_day7_unlock_playback_time`,
    `inclass_teacher_room_total_playback_content_time`,
    `inclass_teacher_room_total_playback_time`,
    `inclass_teacher_room_total_playback_time_v1`,
    `inclass_teacher_room_unlock_total_playback_time`,
    `is_afterclass_assistant_room_attend`,
    `is_afterclass_assistant_room_attend_finish`,
    `is_ai_over`,
    `is_ai_room_attend`,
    `is_assistantcourse_attend`,
    `is_assistantcourse_finish`,
    `is_assistantcourse_need_attend`,
    `is_attend_ai`,
    `is_attend_finish_ai`,
    `is_beforeclass_assistant_room_attend`,
    `is_beforeclass_assistant_room_attend_finish`,
    `is_inclass_teacher_room_attend_30minute`,
    `is_inclass_teacher_room_attend_5minute`,
    `is_inclass_teacher_room_attend_and_playback_30minute_v1`,
    `is_inclass_teacher_room_attend_finish`,
    `is_inclass_teacher_room_attend_one_four`,
    `is_inclass_teacher_room_attend_one_third`,
    `is_inclass_teacher_room_attend_two_third`,
    `is_inclass_teacher_room_day14_playback_three_five_v1`,
    `is_inclass_teacher_room_day7_unlock_playback_30minute`,
    `is_inclass_teacher_room_day7_unlock_playback_finish`,
    `is_inclass_teacher_room_playback_30minute`,
    `is_inclass_teacher_room_playback_finish`,
    `is_inclass_teacher_room_playback_one_four`,
    `is_inclass_teacher_room_total_playback_30minute`,
    `is_inclass_teacher_room_total_playback_5minute`,
    `is_inclass_teacher_room_total_playback_5minute_v1`,
    `is_inclass_teacher_room_total_playback_three_five_v1`,
    `is_inclass_teacher_room_total_playback_three_four`,
    `is_inclass_teacher_room_total_playback_three_four_v1`,
    `is_inclass_teacher_room_unlock_total_playback_30minute`,
    `is_inclass_teacher_room_unlock_total_playback_5minute`,
    `is_inclass_teacher_room_unlock_total_playback_finish`,
    `is_inclass_teacher_room_view_attend`,
    `is_inclass_teacher_room_view_attend_day14`,
    `is_inclass_teacher_room_view_finish`,
    `is_inclass_teacher_room_view_finish_day14`,
    `is_inclass_teacher_room_view_finish_day14_v1`,
    `is_inclass_teacher_room_view_finish_total_playback_three_five_v1`,
    `is_inclass_teacher_room_view_finish_v1`,
    `is_lbp_attend_5minute`,
    `is_lbp_attend_finish`,
    `is_need_attend_ai`,
    `is_participate_class`,
    `last_exit_time`,
    `last_playback_time`,
    `last_playback_timepoint`,
    `lbp_attend_duration`,
    `lbp_last_playtime`,
    `lbp_online_duration`,
    `lbp_play_detail`,
    `new_course_type`,
    `pre_attend`,
    `student_attend_label`,
    `total_ai_view_duration`,
    `user_view_detail_json`,
    `year_season`,
    `inclass_teacher_room_day7_playback_time_v1`,
    `is_inclass_teacher_room_day7_view_30minute_v1`,
    `is_inclass_teacher_room_day7_view_finish_three_four_v1`,
    `is_inclass_teacher_room_content_view_30minute`,
    `is_inclass_teacher_room_content_view_finish_three_four`,
    `is_lbp_attend_30minute`,
    `is_inclass_teacher_room_content_view_5minute`,
    `is_generate_lesson_report`,
    `is_inclass_teacher_room_content_view_10s`,
    `is_inclass_teacher_room_content_view_finish_85percent`,
    `is_submit_lesson_work` ,
    `beforeclass_video_content_time`,
    `afterclass_video_content_time`,
    `is_inclass_teacher_room_total_playback_content_time_ge_3min`,
    `is_inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration`,
    `is_ai_beforeclass_teacher_room_content_view_finish_5percent`,
    `is_ai_inclass_teacher_room_content_view_finish_5percent`,
    `is_ai_afterclass_teacher_room_content_view_finish_5percent`,
    `is_ai_beforeclass_teacher_room_content_view_finish_three_four`,
    `is_ai_afterclass_teacher_room_content_view_finish_three_four`,
    `is_inclass_teacher_room_zplusp_content_view_5min`,
    `is_inclass_teacher_room_zplusp_content_view_three_four`,
    `inclass_teacher_room_zplusp_content_view_duration`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_attendance_lu_common
WHERE
    `hg_binlog_event_type` IN (5, 7);
