-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_exam_relation_v1 (
    `hg_binlog_event_type` BIGINT,
    `bind_id` BIGINT,
    `bind_status` BIGINT,
    `bind_type` BIGINT,
    `exam_id` BIGINT,
    `exam_tag` BIGINT,
    `exam_type` BIGINT,
    `is_artificial_correct` BIGINT,
    `question_detail_new` STRING,
    `relation_type` BIGINT,
    `total_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_bdl_exam_relation_v1',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_bdl_exam_relation_v1 (
    `bind_id` BIGINT,
    `bind_status` BIGINT,
    `bind_type` BIGINT,
    `exam_id` BIGINT,
    `exam_tag` BIGINT,
    `exam_type` BIGINT,
    `is_artificial_correct` BIGINT,
    `question_detail_new` STRING,
    `relation_type` BIGINT,
    `total_num` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `bind_id`,
        `bind_type`,
        `exam_tag`,
        `relation_type`,
        `exam_type`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'bdl_exam_relation_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_bdl_exam_relation_v1
SELECT
    `bind_id`,
    `bind_status`,
    `bind_type`,
    `exam_id`,
    `exam_tag`,
    `exam_type`,
    `is_artificial_correct`,
    `question_detail_new`,
    `relation_type`,
    `total_num`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_exam_relation_v1
WHERE
    `hg_binlog_event_type` <> 3;