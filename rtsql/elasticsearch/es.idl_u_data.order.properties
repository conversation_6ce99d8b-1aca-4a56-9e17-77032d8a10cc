-- u维度明细数据 订单数据
CREATE TABLE source_zyb_zbk_bzr_ads_ads_zbk_user_tag (
    `hg_binlog_event_type` BIGINT,
    `user_id` BIGINT,
    `lpc_trade_num` BIGINT,
    `lpc_trade_num_6mon` BIGINT,
    `lpc_trade_num_1y` BIGINT,
    `last_lpc_trade_time` BIGINT,
    `banke_trade_num` BIGINT,
    `last_banke_trade_time` BIGINT,
    `user_label` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads.ads_zbk_user_tag',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_user_tag (
    `student_uid` BIGINT,
    `lpc_trade_num` BIGINT,
    `lpc_trade_num_6mon` BIGINT,
    `lpc_trade_num_1y` BIGINT,
    `last_lpc_trade_time` BIGINT,
    `banke_trade_num` BIGINT,
    `last_banke_trade_time` BIGINT,
    `user_label` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_u_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_user_tag
SELECT
    `user_id` AS `student_uid`,
    `lpc_trade_num` AS `lpc_trade_num`,
    `lpc_trade_num_6mon` AS `lpc_trade_num_6mon`,
    `lpc_trade_num_1y` AS `lpc_trade_num_1y`,
    `last_lpc_trade_time` AS `last_lpc_trade_time`,
    `banke_trade_num` AS `banke_trade_num`,
    `last_banke_trade_time` AS `last_banke_trade_time`,
    `user_label` AS `user_label`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_ads_zbk_user_tag
WHERE
    `hg_binlog_event_type` IN (5, 7);