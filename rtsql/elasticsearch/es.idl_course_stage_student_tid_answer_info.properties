-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_tid_answer_info (
    `answer_id` STRING,
    `course_id` BIGINT,
    `exam_id` BIGINT,
    `exam_type` BIGINT,
    `is_right` BIGINT,
    `is_submit` BIGINT,
    `stage_id` BIGINT,
    `student_uid` BIGINT,
    `submit_time` BIGINT,
    `tid` BIGINT,
    `tid_idx` BIGINT,
    `unit_id` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = 'hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80',
    'username' = 'LTAI5t5ta8eK5UciPg6fM45e',
    'password' = '******************************',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_tid_answer_info',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_tid_answer_info (
    `answer_id` STRING,
    `course_id` BIGINT,
    `exam_id` BIGINT,
    `exam_type` BIGINT,
    `is_right` BIGINT,
    `is_submit` BIGINT,
    `stage_id` BIGINT,
    `student_uid` BIGINT,
    `submit_time` BIGINT,
    `tid` BIGINT,
    `tid_idx` BIGINT,
    `unit_id` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        course_id,
        stage_id,
        student_uid,
        exam_type,
        exam_id,
        answer_id,
        tid,
        tid_idx
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = 'http://************:9200',
    'index' = 'idl_course_stage_student_tid_answer_info_v1',
    'username' = 'workbench_app',
    'password' = 'RI_WLDQktvrViCzGsopAF'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_tid_answer_info
SELECT
    `answer_id`,
    `course_id`,
    `exam_id`,
    `exam_type`,
    `is_right`,
    COALESCE(`is_submit`, 0) AS `is_submit`,
    `stage_id`,
    `student_uid`,
    `submit_time`,
    `tid`,
    `tid_idx`,
    `unit_id`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_tid_answer_info;