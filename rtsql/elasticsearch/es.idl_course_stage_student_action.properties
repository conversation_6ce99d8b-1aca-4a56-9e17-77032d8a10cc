-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_action (
    `course_id` BIGINT,
    `exam27` STRING,
    `exam35` STRING,
    `exam36` STRING,
    `exam37` STRING,
    `exam38` STRING,
    `exam39` STRING,
    `exam40` STRING,
    `exam41` STRING,
    `exam42` STRING,
    `exam43` STRING,
    `exam44` STRING,
    `exam45` STRING,
    `exam46` STRING,
    `is_need_participate` BIGINT,
    `is_stage_finish` BIGINT,
    `stage_id` BIGINT,
    `stage_release_time` BIGINT,
    `student_uid` BIGINT,
    `unit_id` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_course_stage_student_action',
    'username' = 'LTAI5t5ta8eK5UciPg6fM45e',
    'password' = '******************************',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'endpoint' = 'hgprecn-cn-i7m2c0w9j001-cn-beijing-vpc-st.hologres.aliyuncs.com:80'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_action (
    `course_id` BIGINT,
    `exam27` STRING,
    `exam35` STRING,
    `exam36` STRING,
    `exam37` STRING,
    `exam38` STRING,
    `exam39` STRING,
    `exam40` STRING,
    `exam41` STRING,
    `exam42` STRING,
    `exam43` STRING,
    `exam44` STRING,
    `exam45` STRING,
    `exam46` STRING,
    `is_need_participate` BIGINT,
    `is_stage_finish` BIGINT,
    `stage_id` BIGINT,
    `stage_release_time` BIGINT,
    `student_uid` BIGINT,
    `unit_id` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id, student_uid, stage_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = 'http://************:9200',
    'index' = 'idl_course_stage_student_action',
    'username' = 'workbench_app',
    'password' = 'RI_WLDQktvrViCzGsopAF'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_stage_student_action
SELECT
    `course_id`,
    `exam27`,
    `exam35`,
    `exam36`,
    `exam37`,
    `exam38`,
    `exam39`,
    `exam40`,
    `exam41`,
    `exam42`,
    `exam43`,
    `exam44`,
    `exam45`,
    `exam46`,
    `is_need_participate`,
    `is_stage_finish`,
    `stage_id`,
    `stage_release_time`,
    `student_uid`,
    `unit_id`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_stage_student_action;