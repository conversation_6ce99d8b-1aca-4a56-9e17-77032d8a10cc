-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_attendance_cu_common (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `ai_attend_finish_lesson_num` BIGINT,
    `ai_attend_lesson_num` BIGINT,
    `all_lesson_attend_duration_sum` BIGINT,
    `all_lesson_duration_sum` BIGINT,
    `all_lesson_playback_duration_sum` BIGINT,
    `all_lesson_total_unlock_playback_time_sum` BIGINT,
    `attend_30minute_lesson_num` BIGINT,
    `attend_5minute_lesson_id_detail` STRING,
    `attend_5minute_lesson_num` BIGINT,
    `attend_5minute_main_lesson_num` BIGINT,
    `attend_5minute_zhibo_lesson_id_detail` STRING,
    `attend_and_playback_30minute_main_lesson_num_v1` BIGINT,
    `attend_finish_lesson_id_detail` STRING,
    `attend_finish_lesson_num` BIGINT,
    `attend_finish_main_lesson_num` BIGINT,
    `attend_finish_zhibo_lesson_id_detail` STRING,
    `attend_one_four_lesson_num` BIGINT,
    `attend_one_third_lesson_id_detail` STRING,
    `attend_one_third_lesson_num` BIGINT,
    `attend_two_third_lesson_id_detail` STRING,
    `attend_two_third_lesson_num` BIGINT,
    `cu_is_ai_attend` BIGINT,
    `cu_is_ai_finish` BIGINT,
    `cu_is_all_attend` BIGINT,
    `cu_is_all_finish` BIGINT,
    `cu_is_lbp_attend` BIGINT,
    `cu_is_lbp_attend_finish` BIGINT,
    `last_playback_time` BIGINT,
    `lbp_attend_count` BIGINT,
    `lbp_attend_lessonid_array` STRING,
    `lbp_finish_count` BIGINT,
    `lbp_finish_lessonid_array` STRING,
    `new_course_type` BIGINT,
    `no_attend_5minute_total_playback_5minute_lesson_id_detail` STRING,
    `no_attend_5minute_total_playback_5minute_lesson_num` BIGINT,
    `total_playback_5minute_lesson_id_detail` STRING,
    `total_playback_5minute_lesson_num` BIGINT,
    `total_playback_three_four_lesson_num` BIGINT,
    `total_unlock_playback_30minute_lesson_num` BIGINT,
    `total_unlock_playback_5minute_lesson_id_detail` STRING,
    `total_unlock_playback_5minute_lesson_num` BIGINT,
    `total_unlock_playback_lesson_num` BIGINT,
    `total_unlock_playback_three_four_lesson_id_detail` STRING,
    `total_unlock_playback_three_four_lesson_num` BIGINT,
    `unlock_7day_playback_30minute_lesson_num` BIGINT,
    `unlock_7day_playback_5minute_lesson_num` BIGINT,
    `unlock_7day_playback_three_four_lesson_num` BIGINT,
    `view_5minute_lesson_num` BIGINT,
    `view_finish_lesson_num` BIGINT,
    `view_finish_main_lesson_num_v1` BIGINT,
    `view_finish_total_playback_three_five_lesson_num_v1` BIGINT,
    `view_lesson_num` BIGINT,
    `year_season` STRING,
    `last_playback_timepoint` BIGINT,
    `last_playback_duration` BIGINT,
    `last_playback_lesson_id` BIGINT,
    `content_view_10s_lesson_num` BIGINT,
    `content_view_finish_85percent_lesson_num` BIGINT,
    `overall_finish_lesson_num` BIGINT,
    `is_generate_course_report` BIGINT,
    `inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num` BIGINT,
    `inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num` BIGINT,
    `mandatory_lbp_lesson_num` BIGINT,
    `attend_or_content_view_5minute_main_lesson_num` BIGINT,
    `attend_or_content_view_three_four_main_lesson_num` BIGINT,
    `zplusp_content_view_5min_jd_lesson_num_cu` BIGINT,
    `zplusp_content_view_5min_bd_lesson_num_cu` BIGINT,
    `zplusp_content_view_5min_dxjq_lesson_num_cu` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_attendance_cu_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_attendance_cu_common (
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `ai_attend_finish_lesson_num` BIGINT,
    `ai_attend_lesson_num` BIGINT,
    `all_lesson_attend_duration_sum` BIGINT,
    `all_lesson_duration_sum` BIGINT,
    `all_lesson_playback_duration_sum` BIGINT,
    `all_lesson_total_unlock_playback_time_sum` BIGINT,
    `attend_30minute_lesson_num` BIGINT,
    `attend_5minute_lesson_id_detail` STRING,
    `attend_5minute_lesson_num` BIGINT,
    `attend_5minute_main_lesson_num` BIGINT,
    `attend_5minute_zhibo_lesson_id_detail` STRING,
    `attend_and_playback_30minute_main_lesson_num_v1` BIGINT,
    `attend_finish_lesson_id_detail` STRING,
    `attend_finish_lesson_num` BIGINT,
    `attend_finish_main_lesson_num` BIGINT,
    `attend_finish_zhibo_lesson_id_detail` STRING,
    `attend_one_four_lesson_num` BIGINT,
    `attend_one_third_lesson_id_detail` STRING,
    `attend_one_third_lesson_num` BIGINT,
    `attend_two_third_lesson_id_detail` STRING,
    `attend_two_third_lesson_num` BIGINT,
    `cu_is_ai_attend` BIGINT,
    `cu_is_ai_finish` BIGINT,
    `cu_is_all_attend` BIGINT,
    `cu_is_all_finish` BIGINT,
    `cu_is_lbp_attend` BIGINT,
    `cu_is_lbp_attend_finish` BIGINT,
    `last_playback_time` BIGINT,
    `lbp_attend_count` BIGINT,
    `lbp_attend_lessonid_array` STRING,
    `lbp_finish_count` BIGINT,
    `lbp_finish_lessonid_array` STRING,
    `new_course_type` BIGINT,
    `no_attend_5minute_total_playback_5minute_lesson_id_detail` STRING,
    `no_attend_5minute_total_playback_5minute_lesson_num` BIGINT,
    `total_playback_5minute_lesson_id_detail` STRING,
    `total_playback_5minute_lesson_num` BIGINT,
    `total_playback_three_four_lesson_num` BIGINT,
    `total_unlock_playback_30minute_lesson_num` BIGINT,
    `total_unlock_playback_5minute_lesson_id_detail` STRING,
    `total_unlock_playback_5minute_lesson_num` BIGINT,
    `total_unlock_playback_lesson_num` BIGINT,
    `total_unlock_playback_three_four_lesson_id_detail` STRING,
    `total_unlock_playback_three_four_lesson_num` BIGINT,
    `unlock_7day_playback_30minute_lesson_num` BIGINT,
    `unlock_7day_playback_5minute_lesson_num` BIGINT,
    `unlock_7day_playback_three_four_lesson_num` BIGINT,
    `view_5minute_lesson_num` BIGINT,
    `view_finish_lesson_num` BIGINT,
    `view_finish_main_lesson_num_v1` BIGINT,
    `view_finish_total_playback_three_five_lesson_num_v1` BIGINT,
    `view_lesson_num` BIGINT,
    `year_season` STRING,
    `last_playback_timepoint` BIGINT,
    `last_playback_duration` BIGINT,
    `last_playback_lesson_id` BIGINT,
    `content_view_10s_lesson_num` BIGINT,
    `content_view_finish_85percent_lesson_num` BIGINT,
    `overall_finish_lesson_num` BIGINT,
    `is_generate_course_report` BIGINT,
    `inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num` BIGINT,
    `inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num` BIGINT,
    `mandatory_lbp_lesson_num` BIGINT,
    `zbk_update_time` BIGINT,
    `attend_or_content_view_5minute_main_lesson_num` BIGINT,
    `attend_or_content_view_three_four_main_lesson_num` BIGINT, 
    `zplusp_content_view_5min_jd_lesson_num_cu` BIGINT,
    `zplusp_content_view_5min_bd_lesson_num_cu` BIGINT,
    `zplusp_content_view_5min_dxjq_lesson_num_cu` BIGINT,
    PRIMARY KEY (
        `course_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_course_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_attendance_cu_common
SELECT
    `course_id`,
    `student_uid`,
    `ai_attend_finish_lesson_num`,
    `ai_attend_lesson_num`,
    `all_lesson_attend_duration_sum`,
    `all_lesson_duration_sum`,
    `all_lesson_playback_duration_sum`,
    `all_lesson_total_unlock_playback_time_sum`,
    `attend_30minute_lesson_num`,
    `attend_5minute_lesson_id_detail`,
    `attend_5minute_lesson_num`,
    `attend_5minute_main_lesson_num`,
    `attend_5minute_zhibo_lesson_id_detail`,
    `attend_and_playback_30minute_main_lesson_num_v1`,
    `attend_finish_lesson_id_detail`,
    `attend_finish_lesson_num`,
    `attend_finish_main_lesson_num`,
    `attend_finish_zhibo_lesson_id_detail`,
    `attend_one_four_lesson_num`,
    `attend_one_third_lesson_id_detail`,
    `attend_one_third_lesson_num`,
    `attend_two_third_lesson_id_detail`,
    `attend_two_third_lesson_num`,
    `cu_is_ai_attend`,
    `cu_is_ai_finish`,
    `cu_is_all_attend`,
    `cu_is_all_finish`,
    `cu_is_lbp_attend`,
    `cu_is_lbp_attend_finish`,
    `last_playback_time`,
    `lbp_attend_count`,
    `lbp_attend_lessonid_array`,
    `lbp_finish_count`,
    `lbp_finish_lessonid_array`,
    `new_course_type`,
    `no_attend_5minute_total_playback_5minute_lesson_id_detail`,
    `no_attend_5minute_total_playback_5minute_lesson_num`,
    `total_playback_5minute_lesson_id_detail`,
    `total_playback_5minute_lesson_num`,
    `total_playback_three_four_lesson_num`,
    `total_unlock_playback_30minute_lesson_num`,
    `total_unlock_playback_5minute_lesson_id_detail`,
    `total_unlock_playback_5minute_lesson_num`,
    `total_unlock_playback_lesson_num`,
    `total_unlock_playback_three_four_lesson_id_detail`,
    `total_unlock_playback_three_four_lesson_num`,
    `unlock_7day_playback_30minute_lesson_num`,
    `unlock_7day_playback_5minute_lesson_num`,
    `unlock_7day_playback_three_four_lesson_num`,
    `view_5minute_lesson_num`,
    `view_finish_lesson_num`,
    `view_finish_main_lesson_num_v1`,
    `view_finish_total_playback_three_five_lesson_num_v1`,
    `view_lesson_num`,
    `year_season`,
    `last_playback_timepoint`,
    `last_playback_duration`,
    `last_playback_lesson_id`,
    `content_view_10s_lesson_num`,
    `content_view_finish_85percent_lesson_num`,
    `overall_finish_lesson_num` ,
    `is_generate_course_report` ,
    `inclass_teacher_room_total_playback_content_time_ge_3min_mandatory_lbp_lesson_num` ,
    `inclass_teacher_room_total_playback_content_time_eq_lesson_video_duration_mandatory_lbp_lesson_num` ,
    `mandatory_lbp_lesson_num` ,
    UNIX_TIMESTAMP() AS `zbk_update_time`,
    `attend_or_content_view_5minute_main_lesson_num`,
    `attend_or_content_view_three_four_main_lesson_num`,
    `zplusp_content_view_5min_jd_lesson_num_cu`,
    `zplusp_content_view_5min_bd_lesson_num_cu` ,
    `zplusp_content_view_5min_dxjq_lesson_num_cu`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_attendance_cu_common
WHERE
    `hg_binlog_event_type` IN (5, 7);
