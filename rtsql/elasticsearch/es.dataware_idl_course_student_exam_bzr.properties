-- 声明用到的所有表
CREATE TABLE source_dwd_zbk_exam_student_exam (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `exam58_submit_cnt` BIGINT,
    `exam58_should_submit_cnt` BIGINT,
    `exam61_submit_cnt` BIGINT,
    `exam61_should_submit_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_cu_bzr',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_dwd_zbk_exam_student_exam (
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `exam58_submit_cnt` BIGINT,
    `exam58_should_submit_cnt` BIGINT,
    `exam61_submit_cnt` BIGINT,
    `exam61_should_submit_cnt` BIGINT,
     `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `course_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_course_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_dwd_zbk_exam_student_exam
SELECT
    `course_id`,
    `student_uid`,
    `exam58_submit_cnt`,
    `exam58_should_submit_cnt`,
    `exam61_submit_cnt`,
    `exam61_should_submit_cnt`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_dwd_zbk_exam_student_exam
WHERE
    `hg_binlog_event_type` IN (5, 7);