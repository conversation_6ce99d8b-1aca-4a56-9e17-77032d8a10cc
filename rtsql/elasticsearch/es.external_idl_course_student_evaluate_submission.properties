-- 工作台满意度问卷提交情况 https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=482581486
CREATE TABLE binlog_homework_zhibo_exam_tbl_evaluate_submission (
    BINLOG_NAME VARCHAR,
    <PERSON><PERSON><PERSON>OG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `G<PERSON><PERSON><PERSON>L_ID` VARCHAR,
    `NEW_VALUES` ROW<`id` STRING, `evaluate_id` STRING, `submit_id` STRING, `student_uid` STRING, `student_name` STRING, `source_id` STRING, `source_type` STRING, `dimension_key` STRING, `dimension_type` STRING, `update_time` STRING, `create_time` STRING>,
    `OLD_VALUES` ROW<`id` STRING, `evaluate_id` STRING, `submit_id` STRING, `student_uid` STRING, `student_name` STRING, `source_id` STRING, `source_type` STRING, `dimension_key` STRING, `dimension_type` STRING, `update_time` STRING, `create_time` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = 'binlog-homework_fudao-tblEvaluateSubmission-orderby-pk',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = '${kafka.consumer.groupid}',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_evaluate_submission (
    `id` BIGINT, -- 自增ID
    `evaluate_id` BIGINT, -- 问卷ID
    `submit_id` BIGINT, -- 提交ID，关联一次提交内的多个题目
    `student_uid` BIGINT, -- 提交用户ID，问卷无需登录时为0
    `source_id` BIGINT, -- 来源ID，课程业务对应课程ID
    `source_type` TINYINT, -- 业务类型, 1: 课程
    `dimension_key` STRING, -- 维度ID，老师维度对应老师ID
    `dimension_type` TINYINT, -- 维度类型, 1: 老师
    `create_time` INT, -- 创建时间
    `zbk_delete_flag` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `submit_id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'external_idl_course_student_evaluate_submission_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

CREATE TEMPORARY VIEW view_binlog_homework_zhibo_exam_tbl_evaluate_submission AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_homework_zhibo_exam_tbl_evaluate_submission;

CREATE TEMPORARY VIEW view_source_homework_zhibo_exam_tbl_evaluate_submission AS
SELECT
    CAST(binlog_data.`id` AS BIGINT) AS `id`,
    CAST(binlog_data.`evaluate_id` AS BIGINT) AS `evaluate_id`,
    CAST(binlog_data.`submit_id` AS BIGINT) AS `submit_id`,
    CAST(binlog_data.`student_uid` AS BIGINT) AS `student_uid`,
    CAST(binlog_data.`source_id` AS BIGINT) AS `source_id`,
    CAST(binlog_data.`source_type` AS TINYINT) AS `source_type`,
    binlog_data.`dimension_key` AS `dimension_key`,
    CAST(binlog_data.`dimension_type` AS TINYINT) AS `dimension_type`,
    CAST(binlog_data.`create_time` AS INT) AS `create_time`,
    zbk_delete_flag
FROM
    view_binlog_homework_zhibo_exam_tbl_evaluate_submission;

INSERT INTO
    es_tbl_evaluate_submission
SELECT
    `id`,
    `evaluate_id`,
    `submit_id`,
    `student_uid`,
    `source_id`,
    `source_type`,
    `dimension_key`,
    `dimension_type`,
    `create_time`,
    `zbk_delete_flag`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    view_source_homework_zhibo_exam_tbl_evaluate_submission
WHERE `student_uid` > 0; -- 仅同步登录后填写的问卷