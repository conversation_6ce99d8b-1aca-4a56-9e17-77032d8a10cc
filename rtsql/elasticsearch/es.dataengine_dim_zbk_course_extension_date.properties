-- 在线数据引擎用插班生状态配置表
CREATE TABLE source_zyb_zbk_bzr_dim_dim_zbk_course_extension_date (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `extension_date` BIGINT,
    `status` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dim.dim_zbk_course_extension_date',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dim_zbk_course_extension_date (
    `course_id` BIGINT,
    `extension_date` BIGINT,
    `status` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.dataengine.hosts}',
    'index' = 'dataengine_dim_zbk_course_extension_date_v1',
    'username' = '${es.dataengine.username}',
    'password' = '${es.dataengine.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dim_zbk_course_extension_date
SELECT
    `course_id`,
    `extension_date`,
    `status`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dim_dim_zbk_course_extension_date
WHERE
    `hg_binlog_event_type` IN (5, 7);