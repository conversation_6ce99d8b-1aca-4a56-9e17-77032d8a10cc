CREATE TABLE zyb_zbk_baseinfo_contract_student_bzr  (
    `hg_binlog_event_type` BIGINT,
    `contract_id` BIGINT,
    `student_uid` BIGINT,
    `unlock_lesson_num` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_baseinfo_contract_student_bzr',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_contract_student_agg_bzr (
    `contract_id` BIGINT,
    `student_uid` BIGINT,
    `unlock_lesson_num` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (contract_id,student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_contract_student_agg_bzr_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_contract_student_agg_bzr
SELECT
    `contract_id`,
    `student_uid`,
    `unlock_lesson_num`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_baseinfo_contract_student_bzr
WHERE
    hg_binlog_event_type IN(5, 7);