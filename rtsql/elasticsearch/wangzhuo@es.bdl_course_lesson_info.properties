-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_course_lesson_info (
    `course_id` BIGINT,
    `is_main` BIGINT,
    `lesson_id` BIGINT,
    `main_index` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_bdl_course_lesson_info',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_bdl_course_lesson_info (
    `course_id` BIGINT,
    `is_main` BIGINT,
    `lesson_id` BIGINT,
    `main_index` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `lesson_id`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_bdl_course_lesson_info_l1_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_bdl_course_lesson_info
SELECT
    `course_id`,
    `is_main`,
    `lesson_id`,
    `main_index`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_bdl_course_lesson_info;