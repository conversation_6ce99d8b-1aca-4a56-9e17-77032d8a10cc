CREATE TABLE `zyb_zbk_lpc_ads_base_ads_zbk_obj_model_result` (
    `user_id` BIGINT,
    `course_id` BIGINT,
    `lpc_uid` BIGINT,
    `date` BIGINT,
    `qici` STRING,
    `trans_score` DOUBLE,
    `trans_level` BIGINT,
    `wx_add_time` BIGINT,
    `mdc_time` BIGINT,
    `xzk_time` BIGINT,
    `inclass_time` BIGINT,
    `features` STRING,
    `zbk_update_time` BIGINT,
    `hg_binlog_event_type` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads_base.ads_zbk_obj_model_result',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE `dataware_idl_lpc_leas_ads_base_model` (
    `user_id` BIGINT,
    `course_id` BIGINT,
    `lpc_uid` BIGINT,
    `date` BIGINT,
    `qici` STRING,
    `trans_score` DOUBLE,
    `trans_level` BIGINT,
    `wx_add_time` BIGINT,
    `mdc_time` BIGINT,
    `xzk_time` BIGINT,
    `inclass_time` BIGINT,
    `features` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`user_id`, `course_id`, `date`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lpc_leas_ads_base_model_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    `dataware_idl_lpc_leas_ads_base_model`
SELECT
    `user_id`,
    `course_id`,
    `lpc_uid`,
    `date`,
    `qici`,
    `trans_score`,
    `trans_level`,
    `wx_add_time`,
    `mdc_time`,
    `xzk_time`,
    `inclass_time`,
    `features`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    `zyb_zbk_lpc_ads_base_ads_zbk_obj_model_result`
WHERE
    `hg_binlog_event_type` IN (5, 7);