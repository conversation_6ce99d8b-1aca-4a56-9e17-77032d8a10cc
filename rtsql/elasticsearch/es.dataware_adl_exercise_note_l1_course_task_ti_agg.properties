CREATE TABLE zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_ti_agg (
    `hg_binlog_event_type` BIGINT,
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `tid` BIGINT,
    `tid_idx` BIGINT,
    `total_question_stu_cnt` BIGINT,
    `total_repeat_wrong_stu_cnt` BIGINT,
    `origin_lesson_list` STRING,
    `origin_exam_type_list` STRING
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_question_course_task_ti_agg',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_adl_exercise_note_l1_course_task_ti_agg (
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `tid` BIGINT,
    `tid_idx` BIGINT,
    `total_question_stu_cnt` BIGINT,
    `total_repeat_wrong_stu_cnt` BIGINT,
    `origin_lesson_list` STRING,
    `origin_exam_type_list` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (assistant_uid, course_id, task_id, tid, tid_idx) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_exercise_note_l1_course_task_ti_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_adl_exercise_note_l1_course_task_ti_agg
SELECT
    `assistant_uid`,
    `course_id`,
    `task_id`,
    `tid`,
    `tid_idx`,
    `total_question_stu_cnt`,
    `total_repeat_wrong_stu_cnt`,
    `origin_lesson_list`,
    `origin_exam_type_list`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_ti_agg
WHERE
    hg_binlog_event_type IN(5, 7);
