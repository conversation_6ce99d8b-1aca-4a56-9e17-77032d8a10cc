CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_attendance_leads_lpc  (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `leads_id`  BIGINT,
    `business_uid` BIGINT,
    `student_uid` BIGINT,
    `enterprise_wechat_1v1_course_appearance_times` BIGINT,
    `enterprise_wechat_1v2_6_course_appearance_times` BIGINT,
    `enterprise_wechat_1v7_course_appearance_times` BIGINT,
    `enterprise_wechat_course_appearance_duration` BIGINT,
    `enterprise_wechat_1v1_course_appearance_duration` BIGINT,
    `enterprise_wechat_1v2_6_course_appearance_duration` BIGINT,
    `enterprise_wechat_1v7_course_appearance_duration` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_leads_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE es_dataware_idl_qw_lpc_course_student (
    `course_id` BIGINT,
    `leads_id`  BIGINT,
    `business_uid` BIGINT,
    `student_uid` BIGINT,
    `enterprise_wechat_1v1_course_appearance_times` BIGINT,
    `enterprise_wechat_1v2_6_course_appearance_times` BIGINT,
    `enterprise_wechat_1v7_course_appearance_times` BIGINT,
    `enterprise_wechat_course_appearance_duration` BIGINT,
    `enterprise_wechat_1v1_course_appearance_duration` BIGINT,
    `enterprise_wechat_1v2_6_course_appearance_duration` BIGINT,
    `enterprise_wechat_1v7_course_appearance_duration` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (leads_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_qw_lpc_course_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    es_dataware_idl_qw_lpc_course_student
SELECT
    `course_id` ,
    `leads_id`,
    `business_uid`,
    `student_uid` ,
    `enterprise_wechat_1v1_course_appearance_times` ,
    `enterprise_wechat_1v2_6_course_appearance_times` ,
    `enterprise_wechat_1v7_course_appearance_times` ,
    `enterprise_wechat_course_appearance_duration` ,
    `enterprise_wechat_1v1_course_appearance_duration` ,
    `enterprise_wechat_1v2_6_course_appearance_duration` ,
    `enterprise_wechat_1v7_course_appearance_duration` ,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_attendance_leads_lpc
WHERE
    hg_binlog_event_type IN(5, 7);