CREATE TABLE zyb_zbk_attendance_contract_lesson_assistant_bzr  (
    `hg_binlog_event_type` BIGINT,
    `contract_id` BIGINT,
    `assistant_uid` BIGINT,
    `lesson_id` BIGINT,
    `save_time` BIGINT,
    `content_view_10s_student_num`   BIGINT,
    `content_view_finish_85percent_student_num`   BIGINT,
    `overall_finish_student_num`   BIGINT,
    `attend_5minute_student_num`   BIGINT,
    `attend_finish_student_num`   BIGINT,
    `content_view_5minute_student_num`   BIGINT,
    `content_view_finish_three_four_student_num`   BIGINT,
    `submit_lesson_work_student_num`   BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_attendance_contract_lesson_assistant_bzr',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_attendance_contract_lesson_assistant_bzr (
   `contract_id` BIGINT,
   `assistant_uid` BIGINT,
   `lesson_id` BIGINT,
   `save_time` BIGINT,
   `content_view_10s_student_num`   BIGINT,
   `content_view_finish_85percent_student_num`   BIGINT,
   `overall_finish_student_num`   BIGINT,
   `attend_5minute_student_num`   BIGINT,
   `attend_finish_student_num`   BIGINT,
   `content_view_5minute_student_num`   BIGINT,
   `content_view_finish_three_four_student_num`   BIGINT,
   `submit_lesson_work_student_num`   BIGINT,
   `zbk_update_time` BIGINT,
    PRIMARY KEY (contract_id,assistant_uid,lesson_id,save_time) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_contract_lesson_assistant_agg_bzr_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_attendance_contract_lesson_assistant_bzr
SELECT
   `contract_id`,
   `assistant_uid`,
   `lesson_id`,
   `save_time`,
   `content_view_10s_student_num`,
   `content_view_finish_85percent_student_num`,
   `overall_finish_student_num`,
   `attend_5minute_student_num`,
   `attend_finish_student_num`,
   `content_view_5minute_student_num`,
   `content_view_finish_three_four_student_num`,
   `submit_lesson_work_student_num`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_attendance_contract_lesson_assistant_bzr
WHERE
    hg_binlog_event_type IN(5, 7);