CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_interact_lp_common
(
    `hg_binlog_event_type` BIGINT,
    `lesson_id`            BIGINT,
    `point_id`             BIGINT,
    `inclass_question_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_interact_lp_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_dws_dws_zbk_interact_lp_common
(
    `lesson_id`            BIGINT,
    `point_id`             BIGINT,
    `inclass_question_cnt` BIGINT,
    `zbk_update_time`      BIGINT,
    PRIMARY KEY (
                 `lesson_id`,
                 `point_id`
        ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_lesson_point_common_action_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO dest_zyb_zbk_bzr_dws_dws_zbk_interact_lp_common
SELECT `lesson_id`,
       `point_id`,
       `inclass_question_cnt`,
       UNIX_TIMESTAMP() AS `zbk_update_time`
FROM source_zyb_zbk_bzr_dws_dws_zbk_interact_lp_common
WHERE `hg_binlog_event_type` IN (5, 7);