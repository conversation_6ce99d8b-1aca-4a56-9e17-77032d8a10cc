CREATE TABLE zyb_zbk_deer_dws_dws_zbk_sop_user_login_deer (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `app_download_status` BIGINT,
    `pc_login_device_type` BIGINT,
    `pc_login_device_time` BIGINT,
    `pad_login_device_type` BIGINT,
    `pad_login_device_time` BIGINT,
    `phone_login_device_type` BIGINT,
    `phone_login_device_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_deer_dws.dws_zbk_sop_user_login_deer',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_dim_student_user_info (
    `student_uid` BIGINT,
    `app_download_status` BIGINT,
    `pc_login_device_type` BIGINT,
    `pc_login_device_time` BIGINT,
    `pad_login_device_type` BIGINT,
    `pad_login_device_time` BIGINT,
    `phone_login_device_type` BIGINT,
    `phone_login_device_time` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_dim_student_user_info_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_dim_student_user_info
SELECT
    `student_uid`,
    `app_download_status`,
    `pc_login_device_type`,
    `pc_login_device_time`,
    `pad_login_device_type`,
    `pad_login_device_time`,
    `phone_login_device_type`,
    `phone_login_device_time`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_deer_dws_dws_zbk_sop_user_login_deer
WHERE
    hg_binlog_event_type IN(5, 7);