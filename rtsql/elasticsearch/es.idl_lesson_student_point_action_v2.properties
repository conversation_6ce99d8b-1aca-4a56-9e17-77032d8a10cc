-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_interact_lup_common_v2
(
    `hg_binlog_event_type`     BIGINT,
    `lesson_id`                BIGINT,
    `student_uid`              BIGINT,
    `point_id`                 BIGINT,
    `inclass_question_cnt`     BIGINT,
    `inclass_right_cnt`        BIGINT,
    `inclass_participate_cnt`  BIGINT,
    `playback_right_cnt`       BIGINT,
    `playback_participate_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_interact_lup_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_dws_dws_zbk_interact_lup_common_v2
(
    `lesson_id`                BIGINT,
    `student_uid`              BIGINT,
    `point_id`                 BIGINT,
    `inclass_question_cnt`     BIGINT,
    `inclass_right_cnt`        BIGINT,
    `inclass_participate_cnt`  BIGINT,
    `playback_right_cnt`       BIGINT,
    `playback_participate_cnt` BIGINT,
    `zbk_update_time`          BIGINT,
    PRIMARY KEY (
                 `lesson_id`,
                 `student_uid`,
                 `point_id`
        ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_lesson_student_point_action_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO dest_zyb_zbk_bzr_dws_dws_zbk_interact_lup_common_v2
SELECT `lesson_id`,
       `student_uid`,
       `point_id`,
       `inclass_question_cnt`,
       `inclass_right_cnt`,
       `inclass_participate_cnt`,
       `playback_right_cnt`,
       `playback_participate_cnt`,
       UNIX_TIMESTAMP() AS `zbk_update_time`
FROM source_zyb_zbk_bzr_dws_dws_zbk_interact_lup_common_v2
WHERE `hg_binlog_event_type` IN (5, 7);