-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_assistantcourse_lt (
    `asscourse_attend_num` BIGINT,
    `asscourse_finish_num` BIGINT,
    `attend_total` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `teacher_id` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_adl_assistantcourse_lt',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_assistantcourse_lt (
    `asscourse_attend_num` BIGINT,
    `asscourse_finish_num` BIGINT,
    `attend_total` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `teacher_id` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id, lesson_id, teacher_id) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_assistantcourse_lt_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_adl_assistantcourse_lt
SELECT
    `asscourse_attend_num`,
    `asscourse_finish_num`,
    `attend_total`,
    `course_id`,
    `lesson_id`,
    `teacher_id`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_adl_assistantcourse_lt;