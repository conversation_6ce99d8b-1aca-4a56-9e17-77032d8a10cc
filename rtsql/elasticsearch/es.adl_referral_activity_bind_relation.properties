-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_bind_relation (
    `config_id` BIGINT,
    `inviter_uid` BIGINT,
    `invitee_num` BIGINT,
    `invitee_trans_num` BIGINT,
    `invitee_utrans_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_adl_referral_activity_bind_relation',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_bind_relation (
    `config_id` BIGINT,
    `inviter_uid` BIGINT,
    `invitee_num` BIGINT,
    `invitee_trans_num` BIGINT,
    `invitee_utrans_num` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (config_id, inviter_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'index' = 'adl_referral_activity_bind_relation_v1',
    'hosts' = '${es.workbench.hosts}',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_adl_referral_activity_bind_relation
SELECT
    `config_id`,
    `inviter_uid`,
    `invitee_num`,
    `invitee_trans_num`,
    `invitee_utrans_num`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_adl_referral_activity_bind_relation;