-- https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=380075141
-- https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=515043341
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_exam_rjyl_course_unit_student_agg (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `unit_id` BIGINT,
    `unit_right_num` BIGINT,
    `unit_stage_finish_num` BIGINT,
    `unit_submit_num` BIGINT,
    `total_need_revise_ti_cnt` BIGINT,
    `total_no_revise_ti_cnt` BIGINT,
    `total_revise_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_exam_rjyl_course_unit_student_agg',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_exam_rjyl_course_unit_student_agg (
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `unit_id` BIGINT,
    `unit_right_num` BIGINT,
    `unit_stage_finish_num` BIGINT,
    `unit_submit_num` BIGINT,
    `total_need_revise_ti_cnt` BIGINT,
    `total_no_revise_ti_cnt` BIGINT,
    `total_revise_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `unit_id`, `student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_exam_rjyl_course_unit_student_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_exam_rjyl_course_unit_student_agg
SELECT
    `course_id`,
    `student_uid`,
    `unit_id`,
    `unit_right_num`,
    `unit_stage_finish_num`,
    `unit_submit_num`,
    `total_need_revise_ti_cnt`,
    `total_no_revise_ti_cnt`,
    `total_revise_cnt`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_exam_rjyl_course_unit_student_agg
WHERE
    `hg_binlog_event_type` <> 3;