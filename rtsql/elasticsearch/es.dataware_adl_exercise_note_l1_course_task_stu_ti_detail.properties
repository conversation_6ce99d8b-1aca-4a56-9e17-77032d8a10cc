CREATE TABLE zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_stu_ti_detail (
    `hg_binlog_event_type` BIGINT,
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `tid` BIGINT,
    `tid_idx` BIGINT,
    `student_uid` BIGINT,
    `is_repeat_wrong` BIGINT,
    `origin_lesson_id` BIGINT,
    `origin_exam_type` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_question_course_task_stu_ti_detail',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE dataware_adl_exercise_note_l1_course_task_stu_ti_detail (
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `task_id` BIGINT,
    `tid` BIGINT,
    `tid_idx` BIGINT,
    `student_uid` BIGINT,
    `is_repeat_wrong` BIGINT,
    `origin_lesson_id` BIGINT,
    `origin_exam_type` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (assistant_uid, course_id, task_id, tid, tid_idx, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_exercise_note_l1_course_task_stu_ti_detail_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_adl_exercise_note_l1_course_task_stu_ti_detail
SELECT
    `assistant_uid`,
    `course_id`,
    `task_id`,
    `tid`,
    `tid_idx`,
    `student_uid`,
    `is_repeat_wrong`,
    `origin_lesson_id`,
    `origin_exam_type`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_bzr_dws_dws_zbk_exam_question_course_task_stu_ti_detail
WHERE
    hg_binlog_event_type IN(5, 7);
