-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_baseinfo_ca_agg_business_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `business_uid` BIGINT,
    `leads_cnt` BIGINT,
    `all_leads_cnt` BIGINT,
    `invalid_leads_cnt` BIGINT,
    `first_lesson_teacher` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_ca_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dest_zyb_zbk_bzr_baseinfo_ca_agg_business_lpc (
    `course_id` BIGINT,
    `business_uid` BIGINT,
    `leads_cnt` BIGINT,
    `leads_num` BIGINT,
    `all_leads_num` BIGINT,
    `invalid_leads_num` BIGINT,
    `first_lesson_teacher` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `course_id`,
        `business_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_lpc_business_course_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_baseinfo_ca_agg_business_lpc
SELECT
    `course_id`,
    `business_uid`,
    `leads_cnt`,
    `leads_cnt` AS `leads_num`,
    `all_leads_cnt` AS `all_leads_num`,
    `invalid_leads_cnt` AS `invalid_leads_num`,
    `first_lesson_teacher`,
     UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_baseinfo_ca_agg_business_lpc
WHERE
    `hg_binlog_event_type` <> 3;