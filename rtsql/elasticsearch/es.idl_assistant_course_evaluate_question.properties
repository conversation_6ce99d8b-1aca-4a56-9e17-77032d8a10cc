-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_sop_evaluate_question_common (
    `hg_binlog_event_type` BIGINT,
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `evaluate_id` BIGINT,
    `question_id` BIGINT,
    `question_type` BIGINT,
    `answers_dis` STRING
) WITH (
    'connector'='hologres',
    'endpoint'='${hologres.endpoint}:${hologres.port}',
    'username'='${hologres.username}',
    'password'='${hologres.password}',
    'dbname'='zyb_zbk_bzr',
    'tablename'='zyb_zbk_bzr_dws.dws_zbk_sop_evaluate_question_common',
    'cdcMode'='true',
    'binlogMaxRetryTimes'='10',
    'binlogRetryIntervalMs'='500',
    'binlogBatchReadSize'='100',
    'binlog'='true'
);

CREATE TABLE dest_zyb_zbk_bzr_dws_dws_zbk_sop_evaluate_question_common (
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `evaluate_id` BIGINT,
    `question_id` BIGINT,
    `question_type` BIGINT,
    `answers_dis` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`assistant_uid`, `course_id`, `evaluate_id`, `question_id`) NOT ENFORCED
) WITH (
    'connector'='elasticsearch-7',
    'hosts'='${es.workbench.hosts}',
    'index'='idl_assistant_course_evaluate_question_v1',
    'username'='${es.workbench.username}',
    'password'='${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_dws_dws_zbk_sop_evaluate_question_common
SELECT
    `assistant_uid`,
    `course_id`,
    `evaluate_id`,
    `question_id`,
    `question_type`,
    `answers_dis`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_sop_evaluate_question_common
WHERE
    `hg_binlog_event_type` <> 3;