-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_trade_student_season_agg (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `learn_year` BIGINT,
    `season` BIGINT,
    `earliest_trade_time` BIGINT,
    `grade_subject_enum` STRING
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_trade_student_season_agg',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_trade_student_season_agg (
    `student_uid` BIGINT,
    `learn_year` BIGINT,
    `season` BIGINT,
    `earliest_trade_time` BIGINT,
    `grade_subject_enum` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`student_uid`, `learn_year`, `season`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_trade_student_season_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_trade_student_season_agg
SELECT
    `student_uid`,
    `learn_year`,
    `season`,
    `earliest_trade_time`,
    `grade_subject_enum`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_trade_student_season_agg
WHERE
    `hg_binlog_event_type` <> 3;