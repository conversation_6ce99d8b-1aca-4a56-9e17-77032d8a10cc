-- 声明用到的所有表
CREATE TABLE zyb_zbk_deer_dws_dws_zbk_sop_device_connection_lu_deer (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `bluetooth_connect_status` BIGINT,
    `bluetooth_connect_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_deer_dws.dws_zbk_sop_device_connection_lu_deer',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);
CREATE TABLE dataware_idl_common_lesson_student (
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `bluetooth_connect_status` BIGINT,
    `bluetooth_connect_time` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (`lesson_id`,`student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_idl_common_lesson_student
SELECT
    `lesson_id`,
    `student_uid`,
    `bluetooth_connect_status`,
    `bluetooth_connect_time`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_deer_dws_dws_zbk_sop_device_connection_lu_deer
WHERE
    hg_binlog_event_type IN(5, 7);