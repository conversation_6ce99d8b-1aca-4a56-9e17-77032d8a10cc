-- ????????
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_sop_call_wechat_cua_day_common(
    `hg_binlog_event_type` BIGINT,
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `save_time` BIGINT,
    `all_num` BIGINT,
    `day_wechat_num` BIGINT,
    `access_num` BIGINT,
    `day_wechat_access_num` BIGINT,
    `day_touch_num` BIGINT,
    `day_wechat_touch_num` BIGINT,
    `day_3min_num` BIGINT,
    `day_3min_wechat_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_sop_call_wechat_cua_day_common',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

-- ?????course_id, student_uid, save_time???????course_id, student_uid?save_time?????????save_time?????
CREATE TABLE dest_zyb_zbk_bzr_dws_dws_zbk_sop_call_wechat_cua_day_common (
    `assistant_uid` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `save_time` BIGINT,
    `all_num` BIGINT,
    `day_wechat_num` BIGINT,
    `access_num` BIGINT,
    `day_wechat_access_num` BIGINT,
    `day_touch_num` BIGINT,
    `day_wechat_touch_num` BIGINT,
    `day_3min_num` BIGINT,
    `day_3min_wechat_num` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `assistant_uid`, `student_uid`, `save_time`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_assistant_call_wechat_cua_day',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_dws_dws_zbk_sop_call_wechat_cua_day_common
SELECT
    `assistant_uid`,
    `course_id`,
    `student_uid`,
    `save_time`,
    `all_num`,
    `day_wechat_num`,
    `access_num`,
    `day_wechat_access_num`,
    `day_touch_num`,
    `day_wechat_touch_num`,
    `day_3min_num`,
    `day_3min_wechat_num`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_sop_call_wechat_cua_day_common
WHERE
    `hg_binlog_event_type` <> 3;