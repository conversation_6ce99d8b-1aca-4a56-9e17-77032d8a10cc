CREATE TABLE zyb_zbk_base_contract_assistant_bzr  (
    `hg_binlog_event_type` BIGINT,
    `contract_id` BIGINT,
    `assistant_uid` BIGINT,
    `reg_num` BIGINT,
    `save_time` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_baseinfo_contract_assistant_agg_bzr',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_contract_assistant_agg_bzr_for_base (
    `contract_id` BIGINT,
    `assistant_uid` BIGINT,
    `reg_num` BIGINT,
    `save_time` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (contract_id,assistant_uid,save_time) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_contract_assistant_agg_bzr_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_contract_assistant_agg_bzr_for_base
SELECT
    `contract_id`,
    `assistant_uid`,
    `reg_num`,
    `save_time`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_base_contract_assistant_bzr
WHERE
    hg_binlog_event_type IN(5, 7);