-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dim_dim_zbk_dim_user (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `brand_recent_name` STRING,
    `brand_usual_name` STRING,
    `city_level` STRING,
    `city_name` STRING,
    `mc_recent` STRING,
    `os_usual` STRING,
    `province_name` STRING,
    `deer_user_type` STRING
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dim.dim_zbk_dim_user',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dim_zbk_dim_user (
    `student_uid` BIGINT,
    `brand_recent_name` STRING,
    `brand_usual_name` STRING,
    `city_level` STRING,
    `city_name` STRING,
    `mc_recent` STRING,
    `os_usual` STRING,
    `province_name` STRING,
    `deer_user_type` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`student_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_dim_lpc_zbk_dim_user_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dim_zbk_dim_user
SELECT
    `student_uid`,
    `brand_recent_name`,
    `brand_usual_name`,
    `city_level`,
    `city_name`,
    `mc_recent`,
    `os_usual`,
    `province_name`,
    `deer_user_type`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dim_dim_zbk_dim_user
WHERE
    `hg_binlog_event_type` <> 3;