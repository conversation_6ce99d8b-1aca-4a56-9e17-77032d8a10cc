-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_baseinfo_cu_common (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `unlock_lesson_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_baseinfo_cu_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_baseinfo_cu_common (
    `course_id` BIGINT,
    `student_uid` BIGINT,
    `unlock_lesson_num` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `course_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_course_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_baseinfo_cu_common
SELECT
    `course_id`,
    `student_uid`,
    `unlock_lesson_num`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_baseinfo_cu_common
WHERE
    `hg_binlog_event_type` IN (5, 7);