-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_attendance_ca_agg_business_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `business_uid` BIGINT,
    `generate_course_report_student_num` BIGINT,
    `attend_leads_num` BIGINT,
    `playback_leads_num` BIGINT,
    `finish_leads_num` BIGINT,
    `add_wechat_attend_leads_num` BIGINT,
    `no_attend_playback_leads_num` BIGINT,
    `attend_cnt_detail` VARCHAR,
    `attend_finish_cnt_detail` VARCHAR,
    `no_attend_playback_finish_leads_num` BIGINT,
    `playback_action_leads_num` BIGINT,
    `playback_callout_leads_num` BIGINT,
    `unlock_playback_attend_leads_num` BIGINT,
    `unlock_playback_finish_leads_num` BIGINT,
    `all_attend_leads_num` BIGINT,
    `all_attend_finish_leads_num` BIGINT,
    `ai_attend_leads_num` BIGINT,
    `ai_attend_finish_leads_num` BIGINT,
    `lbp_attend_leads_num` BIGINT,
    `lbp_attend_finish_leads_num` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_attendance_ca_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE dest_zyb_zbk_bzr_attendance_ca_agg_business_lpc (
    `course_id` BIGINT,
    `business_uid` BIGINT,
    `generate_course_report_student_num` BIGINT,
    attend_num BIGINT,
    playback_num BIGINT,
    finish_num BIGINT,
    add_wechat_attend_num BIGINT,
    not_attend_playback_num BIGINT,
    attend_n_lesson_num VARCHAR,
    finish_n_lesson_num VARCHAR,
    finish_playback_num BIGINT,
    playback_action_num BIGINT,
    playback_action_call_num BIGINT,
    unlock_playback_attend_num BIGINT,
    unlock_playback_finish_num BIGINT,
    all_attend_num BIGINT,
    all_attend_finish_num BIGINT,
    ai_attend_num BIGINT,
    ai_attend_finish_num BIGINT,
    lbp_attend_num BIGINT,
    lbp_finish_num BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `course_id`,
        `business_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_lpc_business_course_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_attendance_ca_agg_business_lpc
SELECT
    `course_id`,
    `business_uid`,
    `generate_course_report_student_num`,
    attend_leads_num AS attend_num,
    playback_leads_num AS playback_num,
    finish_leads_num AS finish_num,
    add_wechat_attend_leads_num AS add_wechat_attend_num,
    no_attend_playback_leads_num AS not_attend_playback_num,
    attend_cnt_detail AS attend_n_lesson_num,
    attend_finish_cnt_detail AS finish_n_lesson_num,
    no_attend_playback_finish_leads_num AS finish_playback_num,
    playback_action_leads_num AS playback_action_num,
    playback_callout_leads_num AS playback_action_call_num,
    unlock_playback_attend_leads_num AS unlock_playback_attend_num,
    unlock_playback_finish_leads_num AS unlock_playback_finish_num,
    all_attend_leads_num AS all_attend_num,
    all_attend_finish_leads_num AS all_attend_finish_num,
    ai_attend_leads_num AS ai_attend_num,
    ai_attend_finish_leads_num AS ai_attend_finish_num,
    lbp_attend_leads_num AS lbp_attend_num,
    lbp_attend_finish_leads_num AS lbp_finish_num,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_attendance_ca_agg_business_lpc
WHERE
    `hg_binlog_event_type` <> 3;