-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_all_action_v1 (
    `hg_binlog_event_type` BIGINT,
    `all_index` BIGINT,
    `assistant_first_access_d_value` BIGINT,
    `assistant_first_access_time` BIGINT,
    `assistant_last_leave_time` BIGINT,
    `assistant_postclass_live_duration` BIGINT,
    `assistant_preclass_live_duration` BIGINT,
    `assistant_uid` BIGINT,
    `brand_id` BIGINT,
    `course_assistant_bind_detail` STRING,
    `course_id` BIGINT,
    `course_name` STRING,
    `course_type` BIGINT,
    `courseware_upload_time` BIGINT,
    `deleted` BIGINT,
    `grade_period` BIGINT,
    `is_assistant_postclass_delay` BIGINT,
    `is_assistant_preclass_late` BIGINT,
    `is_assistant_preclass_redgift` BIGINT,
    `is_courseware_upload_timeout` BIGINT,
    `is_inner` BIGINT,
    `is_main` BIGINT,
    `learn_season` BIGINT,
    `lesson_assistant_status` BIGINT,
    `lesson_deleted` BIGINT,
    `lesson_id` BIGINT,
    `lesson_name` STRING,
    `lesson_start_time` BIGINT,
    `lesson_stop_time` BIGINT,
    `main_grade` BIGINT,
    `main_index` BIGINT,
    `main_subject` BIGINT,
    `restart_id` BIGINT,
    `upload_status` BIGINT,
    `year` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_assistant_lesson_all_action_v1',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_all_action_v1 (
    `all_index` BIGINT,
    `assistant_first_access_d_value` BIGINT,
    `assistant_first_access_time` BIGINT,
    `assistant_last_leave_time` BIGINT,
    `assistant_postclass_live_duration` BIGINT,
    `assistant_preclass_live_duration` BIGINT,
    `assistant_uid` BIGINT,
    `brand_id` BIGINT,
    `course_assistant_bind_detail` STRING,
    `course_id` BIGINT,
    `course_name` STRING,
    `course_type` BIGINT,
    `courseware_upload_time` BIGINT,
    `deleted` BIGINT,
    `grade_period` BIGINT,
    `is_assistant_postclass_delay` BIGINT,
    `is_assistant_preclass_late` BIGINT,
    `is_assistant_preclass_redgift` BIGINT,
    `is_courseware_upload_timeout` BIGINT,
    `is_inner` BIGINT,
    `is_main` BIGINT,
    `learn_season` BIGINT,
    `lesson_assistant_status` BIGINT,
    `lesson_deleted` BIGINT,
    `lesson_id` BIGINT,
    `lesson_name` STRING,
    `lesson_start_time` BIGINT,
    `lesson_stop_time` BIGINT,
    `main_grade` BIGINT,
    `main_index` BIGINT,
    `main_subject` BIGINT,
    `restart_id` BIGINT,
    `upload_status` BIGINT,
    `year` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`lesson_id`, `assistant_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_assistant_lesson_all_action_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_assistant_lesson_all_action_v1
SELECT
    `all_index`,
    `assistant_first_access_d_value`,
    `assistant_first_access_time`,
    `assistant_last_leave_time`,
    `assistant_postclass_live_duration`,
    `assistant_preclass_live_duration`,
    `assistant_uid`,
    `brand_id`,
    `course_assistant_bind_detail`,
    `course_id`,
    `course_name`,
    `course_type`,
    `courseware_upload_time`,
    `deleted`,
    `grade_period`,
    `is_assistant_postclass_delay`,
    COALESCE(`is_assistant_preclass_late`, 0) AS `is_assistant_preclass_late`,
    `is_assistant_preclass_redgift`,
    `is_courseware_upload_timeout`,
    `is_inner`,
    `is_main`,
    `learn_season`,
    `lesson_assistant_status`,
    `lesson_deleted`,
    `lesson_id`,
    `lesson_name`,
    COALESCE(`lesson_start_time`, 0) AS `lesson_start_time`,
    `lesson_stop_time`,
    `main_grade`,
    `main_index`,
    `main_subject`,
    `restart_id`,
    `upload_status`,
    `year`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_assistant_lesson_all_action_v1
WHERE
    `hg_binlog_event_type` <> 3;