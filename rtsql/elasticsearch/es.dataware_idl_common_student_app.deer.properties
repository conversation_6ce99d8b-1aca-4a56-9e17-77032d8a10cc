-- 学生在各APP的设备等指标
CREATE TABLE source_zyb_zbk_deer_dws_dws_zbk_sop_user_app_login_deer (
    `hg_binlog_event_type` BIGINT,
    `app_type` BIGINT,
    `student_uid` BIGINT,
    `app_download_status` BIGINT,
    `login_device_time` BIGINT,    
    `pad_login_device_time` BIGINT,
    `pad_login_device_type` BIGINT,
    `pc_login_device_time` BIGINT,
    `pc_login_device_type` BIGINT,
    `phone_login_device_time` BIGINT,
    `phone_login_device_type` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_deer_dws.dws_zbk_sop_user_app_login_deer',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_sop_user_app_login_deer (
    `app_type` BIGINT,
    `student_uid` BIGINT,
    `app_download_status` BIGINT,
    `login_device_time` BIGINT,
    `pad_login_device_time` BIGINT,
    `pad_login_device_type` BIGINT,
    `pc_login_device_time` BIGINT,
    `pc_login_device_type` BIGINT,
    `phone_login_device_time` BIGINT,
    `phone_login_device_type` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`student_uid`, `app_type`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_student_app_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_sop_user_app_login_deer
SELECT
    `app_type`,
    `student_uid`,
    `app_download_status`,
    `login_device_time`,
    `pad_login_device_time`,
    `pad_login_device_type`,
    `pc_login_device_time`,
    `pc_login_device_type`,
    `phone_login_device_time`,
    `phone_login_device_type`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_deer_dws_dws_zbk_sop_user_app_login_deer
WHERE
    `hg_binlog_event_type` IN (5, 7);