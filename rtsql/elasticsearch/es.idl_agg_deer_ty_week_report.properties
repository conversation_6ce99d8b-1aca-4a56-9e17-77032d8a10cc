CREATE TABLE zyb_zbk_outer_workstation_deer_sport_report_ca_byweek (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `assistant_uid`  BIGINT,
    `week` BIGINT,
    `sport_week_report_generate_num` BIGINT,
    `sport_week_report_total` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_outer.workstation_deer_sport_report_ca_byweek',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE es_idl_agg_deer_ty_week_report (
    `course_id` BIGINT,
    `assistant_uid`  BIGINT,
    `week` BIGINT,
    `sport_week_report_generate_num` BIGINT,
    `sport_week_report_total` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id,week,assistant_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_agg_deer_ty_week_report_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    es_idl_agg_deer_ty_week_report
SELECT
    `course_id` ,
    `assistant_uid`,
    `week`,
    `sport_week_report_generate_num` ,
    `sport_week_report_total` ,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_outer_workstation_deer_sport_report_ca_byweek
WHERE
    hg_binlog_event_type IN(5, 7);