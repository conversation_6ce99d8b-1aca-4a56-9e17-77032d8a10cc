-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_assignclass_coursebind_studentcount (
    `course_id` BIGINT,
    `season_year` BIGINT,
    `season` BIGINT,
    `season_num` BIGINT,
    `course_grade` BIGINT,
    `course_subject` BIGINT,
    `course_type` BIGINT,
    `pull_new_duty` BIGINT,
    `course_is_inner` BIGINT,
    `person_uid` BIGINT,
    `assistant_uid` BIGINT,
    `new_cnt` BIGINT,
    `all_cnt` BIGINT,
    `continue_cnt` BIGINT,
    `continue_error_cnt` BIGINT,
    `deleted` BIGINT,
    `no_transfer_new_cnt` BIGINT,
    `nomal_transfer_new_cnt` BIGINT,
    `delay_transfer_new_cnt` BIGINT,
    `single_new_cnt` BIGINT,
    `series_new_cnt` BIGINT,
    `old_cnt` BIGINT,
    `no_transfer_old_cnt` BIGINT,
    `normal_transfer_old_cnt` BIGINT,
    `delay_transfer_old_cnt` BIGINT,
    `single_old_cnt` BIGINT,
    `series_old_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_tbl_assignclass_coursebind_studentcount',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_assignclass_coursebind_studentcount (
    `course_id` BIGINT,
    `season_year` BIGINT,
    `season` BIGINT,
    `season_num` BIGINT,
    `course_grade` BIGINT,
    `course_subject` BIGINT,
    `course_type` BIGINT,
    `pull_new_duty` BIGINT,
    `course_is_inner` BIGINT,
    `person_uid` BIGINT,
    `assistant_uid` BIGINT,
    `new_cnt` BIGINT,
    `all_cnt` BIGINT,
    `continue_cnt` BIGINT,
    `continue_error_cnt` BIGINT,
    `deleted` BIGINT,
    `no_transfer_new_cnt` BIGINT,
    `nomal_transfer_new_cnt` BIGINT,
    `delay_transfer_new_cnt` BIGINT,
    `single_new_cnt` BIGINT,
    `series_new_cnt` BIGINT,
    `old_cnt` BIGINT,
    `no_transfer_old_cnt` BIGINT,
    `normal_transfer_old_cnt` BIGINT,
    `delay_transfer_old_cnt` BIGINT,
    `single_old_cnt` BIGINT,
    `series_old_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id, assistant_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'index' = 'adl_assignclass_coursebind_studentcount_v1',
    'hosts' = '${es.workbench.hosts}',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_adl_assignclass_coursebind_studentcount
SELECT
    `course_id`,
    `season_year`,
    `season`,
    `season_num`,
    `course_grade`,
    `course_subject`,
    `course_type`,
    `pull_new_duty`,
    `course_is_inner`,
    `person_uid`,
    `assistant_uid`,
    `new_cnt`,
    `all_cnt`,
    `continue_cnt`,
    `continue_error_cnt`,
    `deleted`,
    `no_transfer_new_cnt`,
    `nomal_transfer_new_cnt`,
    `delay_transfer_new_cnt`,
    `single_new_cnt`,
    `series_new_cnt`,
    `old_cnt`,
    `no_transfer_old_cnt`,
    `normal_transfer_old_cnt`,
    `delay_transfer_old_cnt`,
    `single_old_cnt`,
    `series_old_cnt`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_adl_assignclass_coursebind_studentcount;