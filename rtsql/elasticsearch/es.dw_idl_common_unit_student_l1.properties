CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_attendance_eu_common (
    `hg_binlog_event_type` BIGINT,
    `unit_id` BIGINT,
    `student_uid` BIGINT,
    `zplusp_content_view_5min_jd_lesson_num_eu` BIGINT,
    `zplusp_content_view_5min_bd_lesson_num_eu` BIGINT,
    `zplusp_content_view_5min_dxjq_lesson_num_eu` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_attendance_eu_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_dws_dws_zbk_attendance_eu_common (
    `unit_id` BIGINT,
    `student_uid` BIGINT,
    `zplusp_content_view_5min_jd_lesson_num_eu` BIGINT,
    `zplusp_content_view_5min_bd_lesson_num_eu` BIGINT,
    `zplusp_content_view_5min_dxjq_lesson_num_eu` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `unit_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dw_idl_common_unit_student_l1_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_dws_dws_zbk_attendance_eu_common
SELECT
    `unit_id`,
    `student_uid`,
    `zplusp_content_view_5min_jd_lesson_num_eu`,
    `zplusp_content_view_5min_bd_lesson_num_eu`,
    `zplusp_content_view_5min_dxjq_lesson_num_eu`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_attendance_eu_common
WHERE
    `hg_binlog_event_type` IN (5, 7);
