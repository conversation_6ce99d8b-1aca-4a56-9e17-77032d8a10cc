CREATE TABLE zyb_zbk_deer_dws_dws_zbk_exam_lesson_student_tid_deer (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `tid` BIGINT,
    `interact_id` BIGINT,
    `code_upload_success_cnt` BIGINT,
    `code_load_success_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_deer_dws.dws_zbk_exam_lesson_student_tid_deer',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_deer_interact_ti_l1 (
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `tid` BIGINT,
    `interact_id` BIGINT,
    `code_upload_success_cnt` BIGINT,
    `code_load_success_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (student_uid, course_id, lesson_id, tid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_deer_interact_ti_l1_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_deer_interact_ti_l1
SELECT
    `student_uid`,
    `course_id`,
    `lesson_id`,
    `tid`,
    `interact_id`,
    `code_upload_success_cnt`,
    `code_load_success_cnt`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_deer_dws_dws_zbk_exam_lesson_student_tid_deer
WHERE
    hg_binlog_event_type IN(5, 7);
