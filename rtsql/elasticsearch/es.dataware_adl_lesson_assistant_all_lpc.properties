-- 声明用到的所有表
CREATE TABLE zyb_zbk_lpc_ads_base_ads_lesson_assistant_all_lpc (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `business_uid` BIGINT,
    `reg_leads_num` BIGINT,
    `content_view_finish_three_four_student_num` BIGINT,
    `content_view_5minute_student_num` BIGINT,
    `content_view_10s_student_num` BIGINT,
    `content_view_finish_85percent_student_num` BIGINT,
    `overall_finish_student_num` BIGINT,
    `attend_leads_num` BIGINT,
    `attend_finish_leads_num` BIGINT,
    `submit_lesson_work_student_num` BIGINT,
    `generate_lesson_report_student_num` BIGINT,
    `bluetooth_before_class_connect_suc_cnt` BIGINT,
    `bluetooth_during_class_connect_suc_cnt` BIGINT,
    `sensor_before_class_check_suc_cnt` BIGINT,
    `sensor_during_class_check_suc_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_ads_base.ads_lesson_assistant_all_lpc',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);


CREATE TABLE dataware_adl_lpc_business_lesson_data_v1 (
   `course_id` BIGINT,
   `lesson_id` BIGINT,
   `business_uid` BIGINT,
   `reg_leads_num` BIGINT,
   `content_view_finish_three_four_student_num` BIGINT,
   `content_view_5minute_student_num` BIGINT,
   `content_view_10s_student_num` BIGINT,
   `content_view_finish_85percent_student_num` BIGINT,
   `overall_finish_student_num` BIGINT,
   `attend_leads_num` BIGINT,
   `attend_finish_leads_num` BIGINT,
   `submit_lesson_work_student_num` BIGINT,
   `generate_lesson_report_student_num` BIGINT,
   `bluetooth_before_class_connect_suc_cnt` BIGINT,
   `bluetooth_during_class_connect_suc_cnt` BIGINT,
   `sensor_before_class_check_suc_cnt` BIGINT,
   `sensor_during_class_check_suc_cnt` BIGINT,
   `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`,`lesson_id`,`business_uid`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_adl_lpc_business_lesson_data_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    dataware_adl_lpc_business_lesson_data_v1
SELECT
    `course_id`,
    `lesson_id`,
    `business_uid`,
    `reg_leads_num`,
    `content_view_finish_three_four_student_num`,
    `content_view_5minute_student_num`,
    `content_view_10s_student_num`,
    `content_view_finish_85percent_student_num`,
    `overall_finish_student_num`,
    `attend_leads_num`,
    `attend_finish_leads_num`,
    `submit_lesson_work_student_num`,
    `generate_lesson_report_student_num`,
    `bluetooth_before_class_connect_suc_cnt`,
    `bluetooth_during_class_connect_suc_cnt`,
    `sensor_before_class_check_suc_cnt`,
    `sensor_during_class_check_suc_cnt`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_ads_base_ads_lesson_assistant_all_lpc
WHERE
    hg_binlog_event_type IN(5, 7);