CREATE TABLE zyb_zbk_bzr_dim_dim_zbk_dim_deer_user (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `deer_programming_hardware_bind_status` BIGINT,
    `xbr1_programming_hardware_bind_status` BIGINT,
    `xz_programming_hardware_bind_status` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dim.dim_zbk_dim_deer_user',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_dim_student_user_info (
    `student_uid` BIGINT,
    `deer_programming_hardware_bind_status` BIGINT,
    `xbr1_programming_hardware_bind_status` BIGINT,
    `xz_programming_hardware_bind_status` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_dim_student_user_info_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_dim_student_user_info
SELECT
    `student_uid`,
    `deer_programming_hardware_bind_status`,
    `xbr1_programming_hardware_bind_status`,
    `xz_programming_hardware_bind_status`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_bzr_dim_dim_zbk_dim_deer_user
WHERE
    hg_binlog_event_type IN(5, 7);
