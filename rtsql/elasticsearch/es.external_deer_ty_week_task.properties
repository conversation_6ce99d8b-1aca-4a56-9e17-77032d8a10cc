CREATE TABLE binlog_homework_deer_ty_tblweektask_0 (
    BINLOG_NAME VARCHAR,
    BINLOG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `GL<PERSON><PERSON>L_ID` VARCHAR,
    `NEW_VALUES` ROW<`id` STRING, `course_id` STRING, `lesson_ids` STRING, `week` STRING, `status` STRING, `create_time` STRING, `update_time` STRING>,
    `OLD_VALUES` ROW<`id` STRING, `course_id` STRING, `lesson_ids` STRING, `week` STRING, `status` STRING, `create_time` STRING, `update_time` STRING>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = '757_deer_ty_tblReportTask',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = '${kafka.consumer.groupid}',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_deer_ty_tblweektask_0 (
    id BIGINT COMMENT 'id',
    course_id BIGINT COMMENT '课程id',
    lesson_ids STRING comment '章节id列表',
    week BIGINT COMMENT '周',
    status BIGINT COMMENT '生成状态 1已生成 2未生成',
    create_time BIGINT COMMENT '创建时间',
    update_time BIGINT COMMENT '更新时间',
    zbk_update_time BIGINT COMMENT '时间',
    PRIMARY KEY (
        `id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = 'http://************:9200',
    'index' = 'deer_ty_report_task_v1',
    'username' = 'workbench_app',
    'password' = 'RI_WLDQktvrViCzGsopAF'
);

CREATE TEMPORARY VIEW view_binlog_homework_deer_ty_tblweektask_0 AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_homework_deer_ty_tblweektask_0;

CREATE TEMPORARY VIEW view_source_homework_deer_ty_tblweektask_0 AS
SELECT
    CAST(binlog_data.`id` AS BIGINT) AS `id`,
    CAST(binlog_data.`course_id` AS BIGINT) AS `course_id`,
    binlog_data.`lesson_ids` AS `lesson_ids`,
    CAST(binlog_data.`week` AS BIGINT) AS `week`,
    CAST(binlog_data.`status` AS BIGINT) AS `status`,
    CAST(binlog_data.`create_time` AS INT) AS `create_time`,
    CAST(binlog_data.`update_time` AS INT) AS `update_time`,
    zbk_delete_flag
FROM
    view_binlog_homework_deer_ty_tblweektask_0;

INSERT INTO
    es_tbl_deer_ty_tblweektask_0
SELECT
    id,
    course_id,
    lesson_ids,
    week,
    status,
    create_time,
    update_time,
    unix_timestamp() AS zbk_update_time
FROM
    view_source_homework_deer_ty_tblweektask_0;