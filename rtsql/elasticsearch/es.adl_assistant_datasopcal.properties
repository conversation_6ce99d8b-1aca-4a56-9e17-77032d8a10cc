-- 声明用到的所有表
CREATE TABLE binlog_ads_base_ads_zbk_tbldatasopcal (
    `hg_binlog_event_type` BIGINT,
    `after_unlock_7d_attend_playback_num` BIGINT,
    `after_unlock_7d_finish_playback_num` BIGINT,
    `after_unlock_attend_playback_num` BIGINT,
    `after_unlock_finish_playback_num` BIGINT,
    `after_unlock_need_attend_num` BIGINT,
    `after_unlock_playback_num` BIGINT,
    `ai_attend_num` BIGINT,
    `ai_attend_total` BIGINT,
    `ai_class_finish_num` BIGINT,
    `all_time_homework_submit_num` BIGINT,
    `all_view_num` BIGINT,
    `assistant_uid` <PERSON><PERSON>IN<PERSON>,
    `assistantcourse_attend_finish_num` BIGINT,
    `assistantcourse_attend_num` BIGINT,
    `assistantcourse_need_attend_num` BIGINT,
    `attend_num_5m` BIGINT,
    `attend_num_quarter` BIGINT,
    `attend_num` BIGINT,
    `attend_one_third_num` BIGINT,
    `attend_total` BIGINT,
    `attend_two_third_num` BIGINT,
    `challenge_attend_right_num` BIGINT,
    `challenge_attend_total_num` BIGINT,
    `challenge_finish_num` BIGINT,
    `challenge_total_num` BIGINT,
    `chat_num` BIGINT,
    `class_finish_num` BIGINT,
    `corrections_homework_num48` BIGINT,
    `corrections_homework_num72` BIGINT,
    `course_id` BIGINT,
    `exam33_similar_question_amend_num` BIGINT,
    `exam33_similar_question_submit_num` BIGINT,
    `exam33_submit_and_unamend_num` BIGINT,
    `exam33_wrong_similar_expound_video_view_num` BIGINT,
    `exam7_submit_and_unamend_num` BIGINT,
    `exam7_wrong_expound_video_view_num` BIGINT,
    `homework_SorA_num` BIGINT,
    `homework_amend_num_in_14d` BIGINT,
    `homework_amend_num` BIGINT,
    `homework_num` BIGINT,
    `homework_recorrection_num` BIGINT,
    `homework_submit_3_times_or_amend_num` BIGINT,
    `homework_tid_first_correct_cnt` BIGINT,
    `homework_tid_first_right_cnt` BIGINT,
    `interactive_participate_num` BIGINT,
    `interactive_right_num` BIGINT,
    `interactive_total_num` BIGINT,
    `is_follow_a` BIGINT,
    `is_follow` BIGINT,
    `lesson_finish_num` BIGINT,
    `lesson_id` BIGINT,
    `lesson_test_finish_num` BIGINT,
    `lesson_test_participate_num` BIGINT,
    `lesson_test_right_num` BIGINT,
    `lesson_test_total_num` BIGINT,
    `lesson_total_num` BIGINT,
    `niudao_finish_num` BIGINT,
    `niudao_total_num` BIGINT,
    `playback_all_num` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `postclass_attend_num` BIGINT,
    `postclass_finish_attend_num` BIGINT,
    `pre_attend_is5_num` BIGINT,
    `pre_attend_num` BIGINT,
    `preclass_attend_num` BIGINT,
    `preclass_finish_attend_num` BIGINT,
    `preview_bef_fin_num` BIGINT,
    `preview_finish_num` BIGINT,
    `reg_num` BIGINT,
    `save_time` BIGINT,
    `stage_test_finish_num` BIGINT,
    `transfer_status` BIGINT,
    `user_type` STRING,
    `view_finish_num_in_14d` BIGINT,
    `view_finish_num` BIGINT,
    `view_num_5m` BIGINT,
    `view_num_quarter` BIGINT,
    `view_num` BIGINT,
    `lbp_attend_num` BIGINT,
    `lbp_finish_num` BIGINT,
    `view_attend_num` BIGINT,
    `view_finish_num_v1` BIGINT,
    `view_attend_in_14d_num` BIGINT,
    `view_finish_in_14d_num_v1` BIGINT,
    `day14_playback_three_five_num_v1` BIGINT,
    `total_playback_three_four_num_v1` BIGINT,
    `view_finish_total_playback_three_five_num_v1` BIGINT,
    `total_playback_three_five_num_v1` BIGINT,
    `homework_s_timeless_cnt` BIGINT,
    `homework_sora_timeless_cnt` BIGINT,
    `mix_live_interactive_total_num` BIGINT,
    `mix_live_interactive_participate_num` BIGINT,
    `mix_live_interactive_right_num` BIGINT,
    `mix_view_interactive_total_num` BIGINT,
    `mix_view_interactive_participate_num` BIGINT,
    `mix_view_interactive_right_num` BIGINT,
    `live_interactive_total_num` BIGINT,
    `live_interactive_participate_num` BIGINT,
    `live_interactive_right_num` BIGINT,
    `view_interactive_total_num` BIGINT,
    `view_interactive_participate_num` BIGINT,
    `view_interactive_right_num` BIGINT,
    `homework_timeless_correct_cnt` BIGINT,
    `deer_programming_submit_num` BIGINT,
    `deer_programming_correct_num` BIGINT,
    `day7_view_30minute_student_num_v1` BIGINT,
    `day7_view_finish_three_four_student_num_v1` BIGINT,
    `content_view_30minute_student_num` BIGINT,
    `content_view_finish_three_four_student_num` BIGINT,
    `content_view_5minute_student_num` BIGINT,
    `current_assistant_view_attend_num` BIGINT,
    `current_assistant_view_finish_total_playback_three_five_num_v1` BIGINT,
    `current_assistant_content_view_finish_three_four_student_num` BIGINT,
    `current_homework_s_timeless_submit_cnt` BIGINT,
    `current_homework_total_submit_cnt` BIGINT,
    `ai_consolidation_exercise_submission_cnt` BIGINT,
    `ai_report_generation_cnt` BIGINT,
    `live_consolidation_exercise_submission_cnt` BIGINT,
    `live_report_generation_cnt` BIGINT,
    `sphd_consolidation_exercise_submission_cnt` BIGINT,
    `sphd_report_generation_cnt` BIGINT,
    `ide_stu_total_cnt` BIGINT,
    `ide_stu_finish_cnt` BIGINT,
    `exam58_submit_cnt` BIGINT,
    `exam61_submit_cnt` BIGINT,
    `precise_timeless_submit_cnt` BIGINT,
    `precise_submit_cnt` BIGINT,
    `exam62_timeless_submit_cnt` BIGINT,
    `exam62_timeless_correct_cnt` BIGINT
) with (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_tbldatasopcal',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '500',
    'binlog' = 'true',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE holo_tbldatasopcal (
    `after_unlock_7d_attend_playback_num` BIGINT,
    `after_unlock_7d_finish_playback_num` BIGINT,
    `after_unlock_attend_playback_num` BIGINT,
    `after_unlock_finish_playback_num` BIGINT,
    `after_unlock_need_attend_num` BIGINT,
    `after_unlock_playback_num` BIGINT,
    `ai_attend_num` BIGINT,
    `ai_attend_total` BIGINT,
    `ai_class_finish_num` BIGINT,
    `all_time_homework_submit_num` BIGINT,
    `all_view_num` BIGINT,
    `assistant_uid` BIGINT,
    `assistantcourse_attend_finish_num` BIGINT,
    `assistantcourse_attend_num` BIGINT,
    `assistantcourse_need_attend_num` BIGINT,
    `attend_num_5m` BIGINT,
    `attend_num_quarter` BIGINT,
    `attend_num` BIGINT,
    `attend_one_third_num` BIGINT,
    `attend_total` BIGINT,
    `attend_two_third_num` BIGINT,
    `challenge_attend_right_num` BIGINT,
    `challenge_attend_total_num` BIGINT,
    `challenge_finish_num` BIGINT,
    `challenge_total_num` BIGINT,
    `chat_num` BIGINT,
    `class_finish_num` BIGINT,
    `corrections_homework_num48` BIGINT,
    `corrections_homework_num72` BIGINT,
    `course_id` BIGINT,
    `exam33_similar_question_amend_num` BIGINT,
    `exam33_similar_question_submit_num` BIGINT,
    `exam33_submit_and_unamend_num` BIGINT,
    `exam33_wrong_similar_expound_video_view_num` BIGINT,
    `exam7_submit_and_unamend_num` BIGINT,
    `exam7_wrong_expound_video_view_num` BIGINT,
    `homework_SorA_num` BIGINT,
    `homework_amend_num_in_14d` BIGINT,
    `homework_amend_num` BIGINT,
    `homework_num` BIGINT,
    `homework_recorrection_num` BIGINT,
    `homework_submit_3_times_or_amend_num` BIGINT,
    `homework_tid_first_correct_cnt` BIGINT,
    `homework_tid_first_right_cnt` BIGINT,
    `interactive_participate_num` BIGINT,
    `interactive_right_num` BIGINT,
    `interactive_total_num` BIGINT,
    `is_follow_a` BIGINT,
    `is_follow` BIGINT,
    `lesson_finish_num` BIGINT,
    `lesson_id` BIGINT,
    `lesson_test_finish_num` BIGINT,
    `lesson_test_participate_num` BIGINT,
    `lesson_test_right_num` BIGINT,
    `lesson_test_total_num` BIGINT,
    `lesson_total_num` BIGINT,
    `niudao_finish_num` BIGINT,
    `niudao_total_num` BIGINT,
    `playback_all_num` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `postclass_attend_num` BIGINT,
    `postclass_finish_attend_num` BIGINT,
    `pre_attend_is5_num` BIGINT,
    `pre_attend_num` BIGINT,
    `preclass_attend_num` BIGINT,
    `preclass_finish_attend_num` BIGINT,
    `preview_bef_fin_num` BIGINT,
    `preview_finish_num` BIGINT,
    `reg_num` BIGINT,
    `save_time` BIGINT,
    `stage_test_finish_num` BIGINT,
    `transfer_status` BIGINT,
    `user_type` STRING,
    `view_finish_num_in_14d` BIGINT,
    `view_finish_num` BIGINT,
    `view_num_5m` BIGINT,
    `view_num_quarter` BIGINT,
    `view_num` BIGINT,
    `lbp_attend_num` BIGINT,
    `lbp_finish_num` BIGINT,
    `view_attend_num` BIGINT,
    `view_finish_num_v1` BIGINT,
    `view_attend_in_14d_num` BIGINT,
    `view_finish_in_14d_num_v1` BIGINT,
    `day14_playback_three_five_num_v1` BIGINT,
    `total_playback_three_four_num_v1` BIGINT,
    `view_finish_total_playback_three_five_num_v1` BIGINT,
    `total_playback_three_five_num_v1` BIGINT,
    `homework_s_timeless_cnt` BIGINT,
    `homework_sora_timeless_cnt` BIGINT,
    `mix_live_interactive_total_num` BIGINT,
    `mix_live_interactive_participate_num` BIGINT,
    `mix_live_interactive_right_num` BIGINT,
    `mix_view_interactive_total_num` BIGINT,
    `mix_view_interactive_participate_num` BIGINT,
    `mix_view_interactive_right_num` BIGINT,
    `live_interactive_total_num` BIGINT,
    `live_interactive_participate_num` BIGINT,
    `live_interactive_right_num` BIGINT,
    `view_interactive_total_num` BIGINT,
    `view_interactive_participate_num` BIGINT,
    `view_interactive_right_num` BIGINT,
    `homework_timeless_correct_cnt` BIGINT,
    `deer_programming_submit_num` BIGINT,
    `deer_programming_correct_num` BIGINT,
    `day7_view_30minute_student_num_v1` BIGINT,
    `day7_view_finish_three_four_student_num_v1` BIGINT,
    `content_view_30minute_student_num` BIGINT,
    `content_view_finish_three_four_student_num` BIGINT,
    `content_view_5minute_student_num` BIGINT,
    `current_assistant_view_attend_num` BIGINT,
    `current_assistant_view_finish_total_playback_three_five_num_v1` BIGINT,
    `current_assistant_content_view_finish_three_four_student_num` BIGINT,
    `current_homework_s_timeless_submit_cnt` BIGINT,
    `current_homework_total_submit_cnt` BIGINT,
    `ai_consolidation_exercise_submission_cnt` BIGINT,
    `ai_report_generation_cnt` BIGINT,
    `live_consolidation_exercise_submission_cnt` BIGINT,
    `live_report_generation_cnt` BIGINT,
    `sphd_consolidation_exercise_submission_cnt` BIGINT,
    `sphd_report_generation_cnt` BIGINT,
    `ide_stu_total_cnt` BIGINT,
    `ide_stu_finish_cnt` BIGINT,
    `exam58_submit_cnt` BIGINT,
    `exam61_submit_cnt` BIGINT,
    `precise_timeless_submit_cnt` BIGINT,
    `precise_submit_cnt` BIGINT,
    `exam62_timeless_submit_cnt` BIGINT,
    `exam62_timeless_correct_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `lesson_id`,
        `assistant_uid`,
        `transfer_status`,
        `user_type`,
        `save_time`
    ) NOT ENFORCED
) with (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_assistant_datasopcal_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

-- 字段顺序及数据类型和目标表里的保持一致
INSERT INTO
    holo_tbldatasopcal
SELECT
    `after_unlock_7d_attend_playback_num`,
    `after_unlock_7d_finish_playback_num`,
    `after_unlock_attend_playback_num`,
    `after_unlock_finish_playback_num`,
    `after_unlock_need_attend_num`,
    `after_unlock_playback_num`,
    `ai_attend_num`,
    `ai_attend_total`,
    `ai_class_finish_num`,
    `all_time_homework_submit_num`,
    `all_view_num`,
    `assistant_uid`,
    `assistantcourse_attend_finish_num`,
    `assistantcourse_attend_num`,
    `assistantcourse_need_attend_num`,
    `attend_num_5m`,
    `attend_num_quarter`,
    `attend_num`,
    `attend_one_third_num`,
    `attend_total`,
    `attend_two_third_num`,
    `challenge_attend_right_num`,
    `challenge_attend_total_num`,
    `challenge_finish_num`,
    `challenge_total_num`,
    `chat_num`,
    `class_finish_num`,
    `corrections_homework_num48`,
    `corrections_homework_num72`,
    COALESCE(`course_id`, 0) AS `course_id`,
    `exam33_similar_question_amend_num`,
    `exam33_similar_question_submit_num`,
    `exam33_submit_and_unamend_num`,
    `exam33_wrong_similar_expound_video_view_num`,
    `exam7_submit_and_unamend_num`,
    `exam7_wrong_expound_video_view_num`,
    `homework_SorA_num`,
    `homework_amend_num_in_14d`,
    `homework_amend_num`,
    `homework_num`,
    `homework_recorrection_num`,
    `homework_submit_3_times_or_amend_num`,
    `homework_tid_first_correct_cnt`,
    `homework_tid_first_right_cnt`,
    `interactive_participate_num`,
    `interactive_right_num`,
    `interactive_total_num`,
    `is_follow_a`,
    `is_follow`,
    `lesson_finish_num`,
    `lesson_id`,
    `lesson_test_finish_num`,
    `lesson_test_participate_num`,
    `lesson_test_right_num`,
    `lesson_test_total_num`,
    `lesson_total_num`,
    `niudao_finish_num`,
    `niudao_total_num`,
    `playback_all_num`,
    `playback_participate_num`,
    `playback_right_num`,
    `postclass_attend_num`,
    `postclass_finish_attend_num`,
    `pre_attend_is5_num`,
    `pre_attend_num`,
    `preclass_attend_num`,
    `preclass_finish_attend_num`,
    `preview_bef_fin_num`,
    `preview_finish_num`,
    `reg_num`,
    `save_time`,
    `stage_test_finish_num`,
    `transfer_status`,
    `user_type`,
    `view_finish_num_in_14d`,
    `view_finish_num`,
    `view_num_5m`,
    `view_num_quarter`,
    `view_num`,
    `lbp_attend_num`,
    `lbp_finish_num`,
    `view_attend_num`,
    `view_finish_num_v1`,
    `view_attend_in_14d_num`,
    `view_finish_in_14d_num_v1`,
    `day14_playback_three_five_num_v1`,
    `total_playback_three_four_num_v1`,
    `view_finish_total_playback_three_five_num_v1`,
    `total_playback_three_five_num_v1`,
    `homework_s_timeless_cnt`,
    `homework_sora_timeless_cnt`,
    `mix_live_interactive_total_num`,
    `mix_live_interactive_participate_num`,
    `mix_live_interactive_right_num`,
    `mix_view_interactive_total_num`,
    `mix_view_interactive_participate_num`,
    `mix_view_interactive_right_num`,
    `live_interactive_total_num`,
    `live_interactive_participate_num`,
    `live_interactive_right_num`,
    `view_interactive_total_num`,
    `view_interactive_participate_num`,
    `view_interactive_right_num`,
    `homework_timeless_correct_cnt`,
    `deer_programming_submit_num`,
    `deer_programming_correct_num`,
    `day7_view_30minute_student_num_v1`,
    `day7_view_finish_three_four_student_num_v1`,
    `content_view_30minute_student_num`,
    `content_view_finish_three_four_student_num`,
    `content_view_5minute_student_num`,
    `current_assistant_view_attend_num`,
    `current_assistant_view_finish_total_playback_three_five_num_v1`,
    `current_assistant_content_view_finish_three_four_student_num`,
    `current_homework_s_timeless_submit_cnt`,
    `current_homework_total_submit_cnt`,
    `ai_consolidation_exercise_submission_cnt`,
    `ai_report_generation_cnt`,
    `live_consolidation_exercise_submission_cnt`,
    `live_report_generation_cnt`,
    `sphd_consolidation_exercise_submission_cnt`,
    `sphd_report_generation_cnt`,
    `ide_stu_total_cnt`,
    `ide_stu_finish_cnt`,
    `exam58_submit_cnt` ,
    `exam61_submit_cnt` ,
    `precise_timeless_submit_cnt`,
    `precise_submit_cnt`,
    `exam62_timeless_submit_cnt`,
    `exam62_timeless_correct_cnt`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    binlog_ads_base_ads_zbk_tbldatasopcal
WHERE
    `hg_binlog_event_type` <> 3
    AND `course_id` IS NOT NULL;