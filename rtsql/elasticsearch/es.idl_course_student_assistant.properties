-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_student_assistant (
    `hg_binlog_event_type` BIGINT,
    `is_transfer_student_typeB` BIGINT,
    `after_test_participate_num` INT,
    `after_test_right_num` INT,
    `assistant_uid` BIGINT,
    `attend_count` BIGINT,
    `attend_count_5m` BIGINT,
    `attend_count_quarter` BIGINT,
    `attend_type_label` STRING,
    `course_id` BIGINT,
    `course_name` STRING,
    `course_type` BIGINT,
    `department` BIGINT,
    `effective_wechat_num` BIGINT,
    `exam32_total_submit_num` BIGINT,
    `finish_num` BIGINT,
    `has_auto_enterprise_wechat` BIGINT,
    `has_backinterview` BIGINT,
    `has_backinterview_tag` BIGINT,
    `has_bangbang_tag` BIGINT,
    `has_interview` BIGIN<PERSON>,
    `has_manual_enterprise_wechat` BIGINT,
    `has_manual_wechat` BIGINT,
    `has_retain_tag` BIGINT,
    `has_tag` BIGINT,
    `has_wechat` BIGINT,
    `homework_submit_count` B<PERSON>INT,
    `interview_type` BIGINT,
    `is_after_class_phone_back_interview` BIGINT,
    `is_after_class_phone_back_interview_1min` INT,
    `is_backinterview_by_phone` BIGINT,
    `is_interview_by_phone` BIGINT,
    `is_interview_call_3min` BIGINT,
    `is_interview_finish` BIGINT,
    `is_phone_access` BIGINT,
    `is_phone_cover` BIGINT,
    `is_refund` BIGINT,
    `is_test_complete` BIGINT,
    `is_transfer_student` BIGINT,
    `is_transfer_student_delayed` BIGINT,
    `is_wechat_bind` BIGINT,
    `last_backinterview_time` BIGINT,
    `m_grade` BIGINT,
    `m_subject` BIGINT,
    `new_user_type` STRING,
    `phone_backinterview_count` BIGINT,
    `playback_cnt_attend_after_unlock_7d` BIGINT,
    `playback_cnt_finish_after_unlock_7d` BIGINT,
    `pre_continue` BIGINT,
    `pre_finish_num` BIGINT,
    `refund_time` BIGINT,
    `service_survey_detail` STRING,
    `start_time` BIGINT,
    `stop_time` BIGINT,
    `student_uid` BIGINT,
    `test_complete_time` BIGINT,
    `trade_id` BIGINT,
    `trade_time` BIGINT,
    `view_count_5m` BIGINT,
    `wechat_cnt` BIGINT,
    `wechat_reply_cnt` BIGINT,
    `wechat_time` BIGINT,
    `lbp_attend_count` BIGINT,
    `lbp_finish_count` BIGINT,
    `level_transfer` STRING,
    `view_finish_total_playback_three_five_lesson_num_v1` BIGINT,
    `leap_user_type` STRING,
    `view_finish_day14_playback_three_five_lesson_num_v1` BIGINT,
    `latest_exam_level` BIGINT,
    `latest_exam_status` BIGINT,
    `latest_is_down_nts` BIGINT,
    `latest_exam_is_qualified` BIGINT,
    `latest_exam_nts_id` STRING,
    `latest_exam_time` BIGINT,
    `latest_time_interval` STRING,
    `is_reserve_phone_access` BIGINT,
    `is_reserve_phone_cover` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_course_student_assistant',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_student_assistant (
    `is_transfer_student_typeB` BIGINT,
    `after_test_participate_num` INT,
    `after_test_right_num` INT,
    `assistant_uid` BIGINT,
    `attend_count` BIGINT,
    `attend_count_5m` BIGINT,
    `attend_count_quarter` BIGINT,
    `attend_type_label` STRING,
    `course_id` BIGINT,
    `course_name` STRING,
    `course_type` BIGINT,
    `department` BIGINT,
    `effective_wechat_num` BIGINT,
    `exam32_total_submit_num` BIGINT,
    `finish_num` BIGINT,
    `has_auto_enterprise_wechat` BIGINT,
    `has_backinterview` BIGINT,
    `has_backinterview_tag` BIGINT,
    `has_bangbang_tag` BIGINT,
    `has_interview` BIGINT,
    `has_manual_enterprise_wechat` BIGINT,
    `has_manual_wechat` BIGINT,
    `has_retain_tag` BIGINT,
    `has_tag` BIGINT,
    `has_wechat` BIGINT,
    `homework_submit_count` BIGINT,
    `interview_type` BIGINT,
    `is_after_class_phone_back_interview` BIGINT,
    `is_after_class_phone_back_interview_1min` INT,
    `is_backinterview_by_phone` BIGINT,
    `is_interview_by_phone` BIGINT,
    `is_interview_call_3min` BIGINT,
    `is_interview_finish` BIGINT,
    `is_phone_access` BIGINT,
    `is_phone_cover` BIGINT,
    `is_refund` BIGINT,
    `is_test_complete` BIGINT,
    `is_transfer_student` BIGINT,
    `is_transfer_student_delayed` BIGINT,
    `is_wechat_bind` BIGINT,
    `last_backinterview_time` BIGINT,
    `m_grade` BIGINT,
    `m_subject` BIGINT,
    `new_user_type` STRING,
    `phone_backinterview_count` BIGINT,
    `playback_cnt_attend_after_unlock_7d` BIGINT,
    `playback_cnt_finish_after_unlock_7d` BIGINT,
    `pre_continue` BIGINT,
    `pre_finish_num` BIGINT,
    `refund_time` BIGINT,
    `service_survey_detail` STRING,
    `start_time` BIGINT,
    `stop_time` BIGINT,
    `student_uid` BIGINT,
    `test_complete_time` BIGINT,
    `trade_id` BIGINT,
    `trade_time` BIGINT,
    `view_count_5m` BIGINT,
    `wechat_cnt` BIGINT,
    `wechat_reply_cnt` BIGINT,
    `wechat_time` BIGINT,
    `zbk_update_time` INT,
    `lbp_attend_count` BIGINT,
    `lbp_finish_count` BIGINT,
    `level_transfer` STRING,
    `view_finish_total_playback_three_five_lesson_num_v1` BIGINT,
    `leap_user_type` STRING,
    `view_finish_day14_playback_three_five_lesson_num_v1` BIGINT,
    `latest_exam_level` BIGINT,
    `latest_exam_status` BIGINT,
    `latest_is_down_nts` BIGINT,
    `latest_exam_is_qualified` BIGINT,
    `latest_exam_nts_id` STRING,
    `latest_exam_time` BIGINT,
    `latest_time_interval` STRING,
    `is_reserve_phone_access` BIGINT,
    `is_reserve_phone_cover` BIGINT,
    PRIMARY KEY (course_id, student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_course_student_assistant_v2',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_course_student_assistant
SELECT
    `is_transfer_student_typeB`,
    `after_test_participate_num`,
    `after_test_right_num`,
    COALESCE(`assistant_uid`, 0) AS `assistant_uid`,
    `attend_count`,
    `attend_count_5m`,
    `attend_count_quarter`,
    `attend_type_label`,
    `course_id`,
    `course_name`,
    `course_type`,
    `department`,
    `effective_wechat_num`,
    `exam32_total_submit_num`,
    `finish_num`,
    `has_auto_enterprise_wechat`,
    `has_backinterview`,
    `has_backinterview_tag`,
    `has_bangbang_tag`,
    `has_interview`,
    `has_manual_enterprise_wechat`,
    `has_manual_wechat`,
    `has_retain_tag`,
    `has_tag`,
    `has_wechat`,
    `homework_submit_count`,
    `interview_type`,
    `is_after_class_phone_back_interview`,
    `is_after_class_phone_back_interview_1min`,
    `is_backinterview_by_phone`,
    `is_interview_by_phone`,
    `is_interview_call_3min`,
    `is_interview_finish`,
    `is_phone_access`,
    `is_phone_cover`,
    `is_refund`,
    `is_test_complete`,
    `is_transfer_student`,
    `is_transfer_student_delayed`,
    `is_wechat_bind`,
    `last_backinterview_time`,
    `m_grade`,
    `m_subject`,
    `new_user_type`,
    `phone_backinterview_count`,
    `playback_cnt_attend_after_unlock_7d`,
    `playback_cnt_finish_after_unlock_7d`,
    `pre_continue`,
    `pre_finish_num`,
    `refund_time`,
    `service_survey_detail`,
    `start_time`,
    `stop_time`,
    `student_uid`,
    `test_complete_time`,
    `trade_id`,
    `trade_time`,
    `view_count_5m`,
    `wechat_cnt`,
    `wechat_reply_cnt`,
    `wechat_time`,
    CAST(UNIX_TIMESTAMP() AS INT) AS `zbk_update_time`,
    `lbp_attend_count`,
    `lbp_finish_count`,
    `level_transfer`,
    `view_finish_total_playback_three_five_lesson_num_v1`,
    `leap_user_type`,
    `view_finish_day14_playback_three_five_lesson_num_v1`,
    `latest_exam_level`,
    `latest_exam_status`,
    `latest_is_down_nts`,
    `latest_exam_is_qualified`,
    `latest_exam_nts_id`,
    `latest_exam_time`,
    `latest_time_interval`,
    `is_reserve_phone_access`,
    `is_reserve_phone_cover`
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_course_student_assistant
WHERE
    `hg_binlog_event_type` <> 3;