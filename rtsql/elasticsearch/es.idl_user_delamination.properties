-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_user_delamination_v1 (
    `hg_binlog_event_type` BIGINT,
    `always_test_exam_finish_num` BIGINT,
    `always_test_exam_right_num` BIGINT,
    `always_test_exam_total_num` BIGINT,
    `assistant_uid` <PERSON>IGIN<PERSON>,
    `attend_finish_lesson_num` BIGINT,
    `attend_long_lesson_num` BIGINT,
    `challenge_finish_num` BIGINT,
    `challenge_total_num` BIGINT,
    `city_level` BIGINT,
    `class_type` STRING,
    `consolidation_exam_finish_num` BIGINT,
    `consolidation_exam_right_num` BIGINT,
    `consolidation_exam_total_num` BIGINT,
    `course_id` BIGINT,
    `course_name` STRING,
    `grade_id` BIGINT,
    `group_user_message_sent_total_num` BIGINT,
    `interactive_exam_finish_num` BIGINT,
    `interactive_exam_right_num` BIGINT,
    `interactive_exam_total_num` BIGINT,
    `is_bound_discount` BIGINT,
    `is_origin_student` BIGINT,
    `is_preclass` BIGINT,
    `is_valid_student` BIGINT,
    `learn_season` STRING,
    `learn_year` STRING,
    `lesson1_finish_num` BIGINT,
    `lesson1_total_num` BIGINT,
    `lesson_transfer_type` BIGINT,
    `need_attend_lesson_num` BIGINT,
    `new_user_type` STRING,
    `next_expansion_course_num` BIGINT,
    `playback_all_num` BIGINT,
    `playback_chapters_absent_num` BIGINT,
    `playback_chapters_attend_num` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `preclass_attend_num` BIGINT,
    `preclass_finish_attend_lesson_num` BIGINT,
    `preview_exam_finish_num` BIGINT,
    `preview_exam_right_num` BIGINT,
    `preview_exam_total_num` BIGINT,
    `province_name` STRING,
    `recent_7_days_tianying_valid_chat_num` BIGINT,
    `same_type_pv` BIGINT,
    `same_type_rank` BIGINT,
    `save_time` STRING,
    `score` STRING,
    `serise_season` BIGINT,
    `student_uid` BIGINT,
    `subject_id` BIGINT,
    `user_active_valid_session_count` BIGINT,
    `user_delamination_continue_level` STRING,
    `user_delamination_continue_level_2nd` STRING,
    `user_second_source` STRING,
    `valid_session_total_num` BIGINT,
    `wechat_cnt` BIGINT,
    `wechat_reply_cnt` BIGINT,
    `attend_long_lesson_num_timeless` BIGINT,
    `attend_finish_lesson_num_timeless` BIGINT,
    `total_voice_chat_num` BIGINT,
    `assistantcourse_attend_num` BIGINT,
    `assistantcourse_attend_finish_num` BIGINT,
    `assistantcourse_need_attend_num` BIGINT,
    `sent_total_num` BIGINT,
    `user_sent_total_num` BIGINT,
    `subject_num` BIGINT,
    `attend_long_lesson_num_14d` BIGINT,
    `attend_finish_lesson_num_14d` BIGINT,
    `total_voice_chat_duration` BIGINT,
    `consolidation_exam_s_num` BIGINT,
    `homework_tid_first_right_cnt` BIGINT,
    `homework_tid_first_correct_cnt` BIGINT,
    `subject_num_semester` BIGINT,
    `leap_user_type` STRING
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_user_delamination_v1',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_user_delamination_v1 (
    `always_test_exam_finish_num` BIGINT,
    `always_test_exam_right_num` BIGINT,
    `always_test_exam_total_num` BIGINT,
    `assistant_uid` BIGINT,
    `attend_finish_lesson_num` BIGINT,
    `attend_long_lesson_num` BIGINT,
    `challenge_finish_num` BIGINT,
    `challenge_total_num` BIGINT,
    `city_level` BIGINT,
    `class_type` STRING,
    `consolidation_exam_finish_num` BIGINT,
    `consolidation_exam_right_num` BIGINT,
    `consolidation_exam_total_num` BIGINT,
    `course_id` BIGINT,
    `course_name` STRING,
    `grade_id` BIGINT,
    `group_user_message_sent_total_num` BIGINT,
    `interactive_exam_finish_num` BIGINT,
    `interactive_exam_right_num` BIGINT,
    `interactive_exam_total_num` BIGINT,
    `is_bound_discount` BIGINT,
    `is_origin_student` BIGINT,
    `is_preclass` BIGINT,
    `is_valid_student` BIGINT,
    `learn_season` STRING,
    `learn_year` STRING,
    `lesson1_finish_num` BIGINT,
    `lesson1_total_num` BIGINT,
    `lesson_transfer_type` BIGINT,
    `need_attend_lesson_num` BIGINT,
    `new_user_type` STRING,
    `next_expansion_course_num` BIGINT,
    `playback_all_num` BIGINT,
    `playback_chapters_absent_num` BIGINT,
    `playback_chapters_attend_num` BIGINT,
    `playback_participate_num` BIGINT,
    `playback_right_num` BIGINT,
    `preclass_attend_num` BIGINT,
    `preclass_finish_attend_lesson_num` BIGINT,
    `preview_exam_finish_num` BIGINT,
    `preview_exam_right_num` BIGINT,
    `preview_exam_total_num` BIGINT,
    `province_name` STRING,
    `recent_7_days_tianying_valid_chat_num` BIGINT,
    `same_type_pv` BIGINT,
    `same_type_rank` BIGINT,
    `save_time` STRING,
    `score` STRING,
    `serise_season` BIGINT,
    `student_uid` BIGINT,
    `subject_id` BIGINT,
    `user_active_valid_session_count` BIGINT,
    `user_delamination_continue_level` STRING,
    `user_delamination_continue_level_2nd` STRING,
    `user_second_source` STRING,
    `valid_session_total_num` BIGINT,
    `wechat_cnt` BIGINT,
    `wechat_reply_cnt` BIGINT,
    `attend_long_lesson_num_timeless` BIGINT,
    `attend_finish_lesson_num_timeless` BIGINT,
    `total_voice_chat_num` BIGINT,
    `assistantcourse_attend_num` BIGINT,
    `assistantcourse_attend_finish_num` BIGINT,
    `assistantcourse_need_attend_num` BIGINT,
    `sent_total_num` BIGINT,
    `user_sent_total_num` BIGINT,
    `subject_num` BIGINT,
    `attend_long_lesson_num_14d` BIGINT,
    `attend_finish_lesson_num_14d` BIGINT,
    `total_voice_chat_duration` BIGINT,
    `consolidation_exam_s_num` BIGINT,
    `homework_tid_first_right_cnt` BIGINT,
    `homework_tid_first_correct_cnt` BIGINT,
    `subject_num_semester` BIGINT,
    `leap_user_type` STRING,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `student_uid`, `save_time`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_user_delamination_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_user_delamination_v1
SELECT
    `always_test_exam_finish_num`,
    `always_test_exam_right_num`,
    `always_test_exam_total_num`,
    COALESCE(`assistant_uid`, 0) AS `assistant_uid`,
    `attend_finish_lesson_num`,
    `attend_long_lesson_num`,
    `challenge_finish_num`,
    `challenge_total_num`,
    `city_level`,
    `class_type`,
    `consolidation_exam_finish_num`,
    `consolidation_exam_right_num`,
    `consolidation_exam_total_num`,
    `course_id`,
    `course_name`,
    `grade_id`,
    `group_user_message_sent_total_num`,
    `interactive_exam_finish_num`,
    `interactive_exam_right_num`,
    `interactive_exam_total_num`,
    `is_bound_discount`,
    `is_origin_student`,
    `is_preclass`,
    COALESCE(`is_valid_student`, 0) AS `is_valid_student`,
    `learn_season`,
    `learn_year`,
    `lesson1_finish_num`,
    `lesson1_total_num`,
    `lesson_transfer_type`,
    `need_attend_lesson_num`,
    `new_user_type`,
    `next_expansion_course_num`,
    `playback_all_num`,
    `playback_chapters_absent_num`,
    `playback_chapters_attend_num`,
    `playback_participate_num`,
    `playback_right_num`,
    `preclass_attend_num`,
    `preclass_finish_attend_lesson_num`,
    `preview_exam_finish_num`,
    `preview_exam_right_num`,
    `preview_exam_total_num`,
    `province_name`,
    `recent_7_days_tianying_valid_chat_num`,
    `same_type_pv`,
    `same_type_rank`,
    `save_time`,
    `score`,
    `serise_season`,
    `student_uid`,
    `subject_id`,
    `user_active_valid_session_count`,
    `user_delamination_continue_level`,
    `user_delamination_continue_level_2nd`,
    `user_second_source`,
    `valid_session_total_num`,
    `wechat_cnt`,
    `wechat_reply_cnt`,
    `attend_long_lesson_num_timeless`,
    `attend_finish_lesson_num_timeless`,
    `total_voice_chat_num`,
    `assistantcourse_attend_num`,
    `assistantcourse_attend_finish_num`,
    `assistantcourse_need_attend_num`,
    `sent_total_num`,
    `user_sent_total_num`,
    `subject_num`,
    `attend_long_lesson_num_14d`,
    `attend_finish_lesson_num_14d`,
    `total_voice_chat_duration`,
    `consolidation_exam_s_num`,
    `homework_tid_first_right_cnt`,
    `homework_tid_first_correct_cnt`,
    `subject_num_semester`,
    `leap_user_type`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_user_delamination_v1
WHERE
    `hg_binlog_event_type` IN (5, 7);