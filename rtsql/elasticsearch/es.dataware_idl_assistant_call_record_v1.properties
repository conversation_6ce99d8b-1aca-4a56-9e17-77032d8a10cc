-- 外呼记录：
CREATE TABLE binlog_dataware_idl_assistant_call_record (
    BINLOG_NAME VARCHAR,
    BINLOG_POS BIGINT,
    `DATABASE` VARCHAR,
    `TABLE` VARCHAR,
    `G<PERSON><PERSON>BAL_ID` VARCHAR,
    `NEW_VALUES` ROW<`call_id` BIGINT, `from_uid` BIGINT, `to_uid` BIGINT, `course_id` BIGINT,`lesson_id` BIGINT, `source_type` BIGINT, `line` BIGINT, `call_mode` BIGINT, `device_uid` BIGINT, `person_uid` BIGINT, `duration` BIGINT, `call_result` BIGINT, `business_type` STRING, `business_key` STRING, `create_time` BIGINT>,
    `OLD_VALUES` ROW<`call_id` BIGINT, `from_uid` BIGINT, `to_uid` BIGINT, `course_id` BIGINT,`lesson_id` BIGINT, `source_type` BIGINT, `line` BIGINT, `call_mode` BIGINT, `device_uid` BIGINT, `person_uid` BIGINT, `duration` BIGINT, `call_result` BIGINT, `business_type` STRING, `business_key` STRING, `create_time` BIGINT>,
    `TYPE` VARCHAR,
    `TIME` BIGINT
) WITH (
    'connector' = 'kafka',
    'topic' = '512_homework_fudao_tblAssistantCallRecord',
    'properties.bootstrap.servers' = '172.29.148.69:9092',
    'properties.group.id' = '${kafka.consumer.groupid}',
    'scan.startup.mode' = 'group-offsets',
    'format' = 'json'
);

CREATE TABLE es_tbl_dataware_idl_assistant_call_record (
    `call_id` BIGINT, -- 外呼ID
    `from_uid` BIGINT, -- 主叫方ID
    `to_uid` BIGINT, -- 被叫方ID
    `course_id` BIGINT, -- 课程ID
    `lesson_id` BIGINT, -- 章节ID
    `source_type` BIGINT, -- 通话来源类型
    `line` BIGINT, -- 业务线 1 LPC, 2 辅导
    `call_mode` BIGINT, -- 外呼类型 9: 帮帮盾, 10: ivr, 11: sip外呼
    `device_uid` BIGINT, -- 资产ID
    `person_uid` BIGINT, -- 真人ID
    `duration` BIGINT, -- 通话时长
    `call_result` BIGINT, -- 通话结果 1-正在呼叫 2-呼叫正常结束（已接通） 3-呼叫异常结束（未接通）
    `business_type` STRING, -- 业务唯一标识类型
    `business_key` STRING, -- 业务唯一标识
    `create_time` BIGINT, -- 外呼时间
    PRIMARY KEY (
        `call_id`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.fwyywechatmessagedb.hosts}',
    'index' = 'dataware_idl_assistant_call_record_v2',
    'username' = '${es.fwyywechatmessagedb.username}',
    'password' = '${es.fwyywechatmessagedb.password}'
);

CREATE TEMPORARY VIEW view_binlog_dataware_idl_assistant_call_record AS
SELECT
    CASE
        WHEN `TYPE` = 'I' THEN NEW_VALUES
        WHEN `TYPE` = 'U' THEN NEW_VALUES
        WHEN `TYPE` = 'D' THEN OLD_VALUES
        ELSE NEW_VALUES
    END AS binlog_data,
    `TYPE` AS op_type,
    `TIME` AS binlog_time,
    CASE
        WHEN `TYPE` = 'I' THEN 0
        WHEN `TYPE` = 'U' THEN 0
        WHEN `TYPE` = 'D' THEN 1
        ELSE 0
    END AS zbk_delete_flag -- 根据binlog操作类型，标识数据是否有效
FROM
    binlog_dataware_idl_assistant_call_record;

CREATE TEMPORARY VIEW view_source_dataware_idl_assistant_call_record AS
SELECT
    CAST(binlog_data.`call_id` AS BIGINT) AS `call_id`,
    CAST(binlog_data.`from_uid` AS BIGINT) AS `from_uid`,
    CAST(binlog_data.`to_uid` AS BIGINT) AS `to_uid`,
    CAST(binlog_data.`course_id` AS BIGINT) AS `course_id`,
    CAST(binlog_data.`lesson_id` AS BIGINT) AS `lesson_id`,
    CAST(binlog_data.`source_type` AS BIGINT) AS `source_type`,
    CAST(binlog_data.`line` AS BIGINT) AS `line`,
    CAST(binlog_data.`call_mode` AS BIGINT) AS `call_mode`,
    CAST(binlog_data.`device_uid` AS BIGINT) AS `device_uid`,
    CAST(binlog_data.`person_uid` AS BIGINT) AS `person_uid`,
    CAST(binlog_data.`duration` AS BIGINT) AS `duration`,
    CAST(binlog_data.`call_result` AS BIGINT) AS `call_result`,
    CAST(binlog_data.`business_type` AS STRING) AS `business_type`,
    CAST(binlog_data.`business_key` AS STRING) AS `business_key`,
    CAST(binlog_data.`create_time` AS BIGINT) AS `create_time`,
    zbk_delete_flag
FROM
    view_binlog_dataware_idl_assistant_call_record;

INSERT INTO
    es_tbl_dataware_idl_assistant_call_record
SELECT
    `call_id`,
    `from_uid`,
    `to_uid`,
    `course_id`,
    `lesson_id`,
    `source_type`,
    `line`,
    `call_mode`,
    `device_uid`,
    `person_uid`,
    `duration`,
    `call_result`,
    `business_type`,
    `business_key`,
    `create_time`
FROM
    view_source_dataware_idl_assistant_call_record
WHERE `call_id` > 0;




