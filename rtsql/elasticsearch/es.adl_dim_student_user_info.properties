CREATE TABLE zyb_zbk_deer_dws_dws_zbk_user_deer_agg (
    `hg_binlog_event_type` BIGINT,
    `student_uid` BIGINT,
    `deer_fingering_l2_directory_finish_cnt` BIGINT,
    `deer_fingering_star_finish_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_deer_dws.dws_zbk_user_deer_agg',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_dim_student_user_info (
    `student_uid` BIGINT,
    `deer_fingering_l2_directory_finish_cnt` BIGINT,
    `deer_fingering_star_finish_cnt` BIGINT,
    zbk_update_time BIGINT,
    PRIMARY KEY (student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_dim_student_user_info_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_dim_student_user_info
SELECT
    `student_uid`,
    `deer_fingering_l2_directory_finish_cnt`,
    `deer_fingering_star_finish_cnt`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_deer_dws_dws_zbk_user_deer_agg
WHERE
    hg_binlog_event_type IN(5, 7);