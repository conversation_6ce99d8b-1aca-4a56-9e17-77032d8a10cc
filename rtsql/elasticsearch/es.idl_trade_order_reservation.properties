-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_reservation (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `create_time` BIGINT,
    `pre_id` BIGINT,
    `student_uid` BIGINT,
    `year` BIGINT,
    `season` BIGINT,
    `learn_season` BIGINT,
    `grade_id` BIGINT,
    `subject_id` BIGINT,
    `reserve_status` BIGINT,
    `reserve_year` BIGINT,
    `reserve_season` BIGINT,
    `reserve_learn_season` BIGINT,
    `reserve_grade_period_id` BIGINT,
    `reserve_grade_id` BIGINT,
    `reserve_subject_id` BIGINT,
    `act_year` STRING,
    `act_season` STRING,
    `act_learn_season` STRING,
    `act_grade_period_id` STRING,
    `act_grade_id` STRING,
    `act_subject_id` STRING,
    `reserve_sku_id` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_idl_trade_order_reservation',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '1000',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_reservation (
    `course_id` BIGINT,
    `create_time` BIGINT,
    `pre_id` BIGINT,
    `student_uid` BIGINT,
    `year` BIGINT,
    `season` BIGINT,
    `learn_season` BIGINT,
    `grade_id` BIGINT,
    `subject_id` BIGINT,
    `reserve_status` BIGINT,
    `reserve_year` BIGINT,
    `reserve_season` BIGINT,
    `reserve_learn_season` BIGINT,
    `reserve_grade_period_id` BIGINT,
    `reserve_grade_id` BIGINT,
    `reserve_subject_id` BIGINT,
    `act_year` STRING,
    `act_season` STRING,
    `act_learn_season` STRING,
    `act_grade_period_id` STRING,
    `act_grade_id` STRING,
    `act_subject_id` STRING,
    `reserve_sku_id` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`course_id`, `student_uid`, `pre_id`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'idl_trade_order_reservation_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_idl_trade_order_reservation
SELECT
    `course_id`,
    `create_time`,
    `pre_id`,
    `student_uid`,
    `year`,
    `season`,
    `learn_season`,
    `grade_id`,
    `subject_id`,
    `reserve_status`,
    `reserve_year`,
    `reserve_season`,
    `reserve_learn_season`,
    `reserve_grade_period_id`,
    `reserve_grade_id`,
    `reserve_subject_id`,
    `act_year`,
    `act_season`,
    `act_learn_season`,
    `act_grade_period_id`,
    `act_grade_id`,
    `act_subject_id`,
    `reserve_sku_id`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_idl_trade_order_reservation
WHERE
    `hg_binlog_event_type` <> 3;