CREATE TABLE zyb_zbk_bzr_dws_dws_zbk_attendance_lu_bzr  (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `enterprise_wechat_course_attend_status` BIGINT,
    `enterprise_wechat_course_appointment_status` BIGINT,
    `enterprise_wechat_course_finish_status`   BIGINT,
    `is_assistantcourse_need_attend`   BIGINT,
    `assistantcourse_appointment_type`   BIGINT,
    `assistantcourse_appointment_course_num`   BIGINT,
    `assistantcourse_appointment_course_detail`   STRING,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_attendance_lu_bzr',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE es_dataware_idl_qw_assistant_lesson_student (
   `lesson_id` BIGINT,
   `student_uid` BIGINT,
   `enterprise_wechat_course_attend_status` BIGINT,
   `enterprise_wechat_course_appointment_status` BIGINT,
   `enterprise_wechat_course_finish_status`   BIGINT,
   `is_assistantcourse_need_attend`   BIGINT,
   `assistantcourse_appointment_type`   BIGINT,
   `assistantcourse_appointment_course_num`   BIGINT,
   `assistantcourse_appointment_course_detail` STRING,
   `zbk_update_time` BIGINT,
    PRIMARY KEY (lesson_id,student_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_qw_assistant_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    es_dataware_idl_qw_assistant_lesson_student
SELECT
   `lesson_id`,
   `student_uid`,
   `enterprise_wechat_course_attend_status` ,
   `enterprise_wechat_course_appointment_status`,
   `enterprise_wechat_course_finish_status`,
   `is_assistantcourse_need_attend`,
   `assistantcourse_appointment_type`,
   `assistantcourse_appointment_course_num`,
   `assistantcourse_appointment_course_detail`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_bzr_dws_dws_zbk_attendance_lu_bzr
WHERE
    hg_binlog_event_type IN(5, 7);