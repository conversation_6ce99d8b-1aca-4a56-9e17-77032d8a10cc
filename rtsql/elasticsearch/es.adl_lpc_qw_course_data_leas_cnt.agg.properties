CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_baseinfo_ca_agg_business_lpc_leas_cnt   (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `business_uid` BIGINT,
    `leads_cnt` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_baseinfo_ca_agg_business_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE es_adl_lpc_qw_course_data_agg_leas_cnt (
  `course_id` BIGINT,
  `business_uid` BIGINT,
  `leads_cnt` BIGINT,
  `zbk_update_time` BIGINT,
    PRIMARY KEY (course_id,business_uid) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_lpc_qw_course_data_agg_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    es_adl_lpc_qw_course_data_agg_leas_cnt
SELECT
    `course_id`,
    `business_uid`,
    `leads_cnt` ,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_baseinfo_ca_agg_business_lpc_leas_cnt
WHERE
    hg_binlog_event_type IN(5, 7);