-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_exam_lu_rowtocolumn_bzr (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `exam60` STRING,
    `precise_first_correct_cnt` BIGINT,
    `precise_first_right_cnt` BIGINT,
    `precise_first_submit_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_lu_rowtocolumn_bzr',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_exam_lu_rowtocolumn_bzr (
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `exam60` STRING,
    `precise_first_correct_cnt` BIGINT,
    `precise_first_right_cnt` BIGINT,
    `precise_first_submit_time` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `lesson_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_exam_lu_rowtocolumn_bzr
SELECT
    `lesson_id`,
    `student_uid`,
    `exam60`,
    `precise_first_correct_cnt`,
    `precise_first_right_cnt`,
    `precise_first_submit_time`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_exam_lu_rowtocolumn_bzr
WHERE
    `hg_binlog_event_type` IN (5, 7);