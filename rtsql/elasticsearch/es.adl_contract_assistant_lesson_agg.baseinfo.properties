CREATE TABLE zyb_zbk_baseinfo_contract_lesson_assistant_agg_bzr  (
    `hg_binlog_event_type` BIGINT,
    `contract_id` BIGINT,
    `assistant_uid` BIGINT,
    `lesson_id` BIGINT,
    `save_time` BIGINT,
    `reg_num`   BIGINT,
    `content_view_10s_student_num` BIGINT,
    `content_view_finish_85percent_student_num` BIGINT,
    `overall_finish_student_num` BIGINT,
    `attend_5minute_student_num` BIGINT,
    `attend_finish_student_num` BIGINT,
    `content_view_5minute_student_num` BIGINT,
    `content_view_finish_three_four_student_num` BIGINT,
    `submit_lesson_work_student_num` BIGINT,
    `ai_consolidation_exercise_submission_cnt` BIGINT,
    `ai_report_generation_cnt` BIGINT,
    `live_consolidation_exercise_submission_cnt` BIGINT,
    `live_report_generation_cnt` BIGINT,
    `sphd_consolidation_exercise_submission_cnt` BIGINT,
    `sphd_report_generation_cnt` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_contract_lesson_assistant_agg_bzr',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);
CREATE TABLE adl_baseinfo_contract_lesson_assistant_agg_bzr (
    `contract_id` BIGINT,
    `assistant_uid` BIGINT,
    `lesson_id` BIGINT,
    `save_time` BIGINT,
    `reg_num`   BIGINT,
    `content_view_10s_student_num` BIGINT,
    `content_view_finish_85percent_student_num` BIGINT,
    `overall_finish_student_num` BIGINT,
    `attend_5minute_student_num` BIGINT,
    `attend_finish_student_num` BIGINT,
    `content_view_5minute_student_num` BIGINT,
    `content_view_finish_three_four_student_num` BIGINT,
    `submit_lesson_work_student_num` BIGINT,
    `ai_consolidation_exercise_submission_cnt` BIGINT,
    `ai_report_generation_cnt` BIGINT,
    `live_consolidation_exercise_submission_cnt` BIGINT,
    `live_report_generation_cnt` BIGINT,
    `sphd_consolidation_exercise_submission_cnt` BIGINT,
    `sphd_report_generation_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (contract_id,assistant_uid,lesson_id,save_time) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'adl_contract_lesson_assistant_agg_bzr_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);
INSERT INTO
    adl_baseinfo_contract_lesson_assistant_agg_bzr
SELECT
    `contract_id`,
    `assistant_uid`,
    `lesson_id`,
    `save_time`,
    `reg_num`,
    `content_view_10s_student_num`,
    `content_view_finish_85percent_student_num`,
    `overall_finish_student_num`,
    `attend_5minute_student_num`,
    `attend_finish_student_num`,
    `content_view_5minute_student_num`,
    `content_view_finish_three_four_student_num`,
    `submit_lesson_work_student_num`,
    `ai_consolidation_exercise_submission_cnt`,
    `ai_report_generation_cnt`,
    `live_consolidation_exercise_submission_cnt`,
    `live_report_generation_cnt`,
    `sphd_consolidation_exercise_submission_cnt`,
    `sphd_report_generation_cnt`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_baseinfo_contract_lesson_assistant_agg_bzr
WHERE
    hg_binlog_event_type IN(5, 7);