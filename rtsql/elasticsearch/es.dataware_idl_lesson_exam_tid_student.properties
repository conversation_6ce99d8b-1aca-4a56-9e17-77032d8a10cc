-- 声明用到的所有表
CREATE TABLE source_dwd_zbk_exam_student_answer_tid (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `exam_id` BIGINT,
    `student_uid` BIGINT,
    `tid` BIGINT,
    `tid_idx` BIGINT,
    `correct_result` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dwd.dwd_zbk_exam_student_answer_tid',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_dwd_zbk_exam_student_answer_tid (
     `lesson_id` BIGINT,
     `exam_id` BIGINT,
     `student_uid` BIGINT,
     `tid` BIGINT,
     `tid_idx` BIGINT,
     `correct_result` BIGINT,
     `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `lesson_id`,
        `exam_id`,
        `student_uid`,
        `tid`,
        `tid_idx`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_lesson_exam_tid_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_dwd_zbk_exam_student_answer_tid
SELECT
    `lesson_id`,
    `exam_id`,
    `student_uid`,
    `tid`,
    `tid_idx`,
    `correct_result`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_dwd_zbk_exam_student_answer_tid
WHERE
    `hg_binlog_event_type` IN (5, 7);