-- 在线数据引擎用扩科配置表
CREATE TABLE source_zyb_zbk_bzr_dim_dim_zbk_dim_grade_season_tag (
    `hg_binlog_event_type` BIGINT,
    `new_course_type` BIGINT,
    `grade_id` BIGINT,
    `learn_season` BIGINT,
    `learn_year` BIGINT,
    `season` BIGINT,
    `season_tag` STRING,
    `status` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dim.dim_zbk_dim_grade_season_tag_v2',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dim_zbk_dim_grade_season_tag (
    `new_course_type` BIGINT,
    `grade_id` BIGINT,
    `learn_season` BIGINT,
    `learn_year` BIGINT,
    `season` BIGINT,
    `season_tag` STRING,
    `status` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`learn_year`, `season`, `learn_season`, `grade_id`, `new_course_type`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.dataengine.hosts}',
    'index' = 'dataengine_dim_zbk_dim_grade_season_tag_v2',
    'username' = '${es.dataengine.username}',
    'password' = '${es.dataengine.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dim_zbk_dim_grade_season_tag
SELECT
    `new_course_type`,
    `grade_id`,
    `learn_season`,
    `learn_year`,
    `season`,
    `season_tag`,
    `status`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dim_dim_zbk_dim_grade_season_tag
WHERE
    `hg_binlog_event_type` IN (5, 7);