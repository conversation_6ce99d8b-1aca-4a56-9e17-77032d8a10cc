-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_ads_base_ads_zbk_adl_student_day_lesson_num (
    `student_uid` BIGINT,
    `c_start_date` BIGINT,
    `live_lesson_num` BIGINT,
    `live_lesson_detail` STRING,
    `is_deleted` BIGINT,
    `update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_ads_base.ads_zbk_student_day_lesson_num',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'cdcMode' = 'true',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_ads_zbk_adl_student_day_lesson_num (
    `student_uid` BIGINT,
    `c_start_date` BIGINT,
    `live_lesson_num` BIGINT,
    `live_lesson_detail` STRING,
    `is_deleted` BIGINT,
    `update_time` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (student_uid, c_start_date) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'index' = 'adl_student_day_lesson_num_v1',
    'hosts' = '${es.workbench.hosts}',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_ads_zbk_adl_student_day_lesson_num
SELECT
    `student_uid`,
    `c_start_date`,
    `live_lesson_num`,
    `live_lesson_detail`,
    `is_deleted`,
    `update_time`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    source_zyb_zbk_bzr_ads_base_ads_zbk_adl_student_day_lesson_num;