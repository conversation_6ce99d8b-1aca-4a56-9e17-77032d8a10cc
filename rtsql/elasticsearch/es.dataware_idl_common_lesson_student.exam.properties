-- 声明用到的所有表
CREATE TABLE source_zyb_zbk_bzr_dws_dws_zbk_exam_lu_rowtocolumn_common (
    `hg_binlog_event_type` BIGINT,
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `exam1` STRING,
    `exam10` STRING,
    `exam11` STRING,
    `exam13` STRING,
    `exam14` STRING,
    `exam15` STRING,
    `exam17` STRING,
    `exam18` STRING,
    `exam19` STRING,
    `exam21` STRING,
    `exam211` STRING,
    `exam212` STRING,
    `exam213` STRING,
    `exam216` STRING,
    `exam217` STRING,
    `exam23` STRING,
    `exam32` STRING,
    `exam33` STRING,
    `exam34` STRING,
    `exam48` STRING,
    `exam49` STRING,
    `exam5` STRING,
    `exam50` STRING,
    `exam51` STRING,
    `exam52` STRING,
    `exam7` STRING,
    `exam9` STRING,
    `new_course_type` BIGINT,
    `year_season` STRING,
    `exam58` STRING,
    `exam61` STRING,
    `exam62` STRING,
    `agg_open_mouth_cnt` BIGINT
) WITH (
    'connector' = 'hologres',
    'endpoint' = '${hologres.endpoint}:${hologres.port}',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_bzr_dws.dws_zbk_exam_lu_rowtocolumn_common',
    'cdcMode' = 'false',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true'
);

CREATE TABLE dest_zyb_zbk_bzr_ads_dws_zbk_exam_lu_rowtocolumn_common (
    `lesson_id` BIGINT,
    `student_uid` BIGINT,
    `course_id` BIGINT,
    `exam1` STRING,
    `exam10` STRING,
    `exam11` STRING,
    `exam13` STRING,
    `exam14` STRING,
    `exam15` STRING,
    `exam17` STRING,
    `exam18` STRING,
    `exam19` STRING,
    `exam21` STRING,
    `exam211` STRING,
    `exam212` STRING,
    `exam213` STRING,
    `exam216` STRING,
    `exam217` STRING,
    `exam23` STRING,
    `exam32` STRING,
    `exam33` STRING,
    `exam34` STRING,
    `exam48` STRING,
    `exam49` STRING,
    `exam5` STRING,
    `exam50` STRING,
    `exam51` STRING,
    `exam52` STRING,
    `exam7` STRING,
    `exam9` STRING,
    `new_course_type` BIGINT,
    `year_season` STRING,
    `exam58` STRING,
    `exam61` STRING,
    `exam62` STRING,
    `agg_open_mouth_cnt` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (
        `lesson_id`,
        `student_uid`
    ) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_common_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    dest_zyb_zbk_bzr_ads_dws_zbk_exam_lu_rowtocolumn_common
SELECT
    `lesson_id`,
    `student_uid`,
    `course_id`,
    `exam1`,
    `exam10`,
    `exam11`,
    `exam13`,
    `exam14`,
    `exam15`,
    `exam17`,
    `exam18`,
    `exam19`,
    `exam21`,
    `exam211`,
    `exam212`,
    `exam213`,
    `exam216`,
    `exam217`,
    `exam23`,
    `exam32`,
    `exam33`,
    `exam34`,
    `exam48`,
    `exam49`,
    `exam5`,
    `exam50`,
    `exam51`,
    `exam52`,
    `exam7`,
    `exam9`,
    `new_course_type`,
    `year_season`,
    `exam58`,
    `exam61`,
    `exam62`,
    `agg_open_mouth_cnt`,
    UNIX_TIMESTAMP() AS `zbk_update_time`
FROM
    source_zyb_zbk_bzr_dws_dws_zbk_exam_lu_rowtocolumn_common
WHERE
    `hg_binlog_event_type` IN (5, 7);