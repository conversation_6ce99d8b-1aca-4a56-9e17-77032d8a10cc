CREATE TABLE zyb_zbk_lpc_dws_dws_zbk_sop_leads_lesson_lpc  (
    `hg_binlog_event_type` BIGINT,
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `leads_id`  BIGINT,
    `student_uid` BIGINT,
    `lesson_start_time` BIGINT,
    `lesson_stop_time` BIGINT,
    `bluetooth_last_connect_status` BIGINT,
    `bluetooth_last_connect_timestamp` BIGINT,
    `bluetooth_first_connect_suc` BIGINT,
    `is_bluetooth_connect_suc` BIGINT,
    `is_bluetooth_before_class_connect_suc` BIGINT,
    `is_bluetooth_during_class_connect_suc` BIGINT,
    `sensor_last_check_status` BIGINT,
    `sensor_last_check_timestamp` BIGINT,
    `sensor_first_check_suc` BIGINT,
    `is_sensor_check_suc` BIGINT,
    `is_sensor_before_class_check_suc` BIGINT,
    `is_sensor_during_class_check_suc` BIGINT,
    `zbk_update_time` BIGINT
) WITH (
    'connector' = 'hologres',
    'dbname' = 'zyb_zbk_bzr',
    'tablename' = 'zyb_zbk_lpc_dws.dws_zbk_sop_leads_lesson_lpc',
    'username' = '${hologres.username}',
    'password' = '${hologres.password}',
    'binlogMaxRetryTimes' = '10',
    'binlogRetryIntervalMs' = '500',
    'binlogBatchReadSize' = '100',
    'binlog' = 'true',
    'cdcMode' = 'false',
    'endpoint' = '${hologres.endpoint}:${hologres.port}'
);

CREATE TABLE es_dataware_idl_sop_course_lesson_student (
    `course_id` BIGINT,
    `lesson_id` BIGINT,
    `leads_id`  BIGINT,
    `student_uid` BIGINT,
    `lesson_start_time` BIGINT,
    `lesson_stop_time` BIGINT,
    `bluetooth_last_connect_status` BIGINT,
    `bluetooth_last_connect_timestamp` BIGINT,
    `bluetooth_first_connect_suc` BIGINT,
    `is_bluetooth_connect_suc` BIGINT,
    `is_bluetooth_before_class_connect_suc` BIGINT,
    `is_bluetooth_during_class_connect_suc` BIGINT,
    `sensor_last_check_status` BIGINT,
    `sensor_last_check_timestamp` BIGINT,
    `sensor_first_check_suc` BIGINT,
    `is_sensor_check_suc` BIGINT,
    `is_sensor_before_class_check_suc` BIGINT,
    `is_sensor_during_class_check_suc` BIGINT,
    `zbk_update_time` BIGINT,
    PRIMARY KEY (`lesson_id`,`leads_id`) NOT ENFORCED
) WITH (
    'connector' = 'elasticsearch-7',
    'hosts' = '${es.workbench.hosts}',
    'index' = 'dataware_idl_dws_zbk_sop_course_lesson_student_v1',
    'username' = '${es.workbench.username}',
    'password' = '${es.workbench.password}'
);

INSERT INTO
    es_dataware_idl_sop_course_lesson_student
SELECT
   `course_id`,
   `lesson_id`,
   `leads_id`,
   `student_uid`,
   `lesson_start_time`,
   `lesson_stop_time`,
   `bluetooth_last_connect_status`,
   `bluetooth_last_connect_timestamp`,
   `bluetooth_first_connect_suc`,
   `is_bluetooth_connect_suc`,
   `is_bluetooth_before_class_connect_suc`,
   `is_bluetooth_during_class_connect_suc`,
   `sensor_last_check_status`,
   `sensor_last_check_timestamp`,
   `sensor_first_check_suc`,
   `is_sensor_check_suc`,
   `is_sensor_before_class_check_suc`,
   `is_sensor_during_class_check_suc`,
    UNIX_TIMESTAMP() AS zbk_update_time
FROM
    zyb_zbk_lpc_dws_dws_zbk_sop_leads_lesson_lpc
WHERE
    hg_binlog_event_type IN(5, 7);
