#!/bin/sh
# https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=309378712
# 命令失败，结束 shell script 进程  必写
set -e
java_version="1.8.0_251"  #java语言版本，必填，不写则直接编译失败。目前仅支持1.8.0_251版本，若有特殊版本需求，请在build.sh里标注，并联系@吴艳丽
maven_version="bigdata_3.6.3"  #maven的语言版本，必填，不填机器没有mvn命令
# 传入参数表示打包flame/cola
APP_NAME=$*

readonly ROOT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
readonly OUTPUT_DIR=$ROOT_DIR/output
readonly OUTPUT_SQL_DIR=$OUTPUT_DIR/conf
readonly OUTPUT_JAR_DIR=$OUTPUT_DIR/bin

rm -rf $OUTPUT_DIR
mkdir -p $OUTPUT_SQL_DIR
mkdir -p $OUTPUT_JAR_DIR

## 编译
echo "开始编译"
cd $ROOT_DIR/udf && mvn clean package -U assembly:single
echo "编译成功"

cd $ROOT_DIR
# rtsql
for file in $(find rtsql -type f -name "*.properties")
do
    if [ -e "${SQL_OUTPUT_DIR}/${file}" ];then
        echo "${file}文件已存在，请删除多余文件"
        exit -1
    else
        cp $file $OUTPUT_SQL_DIR
    fi
done

# 替换变量
sh scripts/replace_zlink_properties_variables.sh

# jar包
find . -type f -name "*.jar" -exec cp -f {} $OUTPUT_JAR_DIR \;

# 改为打包构建可以下载产出
# cd $OUTPUT_DIR && tar -czf zlink-tasks.tar.gz conf bin
